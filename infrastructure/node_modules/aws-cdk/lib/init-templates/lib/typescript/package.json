{"name": "%name%", "version": "0.1.0", "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "22.7.9", "aws-cdk-lib": "%cdk-version%", "constructs": "%constructs-version%", "jest": "^29.7.0", "ts-jest": "^29.2.5", "typescript": "~5.6.3"}, "peerDependencies": {"aws-cdk-lib": "%cdk-version%", "constructs": "%constructs-version%"}}