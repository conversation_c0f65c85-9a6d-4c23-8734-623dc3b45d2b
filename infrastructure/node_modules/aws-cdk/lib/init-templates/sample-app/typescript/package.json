{"name": "%name%", "version": "0.1.0", "bin": {"%name%": "bin/%name%.js"}, "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "22.7.9", "jest": "^29.7.0", "ts-jest": "^29.2.5", "aws-cdk": "%cdk-cli-version%", "ts-node": "^10.9.2", "typescript": "~5.6.3"}, "dependencies": {"aws-cdk-lib": "%cdk-version%", "constructs": "%constructs-version%"}}