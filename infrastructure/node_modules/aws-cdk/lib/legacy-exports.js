"use strict";
// This is the legacy symbols export file.
// We export a number of known symbols that are imported by users from the `aws-cdk` package.
// Importing these symbols was never officially supported, but here we are.
// See https://github.com/aws/aws-cdk/pull/33021 for more information.
//
// In package.json, section `exports`, we declare all known subpaths as an explicit subpath export resolving to this file.
// This way existing unsanctioned imports don't break immediately.
//
// When attempting to import a subpath other than the explicitly exported ones, the following runtime error will be thrown:
// Error [ERR_PACKAGE_PATH_NOT_EXPORTED]: Package subpath './lib/private/subpath' is not defined by "exports" in aws-cdk/package.json
//
// TypeScript can warn users about the not-exported subpath at compile time. However it requires a reasonably modern tsconfig.json.
// Specifically `moduleResolution` must be set to either "node16" or "nodenext".
Object.defineProperty(exports, "__esModule", { value: true });
exports.data = exports.print = exports.highlight = exports.success = exports.warning = exports.error = exports.debug = exports.trace = exports.increaseVerbosity = exports.setCI = exports.setLogLevel = exports.CI = exports.logLevel = exports.LogLevel = exports.withCorkedLogging = exports.CfnEvaluationException = exports.cached = exports.availableInitTemplates = exports.versionNumber = exports.rootDir = exports.Deployments = exports.deepMerge = exports.lowerCaseFirstCharacter = exports.describe = exports.command = exports.aliases = exports.enableTracing = exports.formatAsBanner = exports.leftPad = exports.RequireApproval = exports.execProgram = exports.CloudExecutable = exports.Bootstrapper = exports.Settings = exports.PROJECT_CONTEXT = exports.Configuration = exports.Command = exports.contentHash = exports.PluginHost = exports.SdkProvider = exports.exec = exports.cli = exports.deployStack = exports.partition = exports.numberFromBool = exports.isEmpty = exports.isArray = exports.ifDefined = exports.flatten = exports.deepClone = void 0;
exports.prefix = void 0;
// We need to import the legacy exports via index.ts
// This is because we will bundle all code and dependencies into index.js at build time.
// It's the only place where the code exists as a working, self-contained copy.
// While we could have bundled `legacy-exports.ts` separately, it would create an other copy of the pretty much identical bundle
// and add an additional 16mb+ to the published package.
// To avoid this, we deduplicated the bundled code and run everything through index.ts.
const index_1 = require("./index");
// Re-export all symbols via index.js
// We do this, because index.js is the file that will end up with all dependencies bundled
exports.deepClone = index_1.legacy.deepClone, exports.flatten = index_1.legacy.flatten, exports.ifDefined = index_1.legacy.ifDefined, exports.isArray = index_1.legacy.isArray, exports.isEmpty = index_1.legacy.isEmpty, exports.numberFromBool = index_1.legacy.numberFromBool, exports.partition = index_1.legacy.partition, exports.deployStack = index_1.legacy.deployStack, exports.cli = index_1.legacy.cli, exports.exec = index_1.legacy.exec, exports.SdkProvider = index_1.legacy.SdkProvider, exports.PluginHost = index_1.legacy.PluginHost, exports.contentHash = index_1.legacy.contentHash, exports.Command = index_1.legacy.Command, exports.Configuration = index_1.legacy.Configuration, exports.PROJECT_CONTEXT = index_1.legacy.PROJECT_CONTEXT, exports.Settings = index_1.legacy.Settings, exports.Bootstrapper = index_1.legacy.Bootstrapper, exports.CloudExecutable = index_1.legacy.CloudExecutable, exports.execProgram = index_1.legacy.execProgram, exports.RequireApproval = index_1.legacy.RequireApproval, exports.leftPad = index_1.legacy.leftPad, exports.formatAsBanner = index_1.legacy.formatAsBanner, exports.enableTracing = index_1.legacy.enableTracing, exports.aliases = index_1.legacy.aliases, exports.command = index_1.legacy.command, exports.describe = index_1.legacy.describe, exports.lowerCaseFirstCharacter = index_1.legacy.lowerCaseFirstCharacter, exports.deepMerge = index_1.legacy.deepMerge, exports.Deployments = index_1.legacy.Deployments, exports.rootDir = index_1.legacy.rootDir, exports.versionNumber = index_1.legacy.versionNumber, exports.availableInitTemplates = index_1.legacy.availableInitTemplates, exports.cached = index_1.legacy.cached, exports.CfnEvaluationException = index_1.legacy.CfnEvaluationException, exports.withCorkedLogging = index_1.legacy.withCorkedLogging, exports.LogLevel = index_1.legacy.LogLevel, exports.logLevel = index_1.legacy.logLevel, exports.CI = index_1.legacy.CI, exports.setLogLevel = index_1.legacy.setLogLevel, exports.setCI = index_1.legacy.setCI, exports.increaseVerbosity = index_1.legacy.increaseVerbosity, exports.trace = index_1.legacy.trace, exports.debug = index_1.legacy.debug, exports.error = index_1.legacy.error, exports.warning = index_1.legacy.warning, exports.success = index_1.legacy.success, exports.highlight = index_1.legacy.highlight, exports.print = index_1.legacy.print, exports.data = index_1.legacy.data, exports.prefix = index_1.legacy.prefix;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibGVnYWN5LWV4cG9ydHMuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyJsZWdhY3ktZXhwb3J0cy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQUEsMENBQTBDO0FBQzFDLDZGQUE2RjtBQUM3RiwyRUFBMkU7QUFDM0Usc0VBQXNFO0FBQ3RFLEVBQUU7QUFDRiwwSEFBMEg7QUFDMUgsa0VBQWtFO0FBQ2xFLEVBQUU7QUFDRiwySEFBMkg7QUFDM0gscUlBQXFJO0FBQ3JJLEVBQUU7QUFDRixtSUFBbUk7QUFDbkksZ0ZBQWdGOzs7O0FBRWhGLG9EQUFvRDtBQUNwRCx3RkFBd0Y7QUFDeEYsK0VBQStFO0FBQy9FLGdJQUFnSTtBQUNoSSx3REFBd0Q7QUFDeEQsdUZBQXVGO0FBQ3ZGLG1DQUFpQztBQVlqQyxxQ0FBcUM7QUFDckMsMEZBQTBGO0FBRXhGLGlCQUFTLEdBbURQLGNBQU0sWUFsRFIsZUFBTyxHQWtETCxjQUFNLFVBakRSLGlCQUFTLEdBaURQLGNBQU0sWUFoRFIsZUFBTyxHQWdETCxjQUFNLFVBL0NSLGVBQU8sR0ErQ0wsY0FBTSxVQTlDUixzQkFBYyxHQThDWixjQUFNLGlCQTdDUixpQkFBUyxHQTZDUCxjQUFNLFlBNUNSLG1CQUFXLEdBNENULGNBQU0sY0EzQ1IsV0FBRyxHQTJDRCxjQUFNLE1BMUNSLFlBQUksR0EwQ0YsY0FBTSxPQXpDUixtQkFBVyxHQXlDVCxjQUFNLGNBeENSLGtCQUFVLEdBd0NSLGNBQU0sYUF2Q1IsbUJBQVcsR0F1Q1QsY0FBTSxjQXRDUixlQUFPLEdBc0NMLGNBQU0sVUFyQ1IscUJBQWEsR0FxQ1gsY0FBTSxnQkFwQ1IsdUJBQWUsR0FvQ2IsY0FBTSxrQkFuQ1IsZ0JBQVEsR0FtQ04sY0FBTSxXQWxDUixvQkFBWSxHQWtDVixjQUFNLGVBakNSLHVCQUFlLEdBaUNiLGNBQU0sa0JBaENSLG1CQUFXLEdBZ0NULGNBQU0sY0EvQlIsdUJBQWUsR0ErQmIsY0FBTSxrQkE5QlIsZUFBTyxHQThCTCxjQUFNLFVBN0JSLHNCQUFjLEdBNkJaLGNBQU0saUJBNUJSLHFCQUFhLEdBNEJYLGNBQU0sZ0JBM0JSLGVBQU8sR0EyQkwsY0FBTSxVQTFCUixlQUFPLEdBMEJMLGNBQU0sVUF6QlIsZ0JBQVEsR0F5Qk4sY0FBTSxXQXhCUiwrQkFBdUIsR0F3QnJCLGNBQU0sMEJBdkJSLGlCQUFTLEdBdUJQLGNBQU0sWUF0QlIsbUJBQVcsR0FzQlQsY0FBTSxjQXJCUixlQUFPLEdBcUJMLGNBQU0sVUFwQlIscUJBQWEsR0FvQlgsY0FBTSxnQkFuQlIsOEJBQXNCLEdBbUJwQixjQUFNLHlCQWxCUixjQUFNLEdBa0JKLGNBQU0sU0FqQlIsOEJBQXNCLEdBaUJwQixjQUFNLHlCQWhCUix5QkFBaUIsR0FnQmYsY0FBTSxvQkFmUixnQkFBUSxHQWVOLGNBQU0sV0FkUixnQkFBUSxHQWNOLGNBQU0sV0FiUixVQUFFLEdBYUEsY0FBTSxLQVpSLG1CQUFXLEdBWVQsY0FBTSxjQVhSLGFBQUssR0FXSCxjQUFNLFFBVlIseUJBQWlCLEdBVWYsY0FBTSxvQkFUUixhQUFLLEdBU0gsY0FBTSxRQVJSLGFBQUssR0FRSCxjQUFNLFFBUFIsYUFBSyxHQU9ILGNBQU0sUUFOUixlQUFPLEdBTUwsY0FBTSxVQUxSLGVBQU8sR0FLTCxjQUFNLFVBSlIsaUJBQVMsR0FJUCxjQUFNLFlBSFIsYUFBSyxHQUdILGNBQU0sUUFGUixZQUFJLEdBRUYsY0FBTSxPQURSLGNBQU0sR0FDSixjQUFNLFFBQUMiLCJzb3VyY2VzQ29udGVudCI6WyIvLyBUaGlzIGlzIHRoZSBsZWdhY3kgc3ltYm9scyBleHBvcnQgZmlsZS5cbi8vIFdlIGV4cG9ydCBhIG51bWJlciBvZiBrbm93biBzeW1ib2xzIHRoYXQgYXJlIGltcG9ydGVkIGJ5IHVzZXJzIGZyb20gdGhlIGBhd3MtY2RrYCBwYWNrYWdlLlxuLy8gSW1wb3J0aW5nIHRoZXNlIHN5bWJvbHMgd2FzIG5ldmVyIG9mZmljaWFsbHkgc3VwcG9ydGVkLCBidXQgaGVyZSB3ZSBhcmUuXG4vLyBTZWUgaHR0cHM6Ly9naXRodWIuY29tL2F3cy9hd3MtY2RrL3B1bGwvMzMwMjEgZm9yIG1vcmUgaW5mb3JtYXRpb24uXG4vL1xuLy8gSW4gcGFja2FnZS5qc29uLCBzZWN0aW9uIGBleHBvcnRzYCwgd2UgZGVjbGFyZSBhbGwga25vd24gc3VicGF0aHMgYXMgYW4gZXhwbGljaXQgc3VicGF0aCBleHBvcnQgcmVzb2x2aW5nIHRvIHRoaXMgZmlsZS5cbi8vIFRoaXMgd2F5IGV4aXN0aW5nIHVuc2FuY3Rpb25lZCBpbXBvcnRzIGRvbid0IGJyZWFrIGltbWVkaWF0ZWx5LlxuLy9cbi8vIFdoZW4gYXR0ZW1wdGluZyB0byBpbXBvcnQgYSBzdWJwYXRoIG90aGVyIHRoYW4gdGhlIGV4cGxpY2l0bHkgZXhwb3J0ZWQgb25lcywgdGhlIGZvbGxvd2luZyBydW50aW1lIGVycm9yIHdpbGwgYmUgdGhyb3duOlxuLy8gRXJyb3IgW0VSUl9QQUNLQUdFX1BBVEhfTk9UX0VYUE9SVEVEXTogUGFja2FnZSBzdWJwYXRoICcuL2xpYi9wcml2YXRlL3N1YnBhdGgnIGlzIG5vdCBkZWZpbmVkIGJ5IFwiZXhwb3J0c1wiIGluIGF3cy1jZGsvcGFja2FnZS5qc29uXG4vL1xuLy8gVHlwZVNjcmlwdCBjYW4gd2FybiB1c2VycyBhYm91dCB0aGUgbm90LWV4cG9ydGVkIHN1YnBhdGggYXQgY29tcGlsZSB0aW1lLiBIb3dldmVyIGl0IHJlcXVpcmVzIGEgcmVhc29uYWJseSBtb2Rlcm4gdHNjb25maWcuanNvbi5cbi8vIFNwZWNpZmljYWxseSBgbW9kdWxlUmVzb2x1dGlvbmAgbXVzdCBiZSBzZXQgdG8gZWl0aGVyIFwibm9kZTE2XCIgb3IgXCJub2RlbmV4dFwiLlxuXG4vLyBXZSBuZWVkIHRvIGltcG9ydCB0aGUgbGVnYWN5IGV4cG9ydHMgdmlhIGluZGV4LnRzXG4vLyBUaGlzIGlzIGJlY2F1c2Ugd2Ugd2lsbCBidW5kbGUgYWxsIGNvZGUgYW5kIGRlcGVuZGVuY2llcyBpbnRvIGluZGV4LmpzIGF0IGJ1aWxkIHRpbWUuXG4vLyBJdCdzIHRoZSBvbmx5IHBsYWNlIHdoZXJlIHRoZSBjb2RlIGV4aXN0cyBhcyBhIHdvcmtpbmcsIHNlbGYtY29udGFpbmVkIGNvcHkuXG4vLyBXaGlsZSB3ZSBjb3VsZCBoYXZlIGJ1bmRsZWQgYGxlZ2FjeS1leHBvcnRzLnRzYCBzZXBhcmF0ZWx5LCBpdCB3b3VsZCBjcmVhdGUgYW4gb3RoZXIgY29weSBvZiB0aGUgcHJldHR5IG11Y2ggaWRlbnRpY2FsIGJ1bmRsZVxuLy8gYW5kIGFkZCBhbiBhZGRpdGlvbmFsIDE2bWIrIHRvIHRoZSBwdWJsaXNoZWQgcGFja2FnZS5cbi8vIFRvIGF2b2lkIHRoaXMsIHdlIGRlZHVwbGljYXRlZCB0aGUgYnVuZGxlZCBjb2RlIGFuZCBydW4gZXZlcnl0aGluZyB0aHJvdWdoIGluZGV4LnRzLlxuaW1wb3J0IHsgbGVnYWN5IH0gZnJvbSAnLi9pbmRleCc7XG5cbi8vIFdlIGFsc28gbmVlZCB0byByZS1leHBvcnQgc29tZSB0eXBlc1xuLy8gVGhlc2UgZG9uJ3QgbmVlZCB0byBwYXJ0aWNpcGF0ZSBpbiB0aGUgYnVuZGxpbmcsIHNvIHdlIGNhbiBqdXN0IHB1dCB0aGVtIGhlcmVcbmV4cG9ydCB0eXBlIHsgQm9vdHN0cmFwRW52aXJvbm1lbnRPcHRpb25zLCBCb290c3RyYXBTb3VyY2UgfSBmcm9tICcuL2FwaS9ib290c3RyYXAnO1xuZXhwb3J0IHR5cGUgeyBEZXBsb3lTdGFja1Jlc3VsdCB9IGZyb20gJy4vYXBpL2RlcGxveW1lbnRzJztcblxuLy8gTGVnYWN5IGNvcGllc1xuZXhwb3J0IHR5cGUgKiBmcm9tICcuL2xlZ2FjeS10eXBlcyc7XG5leHBvcnQgdHlwZSB7IEFjY291bnQsIENvbnRleHRQcm92aWRlclBsdWdpbiB9IGZyb20gJy4vbGVnYWN5LWF3cy1hdXRoJztcbmV4cG9ydCB0eXBlIHsgTG9nZ2VyRnVuY3Rpb24gfSBmcm9tICcuL2xlZ2FjeS1sb2dnaW5nLXNvdXJjZSc7XG5cbi8vIFJlLWV4cG9ydCBhbGwgc3ltYm9scyB2aWEgaW5kZXguanNcbi8vIFdlIGRvIHRoaXMsIGJlY2F1c2UgaW5kZXguanMgaXMgdGhlIGZpbGUgdGhhdCB3aWxsIGVuZCB1cCB3aXRoIGFsbCBkZXBlbmRlbmNpZXMgYnVuZGxlZFxuZXhwb3J0IGNvbnN0IHtcbiAgZGVlcENsb25lLFxuICBmbGF0dGVuLFxuICBpZkRlZmluZWQsXG4gIGlzQXJyYXksXG4gIGlzRW1wdHksXG4gIG51bWJlckZyb21Cb29sLFxuICBwYXJ0aXRpb24sXG4gIGRlcGxveVN0YWNrLFxuICBjbGksXG4gIGV4ZWMsXG4gIFNka1Byb3ZpZGVyLFxuICBQbHVnaW5Ib3N0LFxuICBjb250ZW50SGFzaCxcbiAgQ29tbWFuZCxcbiAgQ29uZmlndXJhdGlvbixcbiAgUFJPSkVDVF9DT05URVhULFxuICBTZXR0aW5ncyxcbiAgQm9vdHN0cmFwcGVyLFxuICBDbG91ZEV4ZWN1dGFibGUsXG4gIGV4ZWNQcm9ncmFtLFxuICBSZXF1aXJlQXBwcm92YWwsXG4gIGxlZnRQYWQsXG4gIGZvcm1hdEFzQmFubmVyLFxuICBlbmFibGVUcmFjaW5nLFxuICBhbGlhc2VzLFxuICBjb21tYW5kLFxuICBkZXNjcmliZSxcbiAgbG93ZXJDYXNlRmlyc3RDaGFyYWN0ZXIsXG4gIGRlZXBNZXJnZSxcbiAgRGVwbG95bWVudHMsXG4gIHJvb3REaXIsXG4gIHZlcnNpb25OdW1iZXIsXG4gIGF2YWlsYWJsZUluaXRUZW1wbGF0ZXMsXG4gIGNhY2hlZCxcbiAgQ2ZuRXZhbHVhdGlvbkV4Y2VwdGlvbixcbiAgd2l0aENvcmtlZExvZ2dpbmcsXG4gIExvZ0xldmVsLFxuICBsb2dMZXZlbCxcbiAgQ0ksXG4gIHNldExvZ0xldmVsLFxuICBzZXRDSSxcbiAgaW5jcmVhc2VWZXJib3NpdHksXG4gIHRyYWNlLFxuICBkZWJ1ZyxcbiAgZXJyb3IsXG4gIHdhcm5pbmcsXG4gIHN1Y2Nlc3MsXG4gIGhpZ2hsaWdodCxcbiAgcHJpbnQsXG4gIGRhdGEsXG4gIHByZWZpeCxcbn0gPSBsZWdhY3k7XG4iXX0=