"use strict";
// This is a barrel export file, of all known symbols that are imported by users from the `aws-cdk` package.
// Importing these symbols was never officially supported, but here we are.
// In order to preserver backwards-compatibly for these users, we re-export and preserve them as explicit subpath exports.
// See https://github.com/aws/aws-cdk/pull/33021 for more information.
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.lowerCaseFirstCharacter = exports.deepMerge = exports.contentHash = exports.leftPad = exports.partition = exports.numberFromBool = exports.isEmpty = exports.isArray = exports.ifDefined = exports.flatten = exports.deepClone = exports.describe = exports.command = exports.aliases = exports.availableInitTemplates = exports.RequireApproval = exports.versionNumber = exports.formatAsBanner = exports.PROJECT_CONTEXT = exports.Configuration = exports.Command = exports.rootDir = exports.exec = exports.cli = exports.execProgram = exports.CloudExecutable = exports.Bootstrapper = exports.Settings = exports.PluginHost = exports.deployStack = exports.Deployments = exports.CfnEvaluationException = void 0;
// API
var cloudformation_1 = require("./api/cloudformation");
Object.defineProperty(exports, "CfnEvaluationException", { enumerable: true, get: function () { return cloudformation_1.CfnEvaluationException; } });
var deployments_1 = require("./api/deployments");
Object.defineProperty(exports, "Deployments", { enumerable: true, get: function () { return deployments_1.Deployments; } });
var api_private_1 = require("./api-private");
Object.defineProperty(exports, "deployStack", { enumerable: true, get: function () { return api_private_1.deployStack; } });
var plugin_1 = require("./api/plugin");
Object.defineProperty(exports, "PluginHost", { enumerable: true, get: function () { return plugin_1.PluginHost; } });
var settings_1 = require("./api/settings");
Object.defineProperty(exports, "Settings", { enumerable: true, get: function () { return settings_1.Settings; } });
var bootstrap_1 = require("./api/bootstrap");
Object.defineProperty(exports, "Bootstrapper", { enumerable: true, get: function () { return bootstrap_1.Bootstrapper; } });
// Note: All type exports are in `legacy-exports.ts`
__exportStar(require("./legacy-logging-source"), exports);
__exportStar(require("./legacy-aws-auth"), exports);
// CLI
var cxapp_1 = require("./cxapp");
Object.defineProperty(exports, "CloudExecutable", { enumerable: true, get: function () { return cxapp_1.CloudExecutable; } });
Object.defineProperty(exports, "execProgram", { enumerable: true, get: function () { return cxapp_1.execProgram; } });
var cli_1 = require("./cli/cli");
Object.defineProperty(exports, "cli", { enumerable: true, get: function () { return cli_1.cli; } });
Object.defineProperty(exports, "exec", { enumerable: true, get: function () { return cli_1.exec; } });
var root_dir_1 = require("./cli/root-dir");
Object.defineProperty(exports, "rootDir", { enumerable: true, get: function () { return root_dir_1.cliRootDir; } });
var user_configuration_1 = require("./cli/user-configuration");
Object.defineProperty(exports, "Command", { enumerable: true, get: function () { return user_configuration_1.Command; } });
Object.defineProperty(exports, "Configuration", { enumerable: true, get: function () { return user_configuration_1.Configuration; } });
Object.defineProperty(exports, "PROJECT_CONTEXT", { enumerable: true, get: function () { return user_configuration_1.PROJECT_CONTEXT; } });
var console_formatters_1 = require("./cli/util/console-formatters");
Object.defineProperty(exports, "formatAsBanner", { enumerable: true, get: function () { return console_formatters_1.formatAsBanner; } });
var version_1 = require("./cli/version");
Object.defineProperty(exports, "versionNumber", { enumerable: true, get: function () { return version_1.versionNumber; } });
// Commands
var cloud_assembly_schema_1 = require("@aws-cdk/cloud-assembly-schema");
Object.defineProperty(exports, "RequireApproval", { enumerable: true, get: function () { return cloud_assembly_schema_1.RequireApproval; } });
var init_1 = require("./commands/init");
Object.defineProperty(exports, "availableInitTemplates", { enumerable: true, get: function () { return init_1.availableInitTemplates; } });
var docs_1 = require("./commands/docs");
Object.defineProperty(exports, "aliases", { enumerable: true, get: function () { return docs_1.aliases; } });
Object.defineProperty(exports, "command", { enumerable: true, get: function () { return docs_1.command; } });
Object.defineProperty(exports, "describe", { enumerable: true, get: function () { return docs_1.describe; } });
// util
var util_1 = require("./util");
Object.defineProperty(exports, "deepClone", { enumerable: true, get: function () { return util_1.deepClone; } });
Object.defineProperty(exports, "flatten", { enumerable: true, get: function () { return util_1.flatten; } });
Object.defineProperty(exports, "ifDefined", { enumerable: true, get: function () { return util_1.ifDefined; } });
Object.defineProperty(exports, "isArray", { enumerable: true, get: function () { return util_1.isArray; } });
Object.defineProperty(exports, "isEmpty", { enumerable: true, get: function () { return util_1.isEmpty; } });
Object.defineProperty(exports, "numberFromBool", { enumerable: true, get: function () { return util_1.numberFromBool; } });
Object.defineProperty(exports, "partition", { enumerable: true, get: function () { return util_1.partition; } });
Object.defineProperty(exports, "leftPad", { enumerable: true, get: function () { return util_1.padLeft; } });
Object.defineProperty(exports, "contentHash", { enumerable: true, get: function () { return util_1.contentHash; } });
Object.defineProperty(exports, "deepMerge", { enumerable: true, get: function () { return util_1.deepMerge; } });
Object.defineProperty(exports, "lowerCaseFirstCharacter", { enumerable: true, get: function () { return util_1.lowerCaseFirstCharacter; } });
//# sourceMappingURL=data:application/json;base64,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