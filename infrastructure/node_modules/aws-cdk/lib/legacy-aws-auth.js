"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SdkProvider = void 0;
exports.cached = cached;
exports.enableTracing = enableTracing;
const aws_auth_1 = require("./api/aws-auth");
const io_host_1 = require("./cli/io-host");
const singleton_plugin_host_1 = require("./cli/singleton-plugin-host");
/**
 * @deprecated
 */
function cached(obj, sym, fn) {
    if (!(sym in obj)) {
        obj[sym] = fn();
    }
    return obj[sym];
}
/**
 * Enable tracing in the CDK
 *
 * @deprecated cannot be enabled from outside the CDK
 */
function enableTracing(_enabled) {
    // noop
}
/**
 * @deprecated
 */
class SdkProvider {
    static async withAwsCliCompatibleDefaults(options = {}) {
        return aws_auth_1.SdkProvider.withAwsCliCompatibleDefaults({
            ...options,
            ioHelper: io_host_1.CliIoHost.instance().asIoHelper(),
            pluginHost: singleton_plugin_host_1.GLOBAL_PLUGIN_HOST,
        }, options.profile);
    }
    constructor(defaultCredentialProvider, defaultRegion, requestHandler = {}, logger) {
        return new aws_auth_1.SdkProvider(defaultCredentialProvider, defaultRegion, {
            pluginHost: singleton_plugin_host_1.GLOBAL_PLUGIN_HOST,
            ioHelper: io_host_1.CliIoHost.instance().asIoHelper(),
            requestHandler: requestHandler, // this is fine it's passed through to the SDK
            logger,
        });
    }
}
exports.SdkProvider = SdkProvider;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibGVnYWN5LWF3cy1hdXRoLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsibGVnYWN5LWF3cy1hdXRoLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7OztBQWdCQSx3QkFLQztBQXVCRCxzQ0FFQztBQXJDRCw2Q0FBMEU7QUFDMUUsMkNBQTBDO0FBQzFDLHVFQUFpRTtBQUVqRTs7R0FFRztBQUNILFNBQWdCLE1BQU0sQ0FBc0IsR0FBTSxFQUFFLEdBQVcsRUFBRSxFQUFXO0lBQzFFLElBQUksQ0FBQyxDQUFDLEdBQUcsSUFBSSxHQUFHLENBQUMsRUFBRSxDQUFDO1FBQ2pCLEdBQVcsQ0FBQyxHQUFHLENBQUMsR0FBRyxFQUFFLEVBQUUsQ0FBQztJQUMzQixDQUFDO0lBQ0QsT0FBUSxHQUFXLENBQUMsR0FBRyxDQUFDLENBQUM7QUFDM0IsQ0FBQztBQWtCRDs7OztHQUlHO0FBQ0gsU0FBZ0IsYUFBYSxDQUFDLFFBQWlCO0lBQzdDLE9BQU87QUFDVCxDQUFDO0FBNkNEOztHQUVHO0FBQ0gsTUFBYSxXQUFXO0lBQ2YsTUFBTSxDQUFDLEtBQUssQ0FBQyw0QkFBNEIsQ0FBQyxVQUE4QixFQUFFO1FBQy9FLE9BQU8sc0JBQXlCLENBQUMsNEJBQTRCLENBQUM7WUFDNUQsR0FBRyxPQUFPO1lBQ1YsUUFBUSxFQUFFLG1CQUFTLENBQUMsUUFBUSxFQUFFLENBQUMsVUFBVSxFQUFFO1lBQzNDLFVBQVUsRUFBRSwwQ0FBa0I7U0FDL0IsRUFBRSxPQUFPLENBQUMsT0FBTyxDQUFDLENBQUM7SUFDdEIsQ0FBQztJQUVELFlBQ0UseUJBQXdELEVBQ3hELGFBQXFCLEVBQ3JCLGlCQUF5QyxFQUFFLEVBQzNDLE1BQWU7UUFFZixPQUFPLElBQUksc0JBQXlCLENBQUMseUJBQXlCLEVBQUUsYUFBYSxFQUFFO1lBQzdFLFVBQVUsRUFBRSwwQ0FBa0I7WUFDOUIsUUFBUSxFQUFFLG1CQUFTLENBQUMsUUFBUSxFQUFFLENBQUMsVUFBVSxFQUFFO1lBQzNDLGNBQWMsRUFBRSxjQUFxQixFQUFFLDhDQUE4QztZQUNyRixNQUFNO1NBQ1AsQ0FBQyxDQUFDO0lBQ0wsQ0FBQztDQUNGO0FBdEJELGtDQXNCQyIsInNvdXJjZXNDb250ZW50IjpbIi8vIFRoaXMgaXMgYSBsZWdhY3kgd3JhcHBlciBmb3IgY29kZSBmcm9tIHRoZSBhd3MtYXV0aCB0aGF0IHdlIHdhbnQgdG8ga2VlcCB0aGUgc2lnbmF0dXJlcyBpbnRhY3Rcbi8vIFdlIGdlbmVyYWxseSB1c2UgdHdvIGRpZmZlcmVudCBwYXR0ZXJucyBoZXJlOlxuLy8gLSBtYWtlIGEgY29weSBvZiB0aGUgb2xkIGNvZGUgYXMgaXNcbi8vIC0gd3JhcCB0aGUgb2xkIGNvZGUgYW5kIGFkZCBhIGRlcHJlY2F0aW9uIHdhcm5pbmdcbi8vIC0gbWFrZSBhIG5vLW9wIGNvcHkgdGhhdCBwcmVzZXJ2ZXMgdGhlIHByZXZpb3VzIGludGVyZmFjZSBidXQgZG9lc24ndCBkbyBhbnl0aGluZ1xuLy8gVGhpcyB3YXkgd2UgY2FuIGtlZXAgdGhlIG9sZCBjb2RlIHJ1bm5pbmcgdW50aWwgdGhlIG5ldyBjb2RlIGlzIGZ1bGx5IHJlYWR5XG4vLyBhbmQgY2FuIGJlIHVzZWQgYnkgdGhlIHVzZXJzIHRoYXQgYXJlIHJlYWR5IHRvIG1pZ3JhdGVcbi8vIFRoZSBvbGQgY29kZSB3aWxsIGJlIHJlbW92ZWQgaW4gYSBmdXR1cmUgdmVyc2lvbiBvZiBhd3MtY2RrXG5pbXBvcnQgdHlwZSB7IEF3c0NyZWRlbnRpYWxJZGVudGl0eVByb3ZpZGVyLCBMb2dnZXIsIE5vZGVIdHRwSGFuZGxlck9wdGlvbnMgfSBmcm9tICdAc21pdGh5L3R5cGVzJztcbmltcG9ydCB7IFNka1Byb3ZpZGVyIGFzIFNka1Byb3ZpZGVyQ3VycmVudFZlcnNpb24gfSBmcm9tICcuL2FwaS9hd3MtYXV0aCc7XG5pbXBvcnQgeyBDbGlJb0hvc3QgfSBmcm9tICcuL2NsaS9pby1ob3N0JztcbmltcG9ydCB7IEdMT0JBTF9QTFVHSU5fSE9TVCB9IGZyb20gJy4vY2xpL3NpbmdsZXRvbi1wbHVnaW4taG9zdCc7XG5cbi8qKlxuICogQGRlcHJlY2F0ZWRcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNhY2hlZDxBIGV4dGVuZHMgb2JqZWN0LCBCPihvYmo6IEEsIHN5bTogc3ltYm9sLCBmbjogKCkgPT4gQik6IEIge1xuICBpZiAoIShzeW0gaW4gb2JqKSkge1xuICAgIChvYmogYXMgYW55KVtzeW1dID0gZm4oKTtcbiAgfVxuICByZXR1cm4gKG9iaiBhcyBhbnkpW3N5bV07XG59XG5cbi8qKlxuICogQGRlcHJlY2F0ZWRcbiAqL1xuZXhwb3J0IGludGVyZmFjZSBDb250ZXh0UHJvdmlkZXJQbHVnaW4ge1xuICBnZXRWYWx1ZShhcmdzOiB7IFtrZXk6IHN0cmluZ106IGFueSB9KTogUHJvbWlzZTxhbnk+O1xufVxuXG4vKipcbiAqIEFuIEFXUyBhY2NvdW50XG4gKiBAZGVwcmVjYXRlZFxuICovXG5leHBvcnQgaW50ZXJmYWNlIEFjY291bnQge1xuICByZWFkb25seSBhY2NvdW50SWQ6IHN0cmluZztcbiAgcmVhZG9ubHkgcGFydGl0aW9uOiBzdHJpbmc7XG59XG5cbi8qKlxuICogRW5hYmxlIHRyYWNpbmcgaW4gdGhlIENES1xuICpcbiAqIEBkZXByZWNhdGVkIGNhbm5vdCBiZSBlbmFibGVkIGZyb20gb3V0c2lkZSB0aGUgQ0RLXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBlbmFibGVUcmFjaW5nKF9lbmFibGVkOiBib29sZWFuKSB7XG4gIC8vIG5vb3Bcbn1cblxuLyoqXG4gKiBPcHRpb25zIGZvciBpbmRpdmlkdWFsIFNES3NcbiAqIEBkZXByZWNhdGVkXG4gKi9cbmludGVyZmFjZSBTZGtIdHRwT3B0aW9ucyB7XG4gIC8qKlxuICAgKiBQcm94eSBhZGRyZXNzIHRvIHVzZVxuICAgKlxuICAgKiBAZGVmYXVsdCBObyBwcm94eVxuICAgKi9cbiAgcmVhZG9ubHkgcHJveHlBZGRyZXNzPzogc3RyaW5nO1xuXG4gIC8qKlxuICAgKiBBIHBhdGggdG8gYSBjZXJ0aWZpY2F0ZSBidW5kbGUgdGhhdCBjb250YWlucyBhIGNlcnQgdG8gYmUgdHJ1c3RlZC5cbiAgICpcbiAgICogQGRlZmF1bHQgTm8gY2VydGlmaWNhdGUgYnVuZGxlXG4gICAqL1xuICByZWFkb25seSBjYUJ1bmRsZVBhdGg/OiBzdHJpbmc7XG59XG5cbi8qKlxuICogT3B0aW9ucyBmb3IgdGhlIGRlZmF1bHQgU0RLIHByb3ZpZGVyXG4gKiBAZGVwcmVjYXRlZFxuICovXG5pbnRlcmZhY2UgU2RrUHJvdmlkZXJPcHRpb25zIHtcbiAgLyoqXG4gICAqIFByb2ZpbGUgdG8gcmVhZCBmcm9tIH4vLmF3c1xuICAgKlxuICAgKiBAZGVmYXVsdCAtIE5vIHByb2ZpbGVcbiAgICovXG4gIHJlYWRvbmx5IHByb2ZpbGU/OiBzdHJpbmc7XG5cbiAgLyoqXG4gICAqIEhUVFAgb3B0aW9ucyBmb3IgU0RLXG4gICAqL1xuICByZWFkb25seSBodHRwT3B0aW9ucz86IFNka0h0dHBPcHRpb25zO1xuXG4gIC8qKlxuICAgKiBUaGUgbG9nZ2VyIGZvciBzZGsgY2FsbHMuXG4gICAqL1xuICByZWFkb25seSBsb2dnZXI/OiBMb2dnZXI7XG59XG5cbi8qKlxuICogQGRlcHJlY2F0ZWRcbiAqL1xuZXhwb3J0IGNsYXNzIFNka1Byb3ZpZGVyIHtcbiAgcHVibGljIHN0YXRpYyBhc3luYyB3aXRoQXdzQ2xpQ29tcGF0aWJsZURlZmF1bHRzKG9wdGlvbnM6IFNka1Byb3ZpZGVyT3B0aW9ucyA9IHt9KSB7XG4gICAgcmV0dXJuIFNka1Byb3ZpZGVyQ3VycmVudFZlcnNpb24ud2l0aEF3c0NsaUNvbXBhdGlibGVEZWZhdWx0cyh7XG4gICAgICAuLi5vcHRpb25zLFxuICAgICAgaW9IZWxwZXI6IENsaUlvSG9zdC5pbnN0YW5jZSgpLmFzSW9IZWxwZXIoKSxcbiAgICAgIHBsdWdpbkhvc3Q6IEdMT0JBTF9QTFVHSU5fSE9TVCxcbiAgICB9LCBvcHRpb25zLnByb2ZpbGUpO1xuICB9XG5cbiAgcHVibGljIGNvbnN0cnVjdG9yKFxuICAgIGRlZmF1bHRDcmVkZW50aWFsUHJvdmlkZXI6IEF3c0NyZWRlbnRpYWxJZGVudGl0eVByb3ZpZGVyLFxuICAgIGRlZmF1bHRSZWdpb246IHN0cmluZyxcbiAgICByZXF1ZXN0SGFuZGxlcjogTm9kZUh0dHBIYW5kbGVyT3B0aW9ucyA9IHt9LFxuICAgIGxvZ2dlcj86IExvZ2dlcixcbiAgKSB7XG4gICAgcmV0dXJuIG5ldyBTZGtQcm92aWRlckN1cnJlbnRWZXJzaW9uKGRlZmF1bHRDcmVkZW50aWFsUHJvdmlkZXIsIGRlZmF1bHRSZWdpb24sIHtcbiAgICAgIHBsdWdpbkhvc3Q6IEdMT0JBTF9QTFVHSU5fSE9TVCxcbiAgICAgIGlvSGVscGVyOiBDbGlJb0hvc3QuaW5zdGFuY2UoKS5hc0lvSGVscGVyKCksXG4gICAgICByZXF1ZXN0SGFuZGxlcjogcmVxdWVzdEhhbmRsZXIgYXMgYW55LCAvLyB0aGlzIGlzIGZpbmUgaXQncyBwYXNzZWQgdGhyb3VnaCB0byB0aGUgU0RLXG4gICAgICBsb2dnZXIsXG4gICAgfSk7XG4gIH1cbn1cbiJdfQ==