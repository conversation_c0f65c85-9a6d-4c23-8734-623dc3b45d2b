"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.highlight = exports.success = exports.debug = exports.result = exports.info = exports.warning = exports.error = void 0;
const chalk = require("chalk");
const api_private_1 = require("../lib/api-private");
const cli_io_host_1 = require("./cli/io-host/cli-io-host");
/**
 * Logs messages to the global CliIoHost
 *
 * Internal helper that processes log inputs into a consistent format.
 * Handles string interpolation, format strings, and object parameter styles.
 * Applies optional styling and sends the message to the IoHost.
 */
function formatMessageAndLog(level, input, ...args) {
    const singletonHost = cli_io_host_1.CliIoHost.instance();
    // ALARM: forcing a CliAction into a ToolkitAction.
    const helper = (0, api_private_1.asIoHelper)(singletonHost, singletonHost.currentAction);
    if (typeof input === 'string') {
        void singletonHost.defaults[level](input, ...args);
    }
    else {
        void helper.notify({
            data: undefined,
            time: new Date(),
            level,
            ...input,
        });
    }
}
// Exported logging functions. If any additional logging functionality is required, it should be added as
// a new logging function here.
/**
 * Logs an error level message.
 *
 * Can be used in multiple ways:
 * ```ts
 * error(`operation failed: ${e}`) // infers default error code `CDK_TOOLKIT_E000`
 * error('operation failed: %s', e) // infers default error code `CDK_TOOLKIT_E000`
 * error({ message: 'operation failed', code: 'CDK_SDK_E001' }) // specifies error code `CDK_SDK_E001`
 * error({ message: 'operation failed: %s', code: 'CDK_SDK_E001' }, e) // specifies error code `CDK_SDK_E001`
 * ```
 */
const error = (input, ...args) => {
    return formatMessageAndLog('error', input, ...args);
};
exports.error = error;
/**
 * Logs an warning level message.
 *
 * Can be used in multiple ways:
 * ```ts
 * warning(`deprected feature: ${message}`) // infers default warning code `CDK_TOOLKIT_W000`
 * warning('deprected feature: %s', message) // infers default warning code `CDK_TOOLKIT_W000`
 * warning({ message: 'deprected feature', code: 'CDK_SDK_W001' }) // specifies warning code `CDK_SDK_W001`
 * warning({ message: 'deprected feature: %s', code: 'CDK_SDK_W001' }, message) // specifies warning code `CDK_SDK_W001`
 * ```
 */
const warning = (input, ...args) => {
    return formatMessageAndLog('warn', input, ...args);
};
exports.warning = warning;
/**
 * Logs an info level message.
 *
 * Can be used in multiple ways:
 * ```ts
 * info(`processing: ${message}`) // infers default info code `CDK_TOOLKIT_I000`
 * info('processing: %s', message) // infers default info code `CDK_TOOLKIT_I000`
 * info({ message: 'processing', code: 'CDK_TOOLKIT_I001' }) // specifies info code `CDK_TOOLKIT_I001`
 * info({ message: 'processing: %s', code: 'CDK_TOOLKIT_I001' }, message) // specifies info code `CDK_TOOLKIT_I001`
 * ```
 */
const info = (input, ...args) => {
    return formatMessageAndLog('info', input, ...args);
};
exports.info = info;
/**
 * Logs an result. In the CLI, this always goes to stdout.
 *
 * Can be used in multiple ways:
 * ```ts
 * result(`${JSON.stringify(stats)}`) // infers default info code `CDK_TOOLKIT_I000`
 * result('{"count": %d}', count) // infers default info code `CDK_TOOLKIT_I000`
 * result({ message: 'stats: %j', code: 'CDK_DATA_I001' }) // specifies info code `CDK_DATA_I001`
 * result({ message: 'stats: %j', code: 'CDK_DATA_I001' }, stats) // specifies info code `CDK_DATA_I001`
 * ```
 */
const result = (input, ...args) => {
    return formatMessageAndLog('result', input, ...args);
};
exports.result = result;
/**
 * Logs a debug level message.
 *
 * Can be used in multiple ways:
 * ```ts
 * debug(`state: ${JSON.stringify(state)}`) // infers default info code `CDK_TOOLKIT_I000`
 * debug('cache hit ratio: %d%%', ratio) // infers default info code `CDK_TOOLKIT_I000`
 * debug({ message: 'state update', code: 'CDK_TOOLKIT_I001' }) // specifies info code `CDK_TOOLKIT_I001`
 * debug({ message: 'ratio: %d%%', code: 'CDK_TOOLKIT_I001' }, ratio) // specifies info code `CDK_TOOLKIT_I001`
 * ```
 */
const debug = (input, ...args) => {
    return formatMessageAndLog('debug', input, ...args);
};
exports.debug = debug;
/**
 * Logs an info level success message in green text.
 *
 * Can be used in multiple ways:
 * ```ts
 * success(`deployment completed: ${name}`) // infers default info code `CDK_TOOLKIT_I000`
 * success('processed %d items', count) // infers default info code `CDK_TOOLKIT_I000`
 * success({ message: 'completed', code: 'CDK_TOOLKIT_I001' }) // specifies info code `CDK_TOOLKIT_I001`
 * success({ message: 'items: %d', code: 'CDK_TOOLKIT_I001' }, count) // specifies info code `CDK_TOOLKIT_I001`
 * ```
 */
const success = (input, ...args) => {
    return formatMessageAndLog('info', chalkInput(input, chalk.green), ...args);
};
exports.success = success;
/**
 * Logs an info level message in bold text.
 *
 * Can be used in multiple ways:
 * ```ts
 * highlight(`important: ${msg}`) // infers default info code `CDK_TOOLKIT_I000`
 * highlight('attention required: %s', reason) // infers default info code `CDK_TOOLKIT_I000`
 * highlight({ message: 'notice', code: 'CDK_TOOLKIT_I001' }) // specifies info code `CDK_TOOLKIT_I001`
 * highlight({ message: 'notice: %s', code: 'CDK_TOOLKIT_I001' }, msg) // specifies info code `CDK_TOOLKIT_I001`
 * ```
 */
const highlight = (input, ...args) => {
    return formatMessageAndLog('info', chalkInput(input, chalk.bold), ...args);
};
exports.highlight = highlight;
function chalkInput(i, style) {
    if (typeof i === 'string') {
        return style(i);
    }
    return {
        ...i,
        message: style(i.message),
    };
}
//# sourceMappingURL=data:application/json;base64,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