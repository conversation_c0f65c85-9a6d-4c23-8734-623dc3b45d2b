"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.listStacks = listStacks;
const cxapp_1 = require("../cxapp");
/**
 * List Stacks
 *
 * @param toolkit - cdk toolkit
 * @param options - list stacks options
 * @returns StackDetails[]
 */
async function listStacks(toolkit, options) {
    const assembly = await toolkit.assembly();
    const stacks = await assembly.selectStacks({
        patterns: options.selectors,
    }, {
        extend: cxapp_1.ExtendedStackSelection.Upstream,
        defaultBehavior: cxapp_1.DefaultSelection.AllStacks,
    });
    return stacks.withDependencies();
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibGlzdC1zdGFja3MuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyJsaXN0LXN0YWNrcy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiOztBQXVCQSxnQ0FXQztBQWhDRCxvQ0FBb0U7QUFjcEU7Ozs7OztHQU1HO0FBQ0ksS0FBSyxVQUFVLFVBQVUsQ0FBQyxPQUFtQixFQUFFLE9BQTBCO0lBQzlFLE1BQU0sUUFBUSxHQUFHLE1BQU0sT0FBTyxDQUFDLFFBQVEsRUFBRSxDQUFDO0lBRTFDLE1BQU0sTUFBTSxHQUFHLE1BQU0sUUFBUSxDQUFDLFlBQVksQ0FBQztRQUN6QyxRQUFRLEVBQUUsT0FBTyxDQUFDLFNBQVM7S0FDNUIsRUFBRTtRQUNELE1BQU0sRUFBRSw4QkFBc0IsQ0FBQyxRQUFRO1FBQ3ZDLGVBQWUsRUFBRSx3QkFBZ0IsQ0FBQyxTQUFTO0tBQzVDLENBQUMsQ0FBQztJQUVILE9BQU8sTUFBTSxDQUFDLGdCQUFnQixFQUFFLENBQUM7QUFDbkMsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgU3RhY2tEZXRhaWxzIH0gZnJvbSAnQGF3cy1jZGsvdG9vbGtpdC1saWInO1xuaW1wb3J0IHR5cGUgeyBDZGtUb29sa2l0IH0gZnJvbSAnLi4vY2xpL2Nkay10b29sa2l0JztcbmltcG9ydCB7IERlZmF1bHRTZWxlY3Rpb24sIEV4dGVuZGVkU3RhY2tTZWxlY3Rpb24gfSBmcm9tICcuLi9jeGFwcCc7XG5cbi8qKlxuICogT3B0aW9ucyBmb3IgTGlzdCBTdGFja3NcbiAqL1xuZXhwb3J0IGludGVyZmFjZSBMaXN0U3RhY2tzT3B0aW9ucyB7XG4gIC8qKlxuICAgKiBTdGFja3MgdG8gbGlzdFxuICAgKlxuICAgKiBAZGVmYXVsdCAtIEFsbCBzdGFja3MgYXJlIGxpc3RlZFxuICAgKi9cbiAgcmVhZG9ubHkgc2VsZWN0b3JzOiBzdHJpbmdbXTtcbn1cblxuLyoqXG4gKiBMaXN0IFN0YWNrc1xuICpcbiAqIEBwYXJhbSB0b29sa2l0IC0gY2RrIHRvb2xraXRcbiAqIEBwYXJhbSBvcHRpb25zIC0gbGlzdCBzdGFja3Mgb3B0aW9uc1xuICogQHJldHVybnMgU3RhY2tEZXRhaWxzW11cbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGxpc3RTdGFja3ModG9vbGtpdDogQ2RrVG9vbGtpdCwgb3B0aW9uczogTGlzdFN0YWNrc09wdGlvbnMpOiBQcm9taXNlPFN0YWNrRGV0YWlsc1tdPiB7XG4gIGNvbnN0IGFzc2VtYmx5ID0gYXdhaXQgdG9vbGtpdC5hc3NlbWJseSgpO1xuXG4gIGNvbnN0IHN0YWNrcyA9IGF3YWl0IGFzc2VtYmx5LnNlbGVjdFN0YWNrcyh7XG4gICAgcGF0dGVybnM6IG9wdGlvbnMuc2VsZWN0b3JzLFxuICB9LCB7XG4gICAgZXh0ZW5kOiBFeHRlbmRlZFN0YWNrU2VsZWN0aW9uLlVwc3RyZWFtLFxuICAgIGRlZmF1bHRCZWhhdmlvcjogRGVmYXVsdFNlbGVjdGlvbi5BbGxTdGFja3MsXG4gIH0pO1xuXG4gIHJldHVybiBzdGFja3Mud2l0aERlcGVuZGVuY2llcygpO1xufVxuIl19