"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.contextHandler = contextHandler;
const toolkit_lib_1 = require("@aws-cdk/toolkit-lib");
const chalk = require("chalk");
const minimatch_1 = require("minimatch");
const tables_1 = require("../cli/tables");
const user_configuration_1 = require("../cli/user-configuration");
const version = require("../cli/version");
const logging_1 = require("../logging");
async function contextHandler(options) {
    if (options.clear) {
        options.context.clear();
        await options.context.save(user_configuration_1.PROJECT_CONTEXT);
        (0, logging_1.info)('All context values cleared.');
    }
    else if (options.reset) {
        invalidateContext(options.context, options.reset, options.force ?? false);
        await options.context.save(user_configuration_1.PROJECT_CONTEXT);
    }
    else {
        // List -- support '--json' flag
        if (options.json) {
            /* c8 ignore start */
            const contextValues = options.context.all;
            (0, logging_1.result)(JSON.stringify(contextValues, undefined, 2));
            /* c8 ignore stop */
        }
        else {
            listContext(options.context);
        }
    }
    await version.displayVersionMessage();
    return 0;
}
function listContext(context) {
    const keys = contextKeys(context);
    if (keys.length === 0) {
        (0, logging_1.info)('This CDK application does not have any saved context values yet.');
        (0, logging_1.info)('');
        (0, logging_1.info)('Context will automatically be saved when you synthesize CDK apps');
        (0, logging_1.info)('that use environment context information like AZ information, VPCs,');
        (0, logging_1.info)('SSM parameters, and so on.');
        return;
    }
    // Print config by default
    const data_out = [[chalk.green('#'), chalk.green('Key'), chalk.green('Value')]];
    for (const [i, key] of keys) {
        const jsonWithoutNewlines = JSON.stringify(context.all[key], undefined, 2).replace(/\s+/g, ' ');
        data_out.push([i, key, jsonWithoutNewlines]);
    }
    (0, logging_1.info)('Context found in %s:', chalk.blue(user_configuration_1.PROJECT_CONFIG));
    (0, logging_1.info)('');
    (0, logging_1.info)((0, tables_1.renderTable)(data_out, process.stdout.columns));
    // eslint-disable-next-line @stylistic/max-len
    (0, logging_1.info)(`Run ${chalk.blue('cdk context --reset KEY_OR_NUMBER')} to remove a context key. It will be refreshed on the next CDK synthesis run.`);
}
function invalidateContext(context, key, force) {
    const i = parseInt(key, 10);
    if (`${i}` === key) {
        // was a number and we fully parsed it.
        key = keyByNumber(context, i);
    }
    // Unset!
    if (context.has(key)) {
        context.unset(key);
        // check if the value was actually unset.
        if (!context.has(key)) {
            (0, logging_1.info)('Context value %s reset. It will be refreshed on next synthesis', chalk.blue(key));
            return;
        }
        // Value must be in readonly bag
        (0, logging_1.error)('Only context values specified in %s can be reset through the CLI', chalk.blue(user_configuration_1.PROJECT_CONTEXT));
        if (!force) {
            throw new toolkit_lib_1.ToolkitError(`Cannot reset readonly context value with key: ${key}`);
        }
    }
    // check if value is expression matching keys
    const matches = keysByExpression(context, key);
    if (matches.length > 0) {
        matches.forEach((match) => {
            context.unset(match);
        });
        const { unset, readonly } = getUnsetAndReadonly(context, matches);
        // output the reset values
        printUnset(unset);
        // warn about values not reset
        printReadonly(readonly);
        // throw when none of the matches were reset
        if (!force && unset.length === 0) {
            throw new toolkit_lib_1.ToolkitError('None of the matched context values could be reset');
        }
        return;
    }
    if (!force) {
        throw new toolkit_lib_1.ToolkitError(`No context value matching key: ${key}`);
    }
}
function printUnset(unset) {
    if (unset.length === 0)
        return;
    (0, logging_1.info)('The following matched context values reset. They will be refreshed on next synthesis');
    unset.forEach((match) => {
        (0, logging_1.info)('  %s', match);
    });
}
function printReadonly(readonly) {
    if (readonly.length === 0)
        return;
    (0, logging_1.warning)('The following matched context values could not be reset through the CLI');
    readonly.forEach((match) => {
        (0, logging_1.info)('  %s', match);
    });
    (0, logging_1.info)('');
    (0, logging_1.info)('This usually means they are configured in %s or %s', chalk.blue(user_configuration_1.PROJECT_CONFIG), chalk.blue(user_configuration_1.USER_DEFAULTS));
}
function keysByExpression(context, expression) {
    return context.keys.filter(minimatch_1.minimatch.filter(expression));
}
function getUnsetAndReadonly(context, matches) {
    return matches.reduce((acc, match) => {
        if (context.has(match)) {
            acc.readonly.push(match);
        }
        else {
            acc.unset.push(match);
        }
        return acc;
    }, { unset: [], readonly: [] });
}
function keyByNumber(context, n) {
    for (const [i, key] of contextKeys(context)) {
        if (n === i) {
            return key;
        }
    }
    throw new toolkit_lib_1.ToolkitError(`No context key with number: ${n}`);
}
/**
 * Return enumerated keys in a definitive order
 */
function contextKeys(context) {
    const keys = context.keys;
    keys.sort();
    return enumerate1(keys);
}
function enumerate1(xs) {
    const ret = new Array();
    let i = 1;
    for (const x of xs) {
        ret.push([i, x]);
        i += 1;
    }
    return ret;
}
//# sourceMappingURL=data:application/json;base64,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