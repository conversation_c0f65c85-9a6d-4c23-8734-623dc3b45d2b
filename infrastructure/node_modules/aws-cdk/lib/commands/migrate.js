"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FromScan = exports.CfnTemplateGeneratorProvider = exports.FilterType = exports.ScanStatus = exports.TemplateSourceOptions = void 0;
exports.generateCdkApp = generateCdkApp;
exports.generateStack = generateStack;
exports.readFromPath = readFromPath;
exports.readFromStack = readFromStack;
exports.generateTemplate = generateTemplate;
exports.chunks = chunks;
exports.setEnvironment = setEnvironment;
exports.parseSourceOptions = parseSourceOptions;
exports.scanProgressBar = scanProgressBar;
exports.printBar = printBar;
exports.printDots = printDots;
exports.rewriteLine = rewriteLine;
exports.displayTimeDiff = displayTimeDiff;
exports.writeMigrateJsonFile = writeMigrateJsonFile;
exports.getMigrateScanType = getMigrateScanType;
exports.isThereAWarning = isThereAWarning;
exports.buildGenertedTemplateOutput = buildGenertedTemplateOutput;
exports.buildCfnClient = buildCfnClient;
exports.appendWarningsToReadme = appendWarningsToReadme;
/* eslint-disable @typescript-eslint/no-require-imports */
/* eslint-disable @typescript-eslint/no-var-requires */
const fs = require("fs");
const path = require("path");
const cx_api_1 = require("@aws-cdk/cx-api");
const toolkit_lib_1 = require("@aws-cdk/toolkit-lib");
const cdk_from_cfn = require("cdk-from-cfn");
const chalk = require("chalk");
const init_1 = require("./init");
const logging_1 = require("../../lib/logging");
const cloudformation_1 = require("../api/cloudformation");
const plugin_1 = require("../api/plugin");
const util_1 = require("../util");
const camelCase = require('camelcase');
const decamelize = require('decamelize');
/** The list of languages supported by the built-in noctilucent binary. */
const MIGRATE_SUPPORTED_LANGUAGES = cdk_from_cfn.supported_languages();
/**
 * Generates a CDK app from a yaml or json template.
 *
 * @param stackName - The name to assign to the stack in the generated app
 * @param stack - The yaml or json template for the stack
 * @param language - The language to generate the CDK app in
 * @param outputPath - The path at which to generate the CDK app
 */
async function generateCdkApp(stackName, stack, language, outputPath, compress) {
    const resolvedOutputPath = path.join(outputPath ?? process.cwd(), stackName);
    const formattedStackName = decamelize(stackName);
    try {
        fs.rmSync(resolvedOutputPath, { recursive: true, force: true });
        fs.mkdirSync(resolvedOutputPath, { recursive: true });
        const generateOnly = compress;
        await (0, init_1.cliInit)({
            type: 'app',
            language,
            canUseNetwork: true,
            generateOnly,
            workDir: resolvedOutputPath,
            stackName,
            migrate: true,
        });
        let stackFileName;
        switch (language) {
            case 'typescript':
                stackFileName = `${resolvedOutputPath}/lib/${formattedStackName}-stack.ts`;
                break;
            case 'java':
                stackFileName = `${resolvedOutputPath}/src/main/java/com/myorg/${camelCase(formattedStackName, { pascalCase: true })}Stack.java`;
                break;
            case 'python':
                stackFileName = `${resolvedOutputPath}/${formattedStackName.replace(/-/g, '_')}/${formattedStackName.replace(/-/g, '_')}_stack.py`;
                break;
            case 'csharp':
                stackFileName = `${resolvedOutputPath}/src/${camelCase(formattedStackName, { pascalCase: true })}/${camelCase(formattedStackName, { pascalCase: true })}Stack.cs`;
                break;
            case 'go':
                stackFileName = `${resolvedOutputPath}/${formattedStackName}.go`;
                break;
            default:
                throw new toolkit_lib_1.ToolkitError(`${language} is not supported by CDK Migrate. Please choose from: ${MIGRATE_SUPPORTED_LANGUAGES.join(', ')}`);
        }
        fs.writeFileSync(stackFileName, stack);
        if (compress) {
            await (0, util_1.zipDirectory)(resolvedOutputPath, `${resolvedOutputPath}.zip`);
            fs.rmSync(resolvedOutputPath, { recursive: true, force: true });
        }
    }
    catch (error) {
        fs.rmSync(resolvedOutputPath, { recursive: true, force: true });
        throw error;
    }
}
/**
 * Generates a CDK stack file.
 * @param template - The template to translate into a CDK stack
 * @param stackName - The name to assign to the stack
 * @param language - The language to generate the stack in
 * @returns A string representation of a CDK stack file
 */
function generateStack(template, stackName, language) {
    const formattedStackName = `${camelCase(decamelize(stackName), { pascalCase: true })}Stack`;
    try {
        return cdk_from_cfn.transmute(template, language, formattedStackName);
    }
    catch (e) {
        throw new toolkit_lib_1.ToolkitError(`${formattedStackName} could not be generated because ${e.message}`);
    }
}
/**
 * Reads and returns a stack template from a local path.
 *
 * @param inputPath - The location of the template
 * @returns A string representation of the template if present, otherwise undefined
 */
function readFromPath(inputPath) {
    let readFile;
    try {
        readFile = fs.readFileSync(inputPath, 'utf8');
    }
    catch (e) {
        throw new toolkit_lib_1.ToolkitError(`'${inputPath}' is not a valid path.`);
    }
    if (readFile == '') {
        throw new toolkit_lib_1.ToolkitError(`Cloudformation template filepath: '${inputPath}' is an empty file.`);
    }
    return readFile;
}
/**
 * Reads and returns a stack template from a deployed CloudFormation stack.
 *
 * @param stackName - The name of the stack
 * @param sdkProvider - The sdk provider for making CloudFormation calls
 * @param environment - The account and region where the stack is deployed
 * @returns A string representation of the template if present, otherwise undefined
 */
async function readFromStack(stackName, sdkProvider, environment) {
    const cloudFormation = (await sdkProvider.forEnvironment(environment, plugin_1.Mode.ForReading)).sdk.cloudFormation();
    const stack = await cloudformation_1.CloudFormationStack.lookup(cloudFormation, stackName, true);
    if (stack.stackStatus.isDeploySuccess || stack.stackStatus.isRollbackSuccess) {
        return JSON.stringify(await stack.template());
    }
    else {
        throw new toolkit_lib_1.ToolkitError(`Stack '${stackName}' in account ${environment.account} and region ${environment.region} has a status of '${stack.stackStatus.name}' due to '${stack.stackStatus.reason}'. The stack cannot be migrated until it is in a healthy state.`);
    }
}
/**
 * Takes in a stack name and account and region and returns a generated cloudformation template using the cloudformation
 * template generator.
 *
 * @param GenerateTemplateOptions - An object containing the stack name, filters, sdkProvider, environment, and newScan flag
 * @returns a generated cloudformation template
 */
async function generateTemplate(options) {
    const cfn = new CfnTemplateGeneratorProvider(await buildCfnClient(options.sdkProvider, options.environment));
    const scanId = await findLastSuccessfulScan(cfn, options);
    // if a customer accidentally ctrl-c's out of the command and runs it again, this will continue the progress bar where it left off
    const curScan = await cfn.describeResourceScan(scanId);
    if (curScan.Status == ScanStatus.IN_PROGRESS) {
        (0, logging_1.info)('Resource scan in progress. Please wait, this can take 10 minutes or longer.');
        await scanProgressBar(scanId, cfn);
    }
    displayTimeDiff(new Date(), new Date(curScan.StartTime));
    let resources = await cfn.listResourceScanResources(scanId, options.filters);
    (0, logging_1.info)('finding related resources.');
    let relatedResources = await cfn.getResourceScanRelatedResources(scanId, resources);
    (0, logging_1.info)(`Found ${relatedResources.length} resources.`);
    (0, logging_1.info)('Generating CFN template from scanned resources.');
    const templateArn = (await cfn.createGeneratedTemplate(options.stackName, relatedResources)).GeneratedTemplateId;
    let generatedTemplate = await cfn.describeGeneratedTemplate(templateArn);
    (0, logging_1.info)('Please wait, template creation in progress. This may take a couple minutes.');
    while (generatedTemplate.Status !== ScanStatus.COMPLETE && generatedTemplate.Status !== ScanStatus.FAILED) {
        await printDots(`[${generatedTemplate.Status}] Template Creation in Progress`, 400);
        generatedTemplate = await cfn.describeGeneratedTemplate(templateArn);
    }
    (0, logging_1.info)('');
    (0, logging_1.info)('Template successfully generated!');
    return buildGenertedTemplateOutput(generatedTemplate, (await cfn.getGeneratedTemplate(templateArn)).TemplateBody, templateArn);
}
async function findLastSuccessfulScan(cfn, options) {
    let resourceScanSummaries = [];
    const clientRequestToken = `cdk-migrate-${options.environment.account}-${options.environment.region}`;
    if (options.fromScan === FromScan.NEW) {
        (0, logging_1.info)(`Starting new scan for account ${options.environment.account} in region ${options.environment.region}`);
        try {
            await cfn.startResourceScan(clientRequestToken);
            resourceScanSummaries = (await cfn.listResourceScans()).ResourceScanSummaries;
        }
        catch (e) {
            // continuing here because if the scan fails on a new-scan it is very likely because there is either already a scan in progress
            // or the customer hit a rate limit. In either case we want to continue with the most recent scan.
            // If this happens to fail for a credential error then that will be caught immediately after anyway.
            (0, logging_1.info)(`Scan failed to start due to error '${e.message}', defaulting to latest scan.`);
        }
    }
    else {
        resourceScanSummaries = (await cfn.listResourceScans()).ResourceScanSummaries;
        await cfn.checkForResourceScan(resourceScanSummaries, options, clientRequestToken);
    }
    // get the latest scan, which we know will exist
    resourceScanSummaries = (await cfn.listResourceScans()).ResourceScanSummaries;
    let scanId = resourceScanSummaries[0].ResourceScanId;
    // find the most recent scan that isn't in a failed state in case we didn't start a new one
    for (const summary of resourceScanSummaries) {
        if (summary.Status !== ScanStatus.FAILED) {
            scanId = summary.ResourceScanId;
            break;
        }
    }
    return scanId;
}
/**
 * Takes a string of filters in the format of key1=value1,key2=value2 and returns a map of the filters.
 *
 * @param filters - a string of filters in the format of key1=value1,key2=value2
 * @returns a map of the filters
 */
function parseFilters(filters) {
    if (!filters) {
        return {
            'resource-identifier': undefined,
            'resource-type-prefix': undefined,
            'tag-key': undefined,
            'tag-value': undefined,
        };
    }
    const filterShorthands = {
        'identifier': FilterType.RESOURCE_IDENTIFIER,
        'id': FilterType.RESOURCE_IDENTIFIER,
        'type': FilterType.RESOURCE_TYPE_PREFIX,
        'type-prefix': FilterType.RESOURCE_TYPE_PREFIX,
    };
    const filterList = filters.split(',');
    let filterMap = {
        [FilterType.RESOURCE_IDENTIFIER]: undefined,
        [FilterType.RESOURCE_TYPE_PREFIX]: undefined,
        [FilterType.TAG_KEY]: undefined,
        [FilterType.TAG_VALUE]: undefined,
    };
    for (const fil of filterList) {
        const filter = fil.split('=');
        let filterKey = filter[0];
        const filterValue = filter[1];
        // if the key is a shorthand, replace it with the full name
        if (filterKey in filterShorthands) {
            filterKey = filterShorthands[filterKey];
        }
        if (Object.values(FilterType).includes(filterKey)) {
            filterMap[filterKey] = filterValue;
        }
        else {
            throw new toolkit_lib_1.ToolkitError(`Invalid filter: ${filterKey}`);
        }
    }
    return filterMap;
}
/**
 * Takes a list of any type and breaks it up into chunks of a specified size.
 *
 * @param list - The list to break up
 * @param chunkSize - The size of each chunk
 * @returns A list of lists of the specified size
 */
function chunks(list, chunkSize) {
    const chunkedList = [];
    for (let i = 0; i < list.length; i += chunkSize) {
        chunkedList.push(list.slice(i, i + chunkSize));
    }
    return chunkedList;
}
/**
 * Sets the account and region for making CloudFormation calls.
 * @param account - The account to use
 * @param region - The region to use
 * @returns The environment object
 */
function setEnvironment(account, region) {
    return {
        account: account ?? cx_api_1.UNKNOWN_ACCOUNT,
        region: region ?? cx_api_1.UNKNOWN_REGION,
        name: 'cdk-migrate-env',
    };
}
/**
 * Enum for the source options for the template
 */
var TemplateSourceOptions;
(function (TemplateSourceOptions) {
    TemplateSourceOptions["PATH"] = "path";
    TemplateSourceOptions["STACK"] = "stack";
    TemplateSourceOptions["SCAN"] = "scan";
})(TemplateSourceOptions || (exports.TemplateSourceOptions = TemplateSourceOptions = {}));
/**
 * Enum for the status of a resource scan
 */
var ScanStatus;
(function (ScanStatus) {
    ScanStatus["IN_PROGRESS"] = "IN_PROGRESS";
    ScanStatus["COMPLETE"] = "COMPLETE";
    ScanStatus["FAILED"] = "FAILED";
})(ScanStatus || (exports.ScanStatus = ScanStatus = {}));
var FilterType;
(function (FilterType) {
    FilterType["RESOURCE_IDENTIFIER"] = "resource-identifier";
    FilterType["RESOURCE_TYPE_PREFIX"] = "resource-type-prefix";
    FilterType["TAG_KEY"] = "tag-key";
    FilterType["TAG_VALUE"] = "tag-value";
})(FilterType || (exports.FilterType = FilterType = {}));
/**
 * Validates that exactly one source option has been provided.
 * @param fromPath - The content of the flag `--from-path`
 * @param fromStack - the content of the flag `--from-stack`
 */
function parseSourceOptions(fromPath, fromStack, stackName) {
    if (fromPath && fromStack) {
        throw new toolkit_lib_1.ToolkitError('Only one of `--from-path` or `--from-stack` may be provided.');
    }
    if (!stackName) {
        throw new toolkit_lib_1.ToolkitError('`--stack-name` is a required field.');
    }
    if (!fromPath && !fromStack) {
        return { source: TemplateSourceOptions.SCAN };
    }
    if (fromPath) {
        return { source: TemplateSourceOptions.PATH, templatePath: fromPath };
    }
    return { source: TemplateSourceOptions.STACK, stackName: stackName };
}
/**
 * Takes a set of resources and removes any with the managedbystack flag set to true.
 *
 * @param resourceList - the list of resources provided by the list scanned resources calls
 * @returns a list of resources not managed by cfn stacks
 */
function excludeManaged(resourceList) {
    return resourceList
        .filter((r) => !r.ManagedByStack)
        .map((r) => ({
        ResourceType: r.ResourceType,
        ResourceIdentifier: r.ResourceIdentifier,
    }));
}
/**
 * Transforms a list of resources into a list of resource identifiers by removing the ManagedByStack flag.
 * Setting the value of the field to undefined effectively removes it from the object.
 *
 * @param resourceList - the list of resources provided by the list scanned resources calls
 * @returns a list of ScannedResourceIdentifier[]
 */
function resourceIdentifiers(resourceList) {
    const identifiers = [];
    resourceList.forEach((r) => {
        const identifier = {
            ResourceType: r.ResourceType,
            ResourceIdentifier: r.ResourceIdentifier,
        };
        identifiers.push(identifier);
    });
    return identifiers;
}
/**
 * Takes a scan id and maintains a progress bar to display the progress of a scan to the user.
 *
 * @param scanId - A string representing the scan id
 * @param cloudFormation - The CloudFormation sdk client to use
 */
async function scanProgressBar(scanId, cfn) {
    let curProgress = 0.5;
    // we know it's in progress initially since we wouldn't have gotten here if it wasn't
    let curScan = {
        Status: ScanStatus.IN_PROGRESS,
        $metadata: {},
    };
    while (curScan.Status == ScanStatus.IN_PROGRESS) {
        curScan = await cfn.describeResourceScan(scanId);
        curProgress = curScan.PercentageCompleted ?? curProgress;
        printBar(30, curProgress);
        await new Promise((resolve) => setTimeout(resolve, 2000));
    }
    (0, logging_1.info)('');
    (0, logging_1.info)('✅ Scan Complete!');
}
/**
 * Prints a progress bar to the console. To be used in a while loop to show progress of a long running task.
 * The progress bar deletes the current line on the console and rewrites it with the progress amount.
 *
 * @param width - The width of the progress bar
 * @param progress - The current progress to display as a percentage of 100
 */
function printBar(width, progress) {
    if (!process.env.MIGRATE_INTEG_TEST) {
        const FULL_BLOCK = '█';
        const PARTIAL_BLOCK = ['', '▏', '▎', '▍', '▌', '▋', '▊', '▉'];
        const fraction = Math.min(progress / 100, 1);
        const innerWidth = Math.max(1, width - 2);
        const chars = innerWidth * fraction;
        const remainder = chars - Math.floor(chars);
        const fullChars = FULL_BLOCK.repeat(Math.floor(chars));
        const partialChar = PARTIAL_BLOCK[Math.floor(remainder * PARTIAL_BLOCK.length)];
        const filler = '·'.repeat(innerWidth - Math.floor(chars) - (partialChar ? 1 : 0));
        const color = chalk.green;
        rewriteLine('[' + color(fullChars + partialChar) + filler + `] (${progress}%)`);
    }
}
/**
 * Prints a message to the console with a series periods appended to it. To be used in a while loop to show progress of a long running task.
 * The message deletes the current line and rewrites it several times to display 1-3 periods to show the user that the task is still running.
 *
 * @param message - The message to display
 * @param timeoutx4 - The amount of time to wait before printing the next period
 */
async function printDots(message, timeoutx4) {
    if (!process.env.MIGRATE_INTEG_TEST) {
        rewriteLine(message + ' .');
        await new Promise((resolve) => setTimeout(resolve, timeoutx4));
        rewriteLine(message + ' ..');
        await new Promise((resolve) => setTimeout(resolve, timeoutx4));
        rewriteLine(message + ' ...');
        await new Promise((resolve) => setTimeout(resolve, timeoutx4));
        rewriteLine(message);
        await new Promise((resolve) => setTimeout(resolve, timeoutx4));
    }
}
/**
 * Rewrites the current line on the console and writes a new message to it.
 * This is a helper funciton for printDots and printBar.
 *
 * @param message - The message to display
 */
function rewriteLine(message) {
    process.stdout.clearLine(0);
    process.stdout.cursorTo(0);
    process.stdout.write(message);
}
/**
 * Prints the time difference between two dates in days, hours, and minutes.
 *
 * @param time1 - The first date to compare
 * @param time2 - The second date to compare
 */
function displayTimeDiff(time1, time2) {
    const diff = Math.abs(time1.getTime() - time2.getTime());
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    (0, logging_1.info)(`Using the latest successful scan which is ${days} days, ${hours} hours, and ${minutes} minutes old.`);
}
/**
 * Writes a migrate.json file to the output directory.
 *
 * @param outputPath - The path to write the migrate.json file to
 * @param stackName - The name of the stack
 * @param generatedOutput - The output of the template generator
 */
function writeMigrateJsonFile(outputPath, stackName, migrateJson) {
    const outputToJson = {
        '//': 'This file is generated by cdk migrate. It will be automatically deleted after the first successful deployment of this app to the environment of the original resources.',
        'Source': migrateJson.source,
        'Resources': migrateJson.resources,
    };
    fs.writeFileSync(`${path.join(outputPath ?? process.cwd(), stackName)}/migrate.json`, JSON.stringify(outputToJson, null, 2));
}
/**
 * Takes a string representing the from-scan flag and returns a FromScan enum value.
 *
 * @param scanType - A string representing the from-scan flag
 * @returns A FromScan enum value
 */
function getMigrateScanType(scanType) {
    switch (scanType) {
        case 'new':
            return FromScan.NEW;
        case 'most-recent':
            return FromScan.MOST_RECENT;
        case '':
            return FromScan.DEFAULT;
        case undefined:
            return FromScan.DEFAULT;
        default:
            throw new toolkit_lib_1.ToolkitError(`Unknown scan type: ${scanType}`);
    }
}
/**
 * Takes a generatedTemplateOutput objct and returns a boolean representing whether there are any warnings on any rescources.
 *
 * @param generatedTemplateOutput - A GenerateTemplateOutput object
 * @returns A boolean representing whether there are any warnings on any rescources
 */
function isThereAWarning(generatedTemplateOutput) {
    if (generatedTemplateOutput.resources) {
        for (const resource of generatedTemplateOutput.resources) {
            if (resource.Warnings && resource.Warnings.length > 0) {
                return true;
            }
        }
    }
    return false;
}
/**
 * Builds the GenerateTemplateOutput object from the DescribeGeneratedTemplateOutput and the template body.
 *
 * @param generatedTemplateSummary - The output of the describe generated template call
 * @param templateBody - The body of the generated template
 * @returns A GenerateTemplateOutput object
 */
function buildGenertedTemplateOutput(generatedTemplateSummary, templateBody, source) {
    const resources = generatedTemplateSummary.Resources;
    const migrateJson = {
        templateBody: templateBody,
        source: source,
        resources: generatedTemplateSummary.Resources.map((r) => ({
            ResourceType: r.ResourceType,
            LogicalResourceId: r.LogicalResourceId,
            ResourceIdentifier: r.ResourceIdentifier,
        })),
    };
    const templateId = generatedTemplateSummary.GeneratedTemplateId;
    return {
        migrateJson: migrateJson,
        resources: resources,
        templateId: templateId,
    };
}
/**
 * Builds a CloudFormation sdk client for making requests with the CFN template generator.
 *
 * @param sdkProvider - The sdk provider for making CloudFormation calls
 * @param environment - The account and region where the stack is deployed
 * @returns A CloudFormation sdk client
 */
async function buildCfnClient(sdkProvider, environment) {
    const sdk = (await sdkProvider.forEnvironment(environment, plugin_1.Mode.ForReading)).sdk;
    sdk.appendCustomUserAgent('cdk-migrate');
    return sdk.cloudFormation();
}
/**
 * Appends a list of warnings to a readme file.
 *
 * @param filepath - The path to the readme file
 * @param resources - A list of resources to append warnings for
 */
function appendWarningsToReadme(filepath, resources) {
    const readme = fs.readFileSync(filepath, 'utf8');
    const lines = readme.split('\n');
    const index = lines.findIndex((line) => line.trim() === 'Enjoy!');
    let linesToAdd = ['\n## Warnings'];
    linesToAdd.push('### Write-only properties');
    linesToAdd.push("Write-only properties are resource property values that can be written to but can't be read by AWS CloudFormation or CDK Migrate. For more information, see [IaC generator and write-only properties](https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/generate-IaC-write-only-properties.html).");
    linesToAdd.push('\n');
    linesToAdd.push('Write-only properties discovered during migration are organized here by resource ID and categorized by write-only property type. Resolve write-only properties by providing property values in your CDK app. For guidance, see [Resolve write-only properties](https://docs.aws.amazon.com/cdk/v2/guide/migrate.html#migrate-resources-writeonly).');
    for (const resource of resources) {
        if (resource.Warnings && resource.Warnings.length > 0) {
            linesToAdd.push(`### ${resource.LogicalResourceId}`);
            for (const warning of resource.Warnings) {
                linesToAdd.push(`- **${warning.Type}**: `);
                for (const property of warning.Properties) {
                    linesToAdd.push(`  - ${property.PropertyPath}: ${property.Description}`);
                }
            }
        }
    }
    lines.splice(index, 0, ...linesToAdd);
    fs.writeFileSync(filepath, lines.join('\n'));
}
/**
 * takes a list of resources and returns a list of unique resources based on the resource type and logical resource id.
 *
 * @param resources - A list of resources to deduplicate
 * @returns A list of unique resources
 */
function deduplicateResources(resources) {
    let uniqueResources = {};
    for (const resource of resources) {
        const key = Object.keys(resource.ResourceIdentifier)[0];
        // Creating our unique identifier using the resource type, the key, and the value of the resource identifier
        // The resource identifier is a combination of a key value pair defined by a resource's schema, and the resource type of the resource.
        const uniqueIdentifer = `${resource.ResourceType}:${key}:${resource.ResourceIdentifier[key]}`;
        uniqueResources[uniqueIdentifer] = resource;
    }
    return Object.values(uniqueResources);
}
/**
 * Class for making CloudFormation template generator calls
 */
class CfnTemplateGeneratorProvider {
    constructor(cfn) {
        this.cfn = cfn;
    }
    async checkForResourceScan(resourceScanSummaries, options, clientRequestToken) {
        if (!resourceScanSummaries || resourceScanSummaries.length === 0) {
            if (options.fromScan === FromScan.MOST_RECENT) {
                throw new toolkit_lib_1.ToolkitError('No scans found. Please either start a new scan with the `--from-scan` new or do not specify a `--from-scan` option.');
            }
            else {
                (0, logging_1.info)('No scans found. Initiating a new resource scan.');
                await this.startResourceScan(clientRequestToken);
            }
        }
    }
    /**
     * Retrieves a tokenized list of resources and their associated scan. If a token is present the function
     * will loop through all pages and combine them into a single list of ScannedRelatedResources
     *
     * @param scanId - scan id for the to list resources for
     * @param resources - A list of resources to find related resources for
     */
    async getResourceScanRelatedResources(scanId, resources) {
        let relatedResourceList = resources;
        // break the list of resources into chunks of 100 to avoid hitting the 100 resource limit
        for (const chunk of chunks(resources, 100)) {
            // get the first page of related resources
            const res = await this.cfn.listResourceScanRelatedResources({
                ResourceScanId: scanId,
                Resources: chunk,
            });
            // add the first page to the list
            relatedResourceList.push(...(res.RelatedResources ?? []));
            let nextToken = res.NextToken;
            // if there are more pages, cycle through them and add them to the list before moving on to the next chunk
            while (nextToken) {
                const nextRelatedResources = await this.cfn.listResourceScanRelatedResources({
                    ResourceScanId: scanId,
                    Resources: resourceIdentifiers(resources),
                    NextToken: nextToken,
                });
                nextToken = nextRelatedResources.NextToken;
                relatedResourceList.push(...(nextRelatedResources.RelatedResources ?? []));
            }
        }
        relatedResourceList = deduplicateResources(relatedResourceList);
        // prune the managedbystack flag off of them again.
        return process.env.MIGRATE_INTEG_TEST
            ? resourceIdentifiers(relatedResourceList)
            : resourceIdentifiers(excludeManaged(relatedResourceList));
    }
    /**
     * Kicks off a scan of a customers account, returning the scan id. A scan can take
     * 10 minutes or longer to complete. However this will return a scan id as soon as
     * the scan has begun.
     *
     * @returns A string representing the scan id
     */
    async startResourceScan(requestToken) {
        return (await this.cfn.startResourceScan({
            ClientRequestToken: requestToken,
        })).ResourceScanId;
    }
    /**
     * Gets the most recent scans a customer has completed
     *
     * @returns a list of resource scan summaries
     */
    async listResourceScans() {
        return this.cfn.listResourceScans();
    }
    /**
     * Retrieves a tokenized list of resources from a resource scan. If a token is present, this function
     * will loop through all pages and combine them into a single list of ScannedResource[].
     * Additionally will apply any filters provided by the customer.
     *
     * @param scanId - scan id for the to list resources for
     * @param filters - a string of filters in the format of key1=value1,key2=value2
     * @returns a combined list of all resources from the scan
     */
    async listResourceScanResources(scanId, filters = []) {
        let resourceList = [];
        let resourceScanInputs;
        if (filters.length > 0) {
            (0, logging_1.info)('Applying filters to resource scan.');
            for (const filter of filters) {
                const filterList = parseFilters(filter);
                resourceScanInputs = {
                    ResourceScanId: scanId,
                    ResourceIdentifier: filterList[FilterType.RESOURCE_IDENTIFIER],
                    ResourceTypePrefix: filterList[FilterType.RESOURCE_TYPE_PREFIX],
                    TagKey: filterList[FilterType.TAG_KEY],
                    TagValue: filterList[FilterType.TAG_VALUE],
                };
                const resources = await this.cfn.listResourceScanResources(resourceScanInputs);
                resourceList = resourceList.concat(resources.Resources ?? []);
                let nextToken = resources.NextToken;
                // cycle through the pages adding all resources to the list until we run out of pages
                while (nextToken) {
                    resourceScanInputs.NextToken = nextToken;
                    const nextResources = await this.cfn.listResourceScanResources(resourceScanInputs);
                    nextToken = nextResources.NextToken;
                    resourceList = resourceList.concat(nextResources.Resources ?? []);
                }
            }
        }
        else {
            (0, logging_1.info)('No filters provided. Retrieving all resources from scan.');
            resourceScanInputs = {
                ResourceScanId: scanId,
            };
            const resources = await this.cfn.listResourceScanResources(resourceScanInputs);
            resourceList = resourceList.concat(resources.Resources ?? []);
            let nextToken = resources.NextToken;
            // cycle through the pages adding all resources to the list until we run out of pages
            while (nextToken) {
                resourceScanInputs.NextToken = nextToken;
                const nextResources = await this.cfn.listResourceScanResources(resourceScanInputs);
                nextToken = nextResources.NextToken;
                resourceList = resourceList.concat(nextResources.Resources ?? []);
            }
        }
        if (resourceList.length === 0) {
            throw new toolkit_lib_1.ToolkitError(`No resources found with filters ${filters.join(' ')}. Please try again with different filters.`);
        }
        resourceList = deduplicateResources(resourceList);
        return process.env.MIGRATE_INTEG_TEST
            ? resourceIdentifiers(resourceList)
            : resourceIdentifiers(excludeManaged(resourceList));
    }
    /**
     * Retrieves information about a resource scan.
     *
     * @param scanId - scan id for the to list resources for
     * @returns information about the scan
     */
    async describeResourceScan(scanId) {
        return this.cfn.describeResourceScan({
            ResourceScanId: scanId,
        });
    }
    /**
     * Describes the current status of the template being generated.
     *
     * @param templateId - A string representing the template id
     * @returns DescribeGeneratedTemplateOutput an object containing the template status and results
     */
    async describeGeneratedTemplate(templateId) {
        const generatedTemplate = await this.cfn.describeGeneratedTemplate({
            GeneratedTemplateName: templateId,
        });
        if (generatedTemplate.Status == ScanStatus.FAILED) {
            throw new toolkit_lib_1.ToolkitError(generatedTemplate.StatusReason);
        }
        return generatedTemplate;
    }
    /**
     * Retrieves a completed generated cloudformation template from the template generator.
     *
     * @param templateId - A string representing the template id
     * @param cloudFormation - The CloudFormation sdk client to use
     * @returns DescribeGeneratedTemplateOutput an object containing the template status and body
     */
    async getGeneratedTemplate(templateId) {
        return this.cfn.getGeneratedTemplate({
            GeneratedTemplateName: templateId,
        });
    }
    /**
     * Kicks off a template generation for a set of resources.
     *
     * @param stackName - The name of the stack
     * @param resources - A list of resources to generate the template from
     * @returns CreateGeneratedTemplateOutput an object containing the template arn to query on later
     */
    async createGeneratedTemplate(stackName, resources) {
        const createTemplateOutput = await this.cfn.createGeneratedTemplate({
            Resources: resources,
            GeneratedTemplateName: stackName,
        });
        if (createTemplateOutput.GeneratedTemplateId === undefined) {
            throw new toolkit_lib_1.ToolkitError('CreateGeneratedTemplate failed to return an Arn.');
        }
        return createTemplateOutput;
    }
    /**
     * Deletes a generated template from the template generator.
     *
     * @param templateArn - The arn of the template to delete
     * @returns A promise that resolves when the template has been deleted
     */
    async deleteGeneratedTemplate(templateArn) {
        await this.cfn.deleteGeneratedTemplate({
            GeneratedTemplateName: templateArn,
        });
    }
}
exports.CfnTemplateGeneratorProvider = CfnTemplateGeneratorProvider;
/**
 * The possible ways to choose a scan to generate a CDK application from
 */
var FromScan;
(function (FromScan) {
    /**
     * Initiate a new resource scan to build the CDK application from.
     */
    FromScan[FromScan["NEW"] = 0] = "NEW";
    /**
     * Use the last successful scan to build the CDK application from. Will fail if no scan is found.
     */
    FromScan[FromScan["MOST_RECENT"] = 1] = "MOST_RECENT";
    /**
     * Starts a scan if none exists, otherwise uses the most recent successful scan to build the CDK application from.
     */
    FromScan[FromScan["DEFAULT"] = 2] = "DEFAULT";
})(FromScan || (exports.FromScan = FromScan = {}));
//# sourceMappingURL=data:application/json;base64,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