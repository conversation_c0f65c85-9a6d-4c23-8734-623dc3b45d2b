"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.invokeBuiltinHooks = invokeBuiltinHooks;
const path = require("path");
const toolkit_lib_1 = require("@aws-cdk/toolkit-lib");
const os_1 = require("./os");
const util_1 = require("../../util");
/**
 * Invoke hooks for the given init template
 *
 * Sometimes templates need more complex logic than just replacing tokens. A 'hook' can be
 * used to do additional processing other than copying files.
 *
 * Hooks used to be defined externally to the CLI, by running arbitrarily
 * substituted shell scripts in the target directory.
 *
 * In practice, they're all TypeScript files and all the same, and the dynamism
 * that the original solution allowed wasn't used at all. Worse, since the CLI
 * is now bundled the hooks can't even reuse code from the CLI libraries at all
 * anymore, so all shared code would have to be copy/pasted.
 *
 * Bundle hooks as built-ins into the CLI, so they get bundled and can take advantage
 * of all shared code.
 */
async function invokeBuiltinHooks(target, context) {
    switch (target.language) {
        case 'csharp':
            if (['app', 'sample-app'].includes(target.templateName)) {
                return dotnetAddProject(target.targetDirectory, context);
            }
            break;
        case 'fsharp':
            if (['app', 'sample-app'].includes(target.templateName)) {
                return dotnetAddProject(target.targetDirectory, context, 'fsproj');
            }
            break;
        case 'python':
            // We can't call this file 'requirements.template.txt' because Dependabot needs to be able to find it.
            // Therefore, keep the in-repo name but still substitute placeholders.
            await context.substitutePlaceholdersIn('requirements.txt');
            break;
        case 'java':
            // We can't call this file 'pom.template.xml'... for the same reason as Python above.
            await context.substitutePlaceholdersIn('pom.xml');
            break;
        case 'javascript':
        case 'typescript':
            // See above, but for 'package.json'.
            await context.substitutePlaceholdersIn('package.json');
    }
}
async function dotnetAddProject(targetDirectory, context, ext = 'csproj') {
    const pname = context.placeholder('name.PascalCased');
    const slnPath = path.join(targetDirectory, 'src', `${pname}.sln`);
    const csprojPath = path.join(targetDirectory, 'src', pname, `${pname}.${ext}`);
    // We retry this command a couple of times. It usually never fails, except on CI where
    // we sometimes see:
    //
    //   System.IO.IOException: The system cannot open the device or file specified. : 'NuGet-Migrations'
    //
    // This error can be caused by lack of permissions on a temporary directory,
    // but in our case it's intermittent so my guess is it is caused by multiple
    // invocations of the .NET CLI running in parallel, and trampling on each
    // other creating a Mutex. There is no fix, and it is annoyingly breaking our
    // CI regularly. Retry a couple of times to increase reliability.
    //
    // - https://github.com/dotnet/sdk/issues/43750
    // - https://github.com/dotnet/runtime/issues/80619
    // - https://github.com/dotnet/runtime/issues/91987
    const MAX_ATTEMPTS = 3;
    for (let attempt = 1;; attempt++) {
        try {
            await (0, os_1.shell)(['dotnet', 'sln', slnPath, 'add', csprojPath]);
            return;
        }
        catch (e) {
            if (attempt === MAX_ATTEMPTS) {
                throw new toolkit_lib_1.ToolkitError(`Could not add project ${pname}.${ext} to solution ${pname}.sln. ${(0, util_1.formatErrorMessage)(e)}`);
            }
            // Sleep for a bit then try again
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }
}
//# sourceMappingURL=data:application/json;base64,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