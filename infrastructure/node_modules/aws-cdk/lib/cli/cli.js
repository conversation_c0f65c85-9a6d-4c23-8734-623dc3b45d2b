"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.exec = exec;
exports.cli = cli;
/* eslint-disable @typescript-eslint/no-shadow */ // yargs
const cxapi = require("@aws-cdk/cx-api");
const toolkit_lib_1 = require("@aws-cdk/toolkit-lib");
const chalk = require("chalk");
const cdk_toolkit_1 = require("./cdk-toolkit");
const io_host_1 = require("./io-host");
const parse_command_line_arguments_1 = require("./parse-command-line-arguments");
const platform_warnings_1 = require("./platform-warnings");
const pretty_print_error_1 = require("./pretty-print-error");
const singleton_plugin_host_1 = require("./singleton-plugin-host");
const user_configuration_1 = require("./user-configuration");
const version = require("./version");
const api_private_1 = require("../../lib/api-private");
const api_1 = require("../api");
const aws_auth_1 = require("../api/aws-auth");
const bootstrap_1 = require("../api/bootstrap");
const deployments_1 = require("../api/deployments");
const hotswap_1 = require("../api/hotswap");
const context_1 = require("../commands/context");
const docs_1 = require("../commands/docs");
const doctor_1 = require("../commands/doctor");
const init_1 = require("../commands/init");
const migrate_1 = require("../commands/migrate");
const cxapp_1 = require("../cxapp");
const proxy_agent_1 = require("./proxy-agent");
if (!process.stdout.isTTY) {
    // Disable chalk color highlighting
    process.env.FORCE_COLOR = '0';
}
async function exec(args, synthesizer) {
    const argv = await (0, parse_command_line_arguments_1.parseCommandLineArguments)(args);
    const cmd = argv._[0];
    // if one -v, log at a DEBUG level
    // if 2 -v, log at a TRACE level
    let ioMessageLevel = 'info';
    if (argv.verbose) {
        switch (argv.verbose) {
            case 1:
                ioMessageLevel = 'debug';
                break;
            case 2:
            default:
                ioMessageLevel = 'trace';
                break;
        }
    }
    const ioHost = io_host_1.CliIoHost.instance({
        logLevel: ioMessageLevel,
        isTTY: process.stdout.isTTY,
        isCI: Boolean(argv.ci),
        currentAction: cmd,
        stackProgress: argv.progress,
    }, true);
    // Debug should always imply tracing
    if (argv.debug || argv.verbose > 2) {
        (0, aws_auth_1.setSdkTracing)(true);
    }
    else {
        // cli-lib-alpha needs to explicitly set in case it was enabled before
        (0, aws_auth_1.setSdkTracing)(false);
    }
    try {
        await (0, platform_warnings_1.checkForPlatformWarnings)();
    }
    catch (e) {
        await ioHost.defaults.debug(`Error while checking for platform warnings: ${e}`);
    }
    await ioHost.defaults.debug('CDK Toolkit CLI version:', version.displayVersion());
    await ioHost.defaults.debug('Command line arguments:', argv);
    const configuration = new user_configuration_1.Configuration({
        commandLineArguments: {
            ...argv,
            _: argv._, // TypeScript at its best
        },
    });
    await configuration.load();
    const ioHelper = (0, api_private_1.asIoHelper)(ioHost, ioHost.currentAction);
    // Always create and use ProxyAgent to support configuration via env vars
    const proxyAgent = await new proxy_agent_1.ProxyAgentProvider(ioHelper).create({
        proxyAddress: configuration.settings.get(['proxy']),
        caBundlePath: configuration.settings.get(['caBundlePath']),
    });
    const shouldDisplayNotices = configuration.settings.get(['notices']);
    // Notices either go to stderr, or nowhere
    ioHost.noticesDestination = shouldDisplayNotices ? 'stderr' : 'drop';
    const notices = api_1.Notices.create({
        ioHost,
        context: configuration.context,
        output: configuration.settings.get(['outdir']),
        httpOptions: { agent: proxyAgent },
        cliVersion: version.versionNumber(),
    });
    const refreshNotices = (async () => {
        // the cdk notices command has it's own refresh
        if (shouldDisplayNotices && cmd !== 'notices') {
            try {
                return await notices.refresh();
            }
            catch (e) {
                await ioHelper.defaults.debug(`Could not refresh notices: ${e}`);
            }
        }
    })();
    const sdkProvider = await aws_auth_1.SdkProvider.withAwsCliCompatibleDefaults({
        ioHelper,
        requestHandler: (0, aws_auth_1.sdkRequestHandler)(proxyAgent),
        logger: new aws_auth_1.IoHostSdkLogger((0, api_private_1.asIoHelper)(ioHost, ioHost.currentAction)),
        pluginHost: singleton_plugin_host_1.GLOBAL_PLUGIN_HOST,
    }, configuration.settings.get(['profile']));
    let outDirLock;
    const cloudExecutable = new cxapp_1.CloudExecutable({
        configuration,
        sdkProvider,
        synthesizer: synthesizer ??
            (async (aws, config) => {
                // Invoke 'execProgram', and copy the lock for the directory in the global
                // variable here. It will be released when the CLI exits. Locks are not re-entrant
                // so release it if we have to synthesize more than once (because of context lookups).
                await outDirLock?.release();
                const { assembly, lock } = await (0, cxapp_1.execProgram)(aws, ioHost.asIoHelper(), config);
                outDirLock = lock;
                return assembly;
            }),
        ioHelper: ioHost.asIoHelper(),
    });
    /** Function to load plug-ins, using configurations additively. */
    async function loadPlugins(...settings) {
        for (const source of settings) {
            const plugins = source.get(['plugin']) || [];
            for (const plugin of plugins) {
                await singleton_plugin_host_1.GLOBAL_PLUGIN_HOST.load(plugin, ioHost);
            }
        }
    }
    await loadPlugins(configuration.settings);
    if ((typeof cmd) !== 'string') {
        throw new toolkit_lib_1.ToolkitError(`First argument should be a string. Got: ${cmd} (${typeof cmd})`);
    }
    try {
        return await main(cmd, argv);
    }
    finally {
        // If we locked the 'cdk.out' directory, release it here.
        await outDirLock?.release();
        // Do PSAs here
        await version.displayVersionMessage();
        await refreshNotices;
        if (cmd === 'notices') {
            await notices.refresh({ force: true });
            await notices.display({
                includeAcknowledged: !argv.unacknowledged,
                showTotal: argv.unacknowledged,
            });
        }
        else if (cmd !== 'version') {
            await notices.display();
        }
    }
    async function main(command, args) {
        ioHost.currentAction = command;
        const toolkitStackName = api_1.ToolkitInfo.determineName(configuration.settings.get(['toolkitStackName']));
        await ioHost.defaults.debug(`Toolkit stack: ${chalk.bold(toolkitStackName)}`);
        const cloudFormation = new deployments_1.Deployments({
            sdkProvider,
            toolkitStackName,
            ioHelper: (0, api_private_1.asIoHelper)(ioHost, ioHost.currentAction),
        });
        if (args.all && args.STACKS) {
            throw new toolkit_lib_1.ToolkitError('You must either specify a list of Stacks or the `--all` argument');
        }
        args.STACKS = args.STACKS ?? (args.STACK ? [args.STACK] : []);
        args.ENVIRONMENTS = args.ENVIRONMENTS ?? [];
        const selector = {
            allTopLevel: args.all,
            patterns: args.STACKS,
        };
        const cli = new cdk_toolkit_1.CdkToolkit({
            ioHost,
            cloudExecutable,
            toolkitStackName,
            deployments: cloudFormation,
            verbose: argv.trace || argv.verbose > 0,
            ignoreErrors: argv['ignore-errors'],
            strict: argv.strict,
            configuration,
            sdkProvider,
        });
        switch (command) {
            case 'context':
                ioHost.currentAction = 'context';
                return (0, context_1.contextHandler)({
                    context: configuration.context,
                    clear: argv.clear,
                    json: argv.json,
                    force: argv.force,
                    reset: argv.reset,
                });
            case 'docs':
            case 'doc':
                ioHost.currentAction = 'docs';
                return (0, docs_1.docs)({ browser: configuration.settings.get(['browser']) });
            case 'doctor':
                ioHost.currentAction = 'doctor';
                return (0, doctor_1.doctor)();
            case 'ls':
            case 'list':
                ioHost.currentAction = 'list';
                return cli.list(args.STACKS, {
                    long: args.long,
                    json: argv.json,
                    showDeps: args.showDependencies,
                });
            case 'diff':
                ioHost.currentAction = 'diff';
                const enableDiffNoFail = isFeatureEnabled(configuration, cxapi.ENABLE_DIFF_NO_FAIL_CONTEXT);
                return cli.diff({
                    stackNames: args.STACKS,
                    exclusively: args.exclusively,
                    templatePath: args.template,
                    strict: args.strict,
                    contextLines: args.contextLines,
                    securityOnly: args.securityOnly,
                    fail: args.fail != null ? args.fail : !enableDiffNoFail,
                    compareAgainstProcessedTemplate: args.processed,
                    quiet: args.quiet,
                    changeSet: args['change-set'],
                    toolkitStackName: toolkitStackName,
                    importExistingResources: args.importExistingResources,
                });
            case 'drift':
                ioHost.currentAction = 'drift';
                return cli.drift({
                    selector,
                    fail: args.fail,
                });
            case 'refactor':
                if (!configuration.settings.get(['unstable']).includes('refactor')) {
                    throw new toolkit_lib_1.ToolkitError('Unstable feature use: \'refactor\' is unstable. It must be opted in via \'--unstable\', e.g. \'cdk refactor --unstable=refactor\'');
                }
                ioHost.currentAction = 'refactor';
                return cli.refactor({
                    dryRun: args.dryRun,
                    selector,
                    excludeFile: args.excludeFile,
                    mappingFile: args.mappingFile,
                    revert: args.revert,
                });
            case 'bootstrap':
                ioHost.currentAction = 'bootstrap';
                const source = await determineBootstrapVersion(ioHost, args);
                if (args.showTemplate) {
                    const bootstrapper = new bootstrap_1.Bootstrapper(source, (0, api_private_1.asIoHelper)(ioHost, ioHost.currentAction));
                    return bootstrapper.showTemplate(args.json);
                }
                return cli.bootstrap(args.ENVIRONMENTS, {
                    source,
                    roleArn: args.roleArn,
                    forceDeployment: argv.force,
                    toolkitStackName: toolkitStackName,
                    execute: args.execute,
                    tags: configuration.settings.get(['tags']),
                    terminationProtection: args.terminationProtection,
                    usePreviousParameters: args['previous-parameters'],
                    parameters: {
                        bucketName: configuration.settings.get(['toolkitBucket', 'bucketName']),
                        kmsKeyId: configuration.settings.get(['toolkitBucket', 'kmsKeyId']),
                        createCustomerMasterKey: args.bootstrapCustomerKey,
                        qualifier: args.qualifier ?? configuration.context.get('@aws-cdk/core:bootstrapQualifier'),
                        publicAccessBlockConfiguration: args.publicAccessBlockConfiguration,
                        examplePermissionsBoundary: argv.examplePermissionsBoundary,
                        customPermissionsBoundary: argv.customPermissionsBoundary,
                        trustedAccounts: arrayFromYargs(args.trust),
                        trustedAccountsForLookup: arrayFromYargs(args.trustForLookup),
                        untrustedAccounts: arrayFromYargs(args.untrust),
                        cloudFormationExecutionPolicies: arrayFromYargs(args.cloudformationExecutionPolicies),
                    },
                });
            case 'deploy':
                ioHost.currentAction = 'deploy';
                const parameterMap = {};
                for (const parameter of args.parameters) {
                    if (typeof parameter === 'string') {
                        const keyValue = parameter.split('=');
                        parameterMap[keyValue[0]] = keyValue.slice(1).join('=');
                    }
                }
                if (args.execute !== undefined && args.method !== undefined) {
                    throw new toolkit_lib_1.ToolkitError('Can not supply both --[no-]execute and --method at the same time');
                }
                return cli.deploy({
                    selector,
                    exclusively: args.exclusively,
                    toolkitStackName,
                    roleArn: args.roleArn,
                    notificationArns: args.notificationArns,
                    requireApproval: configuration.settings.get(['requireApproval']),
                    reuseAssets: args['build-exclude'],
                    tags: configuration.settings.get(['tags']),
                    deploymentMethod: determineDeploymentMethod(args, configuration),
                    force: args.force,
                    parameters: parameterMap,
                    usePreviousParameters: args['previous-parameters'],
                    outputsFile: configuration.settings.get(['outputsFile']),
                    progress: configuration.settings.get(['progress']),
                    ci: args.ci,
                    rollback: configuration.settings.get(['rollback']),
                    watch: args.watch,
                    traceLogs: args.logs,
                    concurrency: args.concurrency,
                    assetParallelism: configuration.settings.get(['assetParallelism']),
                    assetBuildTime: configuration.settings.get(['assetPrebuild'])
                        ? cdk_toolkit_1.AssetBuildTime.ALL_BEFORE_DEPLOY
                        : cdk_toolkit_1.AssetBuildTime.JUST_IN_TIME,
                    ignoreNoStacks: args.ignoreNoStacks,
                });
            case 'rollback':
                ioHost.currentAction = 'rollback';
                return cli.rollback({
                    selector,
                    toolkitStackName,
                    roleArn: args.roleArn,
                    force: args.force,
                    validateBootstrapStackVersion: args['validate-bootstrap-version'],
                    orphanLogicalIds: args.orphan,
                });
            case 'import':
                ioHost.currentAction = 'import';
                return cli.import({
                    selector,
                    toolkitStackName,
                    roleArn: args.roleArn,
                    deploymentMethod: {
                        method: 'change-set',
                        execute: args.execute,
                        changeSetName: args.changeSetName,
                    },
                    progress: configuration.settings.get(['progress']),
                    rollback: configuration.settings.get(['rollback']),
                    recordResourceMapping: args['record-resource-mapping'],
                    resourceMappingFile: args['resource-mapping'],
                    force: args.force,
                });
            case 'watch':
                ioHost.currentAction = 'watch';
                await cli.watch({
                    selector,
                    exclusively: args.exclusively,
                    toolkitStackName,
                    roleArn: args.roleArn,
                    reuseAssets: args['build-exclude'],
                    deploymentMethod: determineDeploymentMethod(args, configuration, true),
                    force: args.force,
                    progress: configuration.settings.get(['progress']),
                    rollback: configuration.settings.get(['rollback']),
                    traceLogs: args.logs,
                    concurrency: args.concurrency,
                });
                return;
            case 'destroy':
                ioHost.currentAction = 'destroy';
                return cli.destroy({
                    selector,
                    exclusively: args.exclusively,
                    force: args.force,
                    roleArn: args.roleArn,
                });
            case 'gc':
                ioHost.currentAction = 'gc';
                if (!configuration.settings.get(['unstable']).includes('gc')) {
                    throw new toolkit_lib_1.ToolkitError('Unstable feature use: \'gc\' is unstable. It must be opted in via \'--unstable\', e.g. \'cdk gc --unstable=gc\'');
                }
                return cli.garbageCollect(args.ENVIRONMENTS, {
                    action: args.action,
                    type: args.type,
                    rollbackBufferDays: args['rollback-buffer-days'],
                    createdBufferDays: args['created-buffer-days'],
                    bootstrapStackName: args.bootstrapStackName,
                    confirm: args.confirm,
                });
            case 'synthesize':
            case 'synth':
                ioHost.currentAction = 'synth';
                const quiet = configuration.settings.get(['quiet']) ?? args.quiet;
                if (args.exclusively) {
                    return cli.synth(args.STACKS, args.exclusively, quiet, args.validation, argv.json);
                }
                else {
                    return cli.synth(args.STACKS, true, quiet, args.validation, argv.json);
                }
            case 'notices':
                ioHost.currentAction = 'notices';
                // If the user explicitly asks for notices, they are now the primary output
                // of the command and they should go to stdout.
                ioHost.noticesDestination = 'stdout';
                // This is a valid command, but we're postponing its execution because displaying
                // notices automatically happens after every command.
                return;
            case 'metadata':
                ioHost.currentAction = 'metadata';
                return cli.metadata(args.STACK, argv.json);
            case 'acknowledge':
            case 'ack':
                ioHost.currentAction = 'notices';
                return cli.acknowledge(args.ID);
            case 'cli-telemetry':
                ioHost.currentAction = 'cli-telemetry';
                if (args.enable === undefined && args.disable === undefined) {
                    throw new toolkit_lib_1.ToolkitError('Must specify either \'--enable\' or \'--disable\'');
                }
                const enable = args.enable ?? !args.disable;
                return cli.cliTelemetry(enable);
            case 'init':
                ioHost.currentAction = 'init';
                const language = configuration.settings.get(['language']);
                if (args.list) {
                    return (0, init_1.printAvailableTemplates)(language);
                }
                else {
                    return (0, init_1.cliInit)({
                        type: args.TEMPLATE,
                        language,
                        canUseNetwork: undefined,
                        generateOnly: args.generateOnly,
                        libVersion: args.libVersion,
                    });
                }
            case 'migrate':
                ioHost.currentAction = 'migrate';
                return cli.migrate({
                    stackName: args['stack-name'],
                    fromPath: args['from-path'],
                    fromStack: args['from-stack'],
                    language: args.language,
                    outputPath: args['output-path'],
                    fromScan: (0, migrate_1.getMigrateScanType)(args['from-scan']),
                    filter: args.filter,
                    account: args.account,
                    region: args.region,
                    compress: args.compress,
                });
            case 'version':
                ioHost.currentAction = 'version';
                return ioHost.defaults.result(version.displayVersion());
            default:
                throw new toolkit_lib_1.ToolkitError('Unknown command: ' + command);
        }
    }
}
/**
 * Determine which version of bootstrapping
 */
async function determineBootstrapVersion(ioHost, args) {
    let source;
    if (args.template) {
        await ioHost.defaults.info(`Using bootstrapping template from ${args.template}`);
        source = { source: 'custom', templateFile: args.template };
    }
    else if (process.env.CDK_LEGACY_BOOTSTRAP) {
        await ioHost.defaults.info('CDK_LEGACY_BOOTSTRAP set, using legacy-style bootstrapping');
        source = { source: 'legacy' };
    }
    else {
        // in V2, the "new" bootstrapping is the default
        source = { source: 'default' };
    }
    return source;
}
function isFeatureEnabled(configuration, featureFlag) {
    return configuration.context.get(featureFlag) ?? cxapi.futureFlagDefault(featureFlag);
}
/**
 * Translate a Yargs input array to something that makes more sense in a programming language
 * model (telling the difference between absence and an empty array)
 *
 * - An empty array is the default case, meaning the user didn't pass any arguments. We return
 *   undefined.
 * - If the user passed a single empty string, they did something like `--array=`, which we'll
 *   take to mean they passed an empty array.
 */
function arrayFromYargs(xs) {
    if (xs.length === 0) {
        return undefined;
    }
    return xs.filter((x) => x !== '');
}
function determineDeploymentMethod(args, configuration, watch) {
    let deploymentMethod;
    switch (args.method) {
        case 'direct':
            if (args.changeSetName) {
                throw new toolkit_lib_1.ToolkitError('--change-set-name cannot be used with method=direct');
            }
            if (args.importExistingResources) {
                throw new toolkit_lib_1.ToolkitError('--import-existing-resources cannot be enabled with method=direct');
            }
            deploymentMethod = { method: 'direct' };
            break;
        case 'change-set':
            deploymentMethod = {
                method: 'change-set',
                execute: true,
                changeSetName: args.changeSetName,
                importExistingResources: args.importExistingResources,
            };
            break;
        case 'prepare-change-set':
            deploymentMethod = {
                method: 'change-set',
                execute: false,
                changeSetName: args.changeSetName,
                importExistingResources: args.importExistingResources,
            };
            break;
        case undefined:
        default:
            deploymentMethod = {
                method: 'change-set',
                execute: watch ? true : args.execute ?? true,
                changeSetName: args.changeSetName,
                importExistingResources: args.importExistingResources,
            };
            break;
    }
    const hotswapMode = determineHotswapMode(args.hotswap, args.hotswapFallback, watch);
    const hotswapProperties = configuration.settings.get(['hotswap']) || {};
    switch (hotswapMode) {
        case hotswap_1.HotswapMode.FALL_BACK:
            return {
                method: 'hotswap',
                properties: hotswapProperties,
                fallback: deploymentMethod,
            };
        case hotswap_1.HotswapMode.HOTSWAP_ONLY:
            return {
                method: 'hotswap',
                properties: hotswapProperties,
            };
        default:
        case hotswap_1.HotswapMode.FULL_DEPLOYMENT:
            return deploymentMethod;
    }
}
function determineHotswapMode(hotswap, hotswapFallback, watch) {
    if (hotswap && hotswapFallback) {
        throw new toolkit_lib_1.ToolkitError('Can not supply both --hotswap and --hotswap-fallback at the same time');
    }
    else if (!hotswap && !hotswapFallback) {
        if (hotswap === undefined && hotswapFallback === undefined) {
            return watch ? hotswap_1.HotswapMode.HOTSWAP_ONLY : hotswap_1.HotswapMode.FULL_DEPLOYMENT;
        }
        else if (hotswap === false || hotswapFallback === false) {
            return hotswap_1.HotswapMode.FULL_DEPLOYMENT;
        }
    }
    let hotswapMode;
    if (hotswap) {
        hotswapMode = hotswap_1.HotswapMode.HOTSWAP_ONLY;
        /* if (hotswapFallback)*/
    }
    else {
        hotswapMode = hotswap_1.HotswapMode.FALL_BACK;
    }
    return hotswapMode;
}
/* c8 ignore start */ // we never call this in unit tests
function cli(args = process.argv.slice(2)) {
    exec(args)
        .then(async (value) => {
        if (typeof value === 'number') {
            process.exitCode = value;
        }
    })
        .catch((err) => {
        // Log the stack trace if we're on a developer workstation. Otherwise this will be into a minified
        // file and the printed code line and stack trace are huge and useless.
        (0, pretty_print_error_1.prettyPrintError)(err, version.isDeveloperBuild());
        process.exitCode = 1;
    });
}
/* c8 ignore stop */
//# sourceMappingURL=data:application/json;base64,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