"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CdkToolkit = exports.AssetBuildTime = void 0;
exports.markTesting = markTesting;
const path = require("path");
const util_1 = require("util");
const cloud_assembly_schema_1 = require("@aws-cdk/cloud-assembly-schema");
const cxapi = require("@aws-cdk/cx-api");
const toolkit_lib_1 = require("@aws-cdk/toolkit-lib");
const chalk = require("chalk");
const chokidar = require("chokidar");
const fs = require("fs-extra");
const promptly = require("promptly");
const uuid = require("uuid");
const io_host_1 = require("./io-host");
const user_configuration_1 = require("./user-configuration");
const api_private_1 = require("../../lib/api-private");
const api_1 = require("../api");
const bootstrap_1 = require("../api/bootstrap");
const cloud_assembly_1 = require("../api/cloud-assembly");
const deploy_1 = require("../commands/deploy");
const list_stacks_1 = require("../commands/list-stacks");
const migrate_1 = require("../commands/migrate");
const cxapp_1 = require("../cxapp");
const logging_1 = require("../logging");
const util_2 = require("../util");
// Must use a require() otherwise esbuild complains about calling a namespace
// eslint-disable-next-line @typescript-eslint/no-require-imports,@typescript-eslint/consistent-type-imports
const pLimit = require('p-limit');
let TESTING = false;
function markTesting() {
    TESTING = true;
}
/**
 * When to build assets
 */
var AssetBuildTime;
(function (AssetBuildTime) {
    /**
     * Build all assets before deploying the first stack
     *
     * This is intended for expensive Docker image builds; so that if the Docker image build
     * fails, no stacks are unnecessarily deployed (with the attendant wait time).
     */
    AssetBuildTime["ALL_BEFORE_DEPLOY"] = "all-before-deploy";
    /**
     * Build assets just-in-time, before publishing
     */
    AssetBuildTime["JUST_IN_TIME"] = "just-in-time";
})(AssetBuildTime || (exports.AssetBuildTime = AssetBuildTime = {}));
/**
 * Custom implementation of the public Toolkit to integrate with the legacy CdkToolkit
 *
 * This overwrites how an sdkProvider is acquired
 * in favor of the one provided directly to CdkToolkit.
 */
class InternalToolkit extends toolkit_lib_1.Toolkit {
    constructor(sdkProvider, options) {
        super(options);
        this._sdkProvider = sdkProvider;
    }
    /**
     * Access to the AWS SDK
     * @internal
     */
    async sdkProvider(_action) {
        return this._sdkProvider;
    }
}
/**
 * Toolkit logic
 *
 * The toolkit runs the `cloudExecutable` to obtain a cloud assembly and
 * deploys applies them to `cloudFormation`.
 */
class CdkToolkit {
    constructor(props) {
        this.props = props;
        this.ioHost = props.ioHost ?? io_host_1.CliIoHost.instance();
        this.toolkitStackName = props.toolkitStackName ?? api_1.DEFAULT_TOOLKIT_STACK_NAME;
        this.toolkit = new InternalToolkit(props.sdkProvider, {
            assemblyFailureAt: this.validateMetadataFailAt(),
            color: true,
            emojis: true,
            ioHost: this.ioHost,
            toolkitStackName: this.toolkitStackName,
            unstableFeatures: ['refactor'],
        });
    }
    async metadata(stackName, json) {
        const stacks = await this.selectSingleStackByName(stackName);
        printSerializedObject(stacks.firstStack.manifest.metadata ?? {}, json);
    }
    async acknowledge(noticeId) {
        const acks = this.props.configuration.context.get('acknowledged-issue-numbers') ?? [];
        acks.push(Number(noticeId));
        this.props.configuration.context.set('acknowledged-issue-numbers', acks);
        await this.props.configuration.saveContext();
    }
    async cliTelemetry(enable) {
        this.props.configuration.context.set('cli-telemetry', enable);
        await this.props.configuration.saveContext();
        (0, logging_1.info)(`Telemetry ${enable ? 'enabled' : 'disabled'}`);
    }
    async diff(options) {
        const stacks = await this.selectStacksForDiff(options.stackNames, options.exclusively);
        const strict = !!options.strict;
        const contextLines = options.contextLines || 3;
        const quiet = options.quiet || false;
        let diffs = 0;
        const parameterMap = buildParameterMap(options.parameters);
        if (options.templatePath !== undefined) {
            // Compare single stack against fixed template
            if (stacks.stackCount !== 1) {
                throw new toolkit_lib_1.ToolkitError('Can only select one stack when comparing to fixed template. Use --exclusively to avoid selecting multiple stacks.');
            }
            if (!(await fs.pathExists(options.templatePath))) {
                throw new toolkit_lib_1.ToolkitError(`There is no file at ${options.templatePath}`);
            }
            if (options.importExistingResources) {
                throw new toolkit_lib_1.ToolkitError('Can only use --import-existing-resources flag when comparing against deployed stacks.');
            }
            const template = (0, util_2.deserializeStructure)(await fs.readFile(options.templatePath, { encoding: 'UTF-8' }));
            const formatter = new api_1.DiffFormatter({
                templateInfo: {
                    oldTemplate: template,
                    newTemplate: stacks.firstStack,
                },
            });
            if (options.securityOnly) {
                const securityDiff = formatter.formatSecurityDiff();
                // Warn, count, and display the diff only if the reported changes are broadening permissions
                if (securityDiff.permissionChangeType === toolkit_lib_1.PermissionChangeType.BROADENING) {
                    (0, logging_1.warning)('This deployment will make potentially sensitive changes according to your current security approval level.\nPlease confirm you intend to make the following modifications:\n');
                    (0, logging_1.info)(securityDiff.formattedDiff);
                    diffs += 1;
                }
            }
            else {
                const diff = formatter.formatStackDiff({
                    strict,
                    contextLines,
                    quiet,
                });
                diffs = diff.numStacksWithChanges;
                (0, logging_1.info)(diff.formattedDiff);
            }
        }
        else {
            // Compare N stacks against deployed templates
            for (const stack of stacks.stackArtifacts) {
                const templateWithNestedStacks = await this.props.deployments.readCurrentTemplateWithNestedStacks(stack, options.compareAgainstProcessedTemplate);
                const currentTemplate = templateWithNestedStacks.deployedRootTemplate;
                const nestedStacks = templateWithNestedStacks.nestedStacks;
                const migrator = new api_1.ResourceMigrator({
                    deployments: this.props.deployments,
                    ioHelper: (0, api_private_1.asIoHelper)(this.ioHost, 'diff'),
                });
                const resourcesToImport = await migrator.tryGetResources(await this.props.deployments.resolveEnvironment(stack));
                if (resourcesToImport) {
                    (0, api_1.removeNonImportResources)(stack);
                }
                let changeSet = undefined;
                if (options.changeSet) {
                    let stackExists = false;
                    try {
                        stackExists = await this.props.deployments.stackExists({
                            stack,
                            deployName: stack.stackName,
                            tryLookupRole: true,
                        });
                    }
                    catch (e) {
                        (0, logging_1.debug)((0, util_2.formatErrorMessage)(e));
                        if (!quiet) {
                            (0, logging_1.info)(`Checking if the stack ${stack.stackName} exists before creating the changeset has failed, will base the diff on template differences (run again with -v to see the reason)\n`);
                        }
                        stackExists = false;
                    }
                    if (stackExists) {
                        changeSet = await api_private_1.cfnApi.createDiffChangeSet((0, api_private_1.asIoHelper)(this.ioHost, 'diff'), {
                            stack,
                            uuid: uuid.v4(),
                            deployments: this.props.deployments,
                            willExecute: false,
                            sdkProvider: this.props.sdkProvider,
                            parameters: Object.assign({}, parameterMap['*'], parameterMap[stack.stackName]),
                            resourcesToImport,
                            importExistingResources: options.importExistingResources,
                        });
                    }
                    else {
                        (0, logging_1.debug)(`the stack '${stack.stackName}' has not been deployed to CloudFormation or describeStacks call failed, skipping changeset creation.`);
                    }
                }
                const formatter = new api_1.DiffFormatter({
                    templateInfo: {
                        oldTemplate: currentTemplate,
                        newTemplate: stack,
                        changeSet,
                        isImport: !!resourcesToImport,
                        nestedStacks,
                    },
                });
                if (options.securityOnly) {
                    const securityDiff = formatter.formatSecurityDiff();
                    // Warn, count, and display the diff only if the reported changes are broadening permissions
                    if (securityDiff.permissionChangeType === toolkit_lib_1.PermissionChangeType.BROADENING) {
                        (0, logging_1.warning)('This deployment will make potentially sensitive changes according to your current security approval level.\nPlease confirm you intend to make the following modifications:\n');
                        (0, logging_1.info)(securityDiff.formattedDiff);
                        diffs += 1;
                    }
                }
                else {
                    const diff = formatter.formatStackDiff({
                        strict,
                        contextLines,
                        quiet,
                    });
                    (0, logging_1.info)(diff.formattedDiff);
                    diffs += diff.numStacksWithChanges;
                }
            }
        }
        (0, logging_1.info)((0, util_1.format)('\n✨  Number of stacks with differences: %s\n', diffs));
        return diffs && options.fail ? 1 : 0;
    }
    async deploy(options) {
        if (options.watch) {
            return this.watch(options);
        }
        // set progress from options, this includes user and app config
        if (options.progress) {
            this.ioHost.stackProgress = options.progress;
        }
        const startSynthTime = new Date().getTime();
        const stackCollection = await this.selectStacksForDeploy(options.selector, options.exclusively, options.cacheCloudAssembly, options.ignoreNoStacks);
        const elapsedSynthTime = new Date().getTime() - startSynthTime;
        (0, logging_1.info)(`\n✨  Synthesis time: ${(0, util_2.formatTime)(elapsedSynthTime)}s\n`);
        if (stackCollection.stackCount === 0) {
            (0, logging_1.error)('This app contains no stacks');
            return;
        }
        const migrator = new api_1.ResourceMigrator({
            deployments: this.props.deployments,
            ioHelper: (0, api_private_1.asIoHelper)(this.ioHost, 'deploy'),
        });
        await migrator.tryMigrateResources(stackCollection, {
            toolkitStackName: this.toolkitStackName,
            ...options,
        });
        const requireApproval = options.requireApproval ?? cloud_assembly_schema_1.RequireApproval.BROADENING;
        const parameterMap = buildParameterMap(options.parameters);
        if (options.deploymentMethod?.method === 'hotswap') {
            (0, logging_1.warning)('⚠️ The --hotswap and --hotswap-fallback flags deliberately introduce CloudFormation drift to speed up deployments');
            (0, logging_1.warning)('⚠️ They should only be used for development - never use them for your production Stacks!\n');
        }
        const stacks = stackCollection.stackArtifacts;
        const stackOutputs = {};
        const outputsFile = options.outputsFile;
        const buildAsset = async (assetNode) => {
            await this.props.deployments.buildSingleAsset(assetNode.assetManifestArtifact, assetNode.assetManifest, assetNode.asset, {
                stack: assetNode.parentStack,
                roleArn: options.roleArn,
                stackName: assetNode.parentStack.stackName,
            });
        };
        const publishAsset = async (assetNode) => {
            await this.props.deployments.publishSingleAsset(assetNode.assetManifest, assetNode.asset, {
                stack: assetNode.parentStack,
                roleArn: options.roleArn,
                stackName: assetNode.parentStack.stackName,
                forcePublish: options.force,
            });
        };
        const deployStack = async (stackNode) => {
            const stack = stackNode.stack;
            if (stackCollection.stackCount !== 1) {
                (0, logging_1.highlight)(stack.displayName);
            }
            if (!stack.environment) {
                // eslint-disable-next-line @stylistic/max-len
                throw new toolkit_lib_1.ToolkitError(`Stack ${stack.displayName} does not define an environment, and AWS credentials could not be obtained from standard locations or no region was configured.`);
            }
            if (Object.keys(stack.template.Resources || {}).length === 0) {
                // The generated stack has no resources
                if (!(await this.props.deployments.stackExists({ stack }))) {
                    (0, logging_1.warning)('%s: stack has no resources, skipping deployment.', chalk.bold(stack.displayName));
                }
                else {
                    (0, logging_1.warning)('%s: stack has no resources, deleting existing stack.', chalk.bold(stack.displayName));
                    await this.destroy({
                        selector: { patterns: [stack.hierarchicalId] },
                        exclusively: true,
                        force: true,
                        roleArn: options.roleArn,
                        fromDeploy: true,
                    });
                }
                return;
            }
            if (requireApproval !== cloud_assembly_schema_1.RequireApproval.NEVER) {
                const currentTemplate = await this.props.deployments.readCurrentTemplate(stack);
                const formatter = new api_1.DiffFormatter({
                    templateInfo: {
                        oldTemplate: currentTemplate,
                        newTemplate: stack,
                    },
                });
                const securityDiff = formatter.formatSecurityDiff();
                if (requiresApproval(requireApproval, securityDiff.permissionChangeType)) {
                    (0, logging_1.info)(securityDiff.formattedDiff);
                    await askUserConfirmation(this.ioHost, concurrency, '"--require-approval" is enabled and stack includes security-sensitive updates', 'Do you wish to deploy these changes');
                }
            }
            // Following are the same semantics we apply with respect to Notification ARNs (dictated by the SDK)
            //
            //  - undefined  =>  cdk ignores it, as if it wasn't supported (allows external management).
            //  - []:        =>  cdk manages it, and the user wants to wipe it out.
            //  - ['arn-1']  =>  cdk manages it, and the user wants to set it to ['arn-1'].
            const notificationArns = (!!options.notificationArns || !!stack.notificationArns)
                ? (options.notificationArns ?? []).concat(stack.notificationArns ?? [])
                : undefined;
            for (const notificationArn of notificationArns ?? []) {
                if (!(0, util_2.validateSnsTopicArn)(notificationArn)) {
                    throw new toolkit_lib_1.ToolkitError(`Notification arn ${notificationArn} is not a valid arn for an SNS topic`);
                }
            }
            const stackIndex = stacks.indexOf(stack) + 1;
            (0, logging_1.info)(`${chalk.bold(stack.displayName)}: deploying... [${stackIndex}/${stackCollection.stackCount}]`);
            const startDeployTime = new Date().getTime();
            let tags = options.tags;
            if (!tags || tags.length === 0) {
                tags = (0, api_private_1.tagsForStack)(stack);
            }
            let elapsedDeployTime = 0;
            try {
                let deployResult;
                let rollback = options.rollback;
                let iteration = 0;
                while (!deployResult) {
                    if (++iteration > 2) {
                        throw new toolkit_lib_1.ToolkitError('This loop should have stabilized in 2 iterations, but didn\'t. If you are seeing this error, please report it at https://github.com/aws/aws-cdk/issues/new/choose');
                    }
                    const r = await this.props.deployments.deployStack({
                        stack,
                        deployName: stack.stackName,
                        roleArn: options.roleArn,
                        toolkitStackName: options.toolkitStackName,
                        reuseAssets: options.reuseAssets,
                        notificationArns,
                        tags,
                        execute: options.execute,
                        changeSetName: options.changeSetName,
                        deploymentMethod: options.deploymentMethod,
                        forceDeployment: options.force,
                        parameters: Object.assign({}, parameterMap['*'], parameterMap[stack.stackName]),
                        usePreviousParameters: options.usePreviousParameters,
                        rollback,
                        extraUserAgent: options.extraUserAgent,
                        assetParallelism: options.assetParallelism,
                        ignoreNoStacks: options.ignoreNoStacks,
                    });
                    switch (r.type) {
                        case 'did-deploy-stack':
                            deployResult = r;
                            break;
                        case 'failpaused-need-rollback-first': {
                            const motivation = r.reason === 'replacement'
                                ? `Stack is in a paused fail state (${r.status}) and change includes a replacement which cannot be deployed with "--no-rollback"`
                                : `Stack is in a paused fail state (${r.status}) and command line arguments do not include "--no-rollback"`;
                            if (options.force) {
                                (0, logging_1.warning)(`${motivation}. Rolling back first (--force).`);
                            }
                            else {
                                await askUserConfirmation(this.ioHost, concurrency, motivation, `${motivation}. Roll back first and then proceed with deployment`);
                            }
                            // Perform a rollback
                            await this.rollback({
                                selector: { patterns: [stack.hierarchicalId] },
                                toolkitStackName: options.toolkitStackName,
                                force: options.force,
                            });
                            // Go around through the 'while' loop again but switch rollback to true.
                            rollback = true;
                            break;
                        }
                        case 'replacement-requires-rollback': {
                            const motivation = 'Change includes a replacement which cannot be deployed with "--no-rollback"';
                            if (options.force) {
                                (0, logging_1.warning)(`${motivation}. Proceeding with regular deployment (--force).`);
                            }
                            else {
                                await askUserConfirmation(this.ioHost, concurrency, motivation, `${motivation}. Perform a regular deployment`);
                            }
                            // Go around through the 'while' loop again but switch rollback to true.
                            rollback = true;
                            break;
                        }
                        default:
                            throw new toolkit_lib_1.ToolkitError(`Unexpected result type from deployStack: ${JSON.stringify(r)}. If you are seeing this error, please report it at https://github.com/aws/aws-cdk/issues/new/choose`);
                    }
                }
                const message = deployResult.noOp
                    ? ' ✅  %s (no changes)'
                    : ' ✅  %s';
                (0, logging_1.success)('\n' + message, stack.displayName);
                elapsedDeployTime = new Date().getTime() - startDeployTime;
                (0, logging_1.info)(`\n✨  Deployment time: ${(0, util_2.formatTime)(elapsedDeployTime)}s\n`);
                if (Object.keys(deployResult.outputs).length > 0) {
                    (0, logging_1.info)('Outputs:');
                    stackOutputs[stack.stackName] = deployResult.outputs;
                }
                for (const name of Object.keys(deployResult.outputs).sort()) {
                    const value = deployResult.outputs[name];
                    (0, logging_1.info)(`${chalk.cyan(stack.id)}.${chalk.cyan(name)} = ${chalk.underline(chalk.cyan(value))}`);
                }
                (0, logging_1.info)('Stack ARN:');
                (0, logging_1.result)(deployResult.stackArn);
            }
            catch (e) {
                // It has to be exactly this string because an integration test tests for
                // "bold(stackname) failed: ResourceNotReady: <error>"
                throw new toolkit_lib_1.ToolkitError([`❌  ${chalk.bold(stack.stackName)} failed:`, ...(e.name ? [`${e.name}:`] : []), (0, util_2.formatErrorMessage)(e)].join(' '));
            }
            finally {
                if (options.cloudWatchLogMonitor) {
                    const foundLogGroupsResult = await (0, api_1.findCloudWatchLogGroups)(this.props.sdkProvider, (0, api_private_1.asIoHelper)(this.ioHost, 'deploy'), stack);
                    options.cloudWatchLogMonitor.addLogGroups(foundLogGroupsResult.env, foundLogGroupsResult.sdk, foundLogGroupsResult.logGroupNames);
                }
                // If an outputs file has been specified, create the file path and write stack outputs to it once.
                // Outputs are written after all stacks have been deployed. If a stack deployment fails,
                // all of the outputs from successfully deployed stacks before the failure will still be written.
                if (outputsFile) {
                    fs.ensureFileSync(outputsFile);
                    await fs.writeJson(outputsFile, stackOutputs, {
                        spaces: 2,
                        encoding: 'utf8',
                    });
                }
            }
            (0, logging_1.info)(`\n✨  Total time: ${(0, util_2.formatTime)(elapsedSynthTime + elapsedDeployTime)}s\n`);
        };
        const assetBuildTime = options.assetBuildTime ?? AssetBuildTime.ALL_BEFORE_DEPLOY;
        const prebuildAssets = assetBuildTime === AssetBuildTime.ALL_BEFORE_DEPLOY;
        const concurrency = options.concurrency || 1;
        if (concurrency > 1) {
            // always force "events" progress output when we have concurrency
            this.ioHost.stackProgress = deploy_1.StackActivityProgress.EVENTS;
            // ...but only warn if the user explicitly requested "bar" progress
            if (options.progress && options.progress != deploy_1.StackActivityProgress.EVENTS) {
                (0, logging_1.warning)('⚠️ The --concurrency flag only supports --progress "events". Switching to "events".');
            }
        }
        const stacksAndTheirAssetManifests = stacks.flatMap((stack) => [
            stack,
            ...stack.dependencies.filter(x => cxapi.AssetManifestArtifact.isAssetManifestArtifact(x)),
        ]);
        const workGraph = new api_1.WorkGraphBuilder((0, api_private_1.asIoHelper)(this.ioHost, 'deploy'), prebuildAssets).build(stacksAndTheirAssetManifests);
        // Unless we are running with '--force', skip already published assets
        if (!options.force) {
            await this.removePublishedAssets(workGraph, options);
        }
        const graphConcurrency = {
            'stack': concurrency,
            'asset-build': 1, // This will be CPU-bound/memory bound, mostly matters for Docker builds
            'asset-publish': (options.assetParallelism ?? true) ? 8 : 1, // This will be I/O-bound, 8 in parallel seems reasonable
        };
        await workGraph.doParallel(graphConcurrency, {
            deployStack,
            buildAsset,
            publishAsset,
        });
    }
    /**
     * Detect infrastructure drift for the given stack(s)
     */
    async drift(options) {
        const driftResults = await this.toolkit.drift(this.props.cloudExecutable, {
            stacks: {
                patterns: options.selector.patterns,
                strategy: options.selector.patterns.length > 0 ? toolkit_lib_1.StackSelectionStrategy.PATTERN_MATCH : toolkit_lib_1.StackSelectionStrategy.ALL_STACKS,
            },
        });
        const totalDrifts = Object.values(driftResults).reduce((total, current) => total + (current.numResourcesWithDrift ?? 0), 0);
        return totalDrifts > 0 && options.fail ? 1 : 0;
    }
    /**
     * Roll back the given stack or stacks.
     */
    async rollback(options) {
        const startSynthTime = new Date().getTime();
        const stackCollection = await this.selectStacksForDeploy(options.selector, true);
        const elapsedSynthTime = new Date().getTime() - startSynthTime;
        (0, logging_1.info)(`\n✨  Synthesis time: ${(0, util_2.formatTime)(elapsedSynthTime)}s\n`);
        if (stackCollection.stackCount === 0) {
            (0, logging_1.error)('No stacks selected');
            return;
        }
        let anyRollbackable = false;
        for (const stack of stackCollection.stackArtifacts) {
            (0, logging_1.info)('Rolling back %s', chalk.bold(stack.displayName));
            const startRollbackTime = new Date().getTime();
            try {
                const result = await this.props.deployments.rollbackStack({
                    stack,
                    roleArn: options.roleArn,
                    toolkitStackName: options.toolkitStackName,
                    orphanFailedResources: options.force,
                    validateBootstrapStackVersion: options.validateBootstrapStackVersion,
                    orphanLogicalIds: options.orphanLogicalIds,
                });
                if (!result.notInRollbackableState) {
                    anyRollbackable = true;
                }
                const elapsedRollbackTime = new Date().getTime() - startRollbackTime;
                (0, logging_1.info)(`\n✨  Rollback time: ${(0, util_2.formatTime)(elapsedRollbackTime).toString()}s\n`);
            }
            catch (e) {
                (0, logging_1.error)('\n ❌  %s failed: %s', chalk.bold(stack.displayName), (0, util_2.formatErrorMessage)(e));
                throw new toolkit_lib_1.ToolkitError('Rollback failed (use --force to orphan failing resources)');
            }
        }
        if (!anyRollbackable) {
            throw new toolkit_lib_1.ToolkitError('No stacks were in a state that could be rolled back');
        }
    }
    async watch(options) {
        const rootDir = path.dirname(path.resolve(user_configuration_1.PROJECT_CONFIG));
        const ioHelper = (0, api_private_1.asIoHelper)(this.ioHost, 'watch');
        (0, logging_1.debug)("root directory used for 'watch' is: %s", rootDir);
        const watchSettings = this.props.configuration.settings.get(['watch']);
        if (!watchSettings) {
            throw new toolkit_lib_1.ToolkitError("Cannot use the 'watch' command without specifying at least one directory to monitor. " +
                'Make sure to add a "watch" key to your cdk.json');
        }
        // For the "include" subkey under the "watch" key, the behavior is:
        // 1. No "watch" setting? We error out.
        // 2. "watch" setting without an "include" key? We default to observing "./**".
        // 3. "watch" setting with an empty "include" key? We default to observing "./**".
        // 4. Non-empty "include" key? Just use the "include" key.
        const watchIncludes = this.patternsArrayForWatch(watchSettings.include, {
            rootDir,
            returnRootDirIfEmpty: true,
        });
        (0, logging_1.debug)("'include' patterns for 'watch': %s", watchIncludes);
        // For the "exclude" subkey under the "watch" key,
        // the behavior is to add some default excludes in addition to the ones specified by the user:
        // 1. The CDK output directory.
        // 2. Any file whose name starts with a dot.
        // 3. Any directory's content whose name starts with a dot.
        // 4. Any node_modules and its content (even if it's not a JS/TS project, you might be using a local aws-cli package)
        const outputDir = this.props.configuration.settings.get(['output']);
        const watchExcludes = this.patternsArrayForWatch(watchSettings.exclude, {
            rootDir,
            returnRootDirIfEmpty: false,
        }).concat(`${outputDir}/**`, '**/.*', '**/.*/**', '**/node_modules/**');
        (0, logging_1.debug)("'exclude' patterns for 'watch': %s", watchExcludes);
        // Since 'cdk deploy' is a relatively slow operation for a 'watch' process,
        // introduce a concurrency latch that tracks the state.
        // This way, if file change events arrive when a 'cdk deploy' is still executing,
        // we will batch them, and trigger another 'cdk deploy' after the current one finishes,
        // making sure 'cdk deploy's  always execute one at a time.
        // Here's a diagram showing the state transitions:
        // --------------                --------    file changed     --------------    file changed     --------------  file changed
        // |            |  ready event   |      | ------------------> |            | ------------------> |            | --------------|
        // | pre-ready  | -------------> | open |                     | deploying  |                     |   queued   |               |
        // |            |                |      | <------------------ |            | <------------------ |            | <-------------|
        // --------------                --------  'cdk deploy' done  --------------  'cdk deploy' done  --------------
        let latch = 'pre-ready';
        const cloudWatchLogMonitor = options.traceLogs ? new api_1.CloudWatchLogEventMonitor({
            ioHelper,
        }) : undefined;
        const deployAndWatch = async () => {
            latch = 'deploying';
            await cloudWatchLogMonitor?.deactivate();
            await this.invokeDeployFromWatch(options, cloudWatchLogMonitor);
            // If latch is still 'deploying' after the 'await', that's fine,
            // but if it's 'queued', that means we need to deploy again
            while (latch === 'queued') {
                // TypeScript doesn't realize latch can change between 'awaits',
                // and thinks the above 'while' condition is always 'false' without the cast
                latch = 'deploying';
                (0, logging_1.info)("Detected file changes during deployment. Invoking 'cdk deploy' again");
                await this.invokeDeployFromWatch(options, cloudWatchLogMonitor);
            }
            latch = 'open';
            await cloudWatchLogMonitor?.activate();
        };
        chokidar
            .watch(watchIncludes, {
            ignored: watchExcludes,
            cwd: rootDir,
        })
            .on('ready', async () => {
            latch = 'open';
            (0, logging_1.debug)("'watch' received the 'ready' event. From now on, all file changes will trigger a deployment");
            (0, logging_1.info)("Triggering initial 'cdk deploy'");
            await deployAndWatch();
        })
            .on('all', async (event, filePath) => {
            if (latch === 'pre-ready') {
                (0, logging_1.info)(`'watch' is observing ${event === 'addDir' ? 'directory' : 'the file'} '%s' for changes`, filePath);
            }
            else if (latch === 'open') {
                (0, logging_1.info)("Detected change to '%s' (type: %s). Triggering 'cdk deploy'", filePath, event);
                await deployAndWatch();
            }
            else {
                // this means latch is either 'deploying' or 'queued'
                latch = 'queued';
                (0, logging_1.info)("Detected change to '%s' (type: %s) while 'cdk deploy' is still running. " +
                    'Will queue for another deployment after this one finishes', filePath, event);
            }
        });
    }
    async import(options) {
        const stacks = await this.selectStacksForDeploy(options.selector, true, true, false);
        // set progress from options, this includes user and app config
        if (options.progress) {
            this.ioHost.stackProgress = options.progress;
        }
        if (stacks.stackCount > 1) {
            throw new toolkit_lib_1.ToolkitError(`Stack selection is ambiguous, please choose a specific stack for import [${stacks.stackArtifacts.map((x) => x.id).join(', ')}]`);
        }
        if (!process.stdout.isTTY && !options.resourceMappingFile) {
            throw new toolkit_lib_1.ToolkitError('--resource-mapping is required when input is not a terminal');
        }
        const stack = stacks.stackArtifacts[0];
        (0, logging_1.highlight)(stack.displayName);
        const resourceImporter = new api_1.ResourceImporter(stack, {
            deployments: this.props.deployments,
            ioHelper: (0, api_private_1.asIoHelper)(this.ioHost, 'import'),
        });
        const { additions, hasNonAdditions } = await resourceImporter.discoverImportableResources(options.force);
        if (additions.length === 0) {
            (0, logging_1.warning)('%s: no new resources compared to the currently deployed stack, skipping import.', chalk.bold(stack.displayName));
            return;
        }
        // Prepare a mapping of physical resources to CDK constructs
        const actualImport = !options.resourceMappingFile
            ? await resourceImporter.askForResourceIdentifiers(additions)
            : await resourceImporter.loadResourceIdentifiers(additions, options.resourceMappingFile);
        if (actualImport.importResources.length === 0) {
            (0, logging_1.warning)('No resources selected for import.');
            return;
        }
        // If "--create-resource-mapping" option was passed, write the resource mapping to the given file and exit
        if (options.recordResourceMapping) {
            const outputFile = options.recordResourceMapping;
            fs.ensureFileSync(outputFile);
            await fs.writeJson(outputFile, actualImport.resourceMap, {
                spaces: 2,
                encoding: 'utf8',
            });
            (0, logging_1.info)('%s: mapping file written.', outputFile);
            return;
        }
        // Import the resources according to the given mapping
        (0, logging_1.info)('%s: importing resources into stack...', chalk.bold(stack.displayName));
        const tags = (0, api_private_1.tagsForStack)(stack);
        await resourceImporter.importResourcesFromMap(actualImport, {
            roleArn: options.roleArn,
            tags,
            deploymentMethod: options.deploymentMethod,
            usePreviousParameters: true,
            rollback: options.rollback,
        });
        // Notify user of next steps
        (0, logging_1.info)(`Import operation complete. We recommend you run a ${chalk.blueBright('drift detection')} operation ` +
            'to confirm your CDK app resource definitions are up-to-date. Read more here: ' +
            chalk.underline.blueBright('https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/detect-drift-stack.html'));
        if (actualImport.importResources.length < additions.length) {
            (0, logging_1.info)('');
            (0, logging_1.warning)(`Some resources were skipped. Run another ${chalk.blueBright('cdk import')} or a ${chalk.blueBright('cdk deploy')} to bring the stack up-to-date with your CDK app definition.`);
        }
        else if (hasNonAdditions) {
            (0, logging_1.info)('');
            (0, logging_1.warning)(`Your app has pending updates or deletes excluded from this import operation. Run a ${chalk.blueBright('cdk deploy')} to bring the stack up-to-date with your CDK app definition.`);
        }
    }
    async destroy(options) {
        let stacks = await this.selectStacksForDestroy(options.selector, options.exclusively);
        // The stacks will have been ordered for deployment, so reverse them for deletion.
        stacks = stacks.reversed();
        if (!options.force) {
            // eslint-disable-next-line @stylistic/max-len
            const confirmed = await promptly.confirm(`Are you sure you want to delete: ${chalk.blue(stacks.stackArtifacts.map((s) => s.hierarchicalId).join(', '))} (y/n)?`);
            if (!confirmed) {
                return;
            }
        }
        const action = options.fromDeploy ? 'deploy' : 'destroy';
        for (const [index, stack] of stacks.stackArtifacts.entries()) {
            (0, logging_1.success)('%s: destroying... [%s/%s]', chalk.blue(stack.displayName), index + 1, stacks.stackCount);
            try {
                await this.props.deployments.destroyStack({
                    stack,
                    deployName: stack.stackName,
                    roleArn: options.roleArn,
                });
                (0, logging_1.success)(`\n ✅  %s: ${action}ed`, chalk.blue(stack.displayName));
            }
            catch (e) {
                (0, logging_1.error)(`\n ❌  %s: ${action} failed`, chalk.blue(stack.displayName), e);
                throw e;
            }
        }
    }
    async list(selectors, options = {}) {
        const stacks = await (0, list_stacks_1.listStacks)(this, {
            selectors: selectors,
        });
        if (options.long && options.showDeps) {
            printSerializedObject(stacks, options.json ?? false);
            return 0;
        }
        if (options.showDeps) {
            const stackDeps = [];
            for (const stack of stacks) {
                stackDeps.push({
                    id: stack.id,
                    dependencies: stack.dependencies,
                });
            }
            printSerializedObject(stackDeps, options.json ?? false);
            return 0;
        }
        if (options.long) {
            const long = [];
            for (const stack of stacks) {
                long.push({
                    id: stack.id,
                    name: stack.name,
                    environment: stack.environment,
                });
            }
            printSerializedObject(long, options.json ?? false);
            return 0;
        }
        // just print stack IDs
        for (const stack of stacks) {
            (0, logging_1.result)(stack.id);
        }
        return 0; // exit-code
    }
    /**
     * Synthesize the given set of stacks (called when the user runs 'cdk synth')
     *
     * INPUT: Stack names can be supplied using a glob filter. If no stacks are
     * given, all stacks from the application are implicitly selected.
     *
     * OUTPUT: If more than one stack ends up being selected, an output directory
     * should be supplied, where the templates will be written.
     */
    async synth(stackNames, exclusively, quiet, autoValidate, json) {
        const stacks = await this.selectStacksForDiff(stackNames, exclusively, autoValidate);
        // if we have a single stack, print it to STDOUT
        if (stacks.stackCount === 1) {
            if (!quiet) {
                printSerializedObject((0, util_2.obscureTemplate)(stacks.firstStack.template), json ?? false);
            }
            return undefined;
        }
        // not outputting template to stdout, let's explain things to the user a little bit...
        (0, logging_1.success)(`Successfully synthesized to ${chalk.blue(path.resolve(stacks.assembly.directory))}`);
        (0, logging_1.info)(`Supply a stack id (${stacks.stackArtifacts.map((s) => chalk.green(s.hierarchicalId)).join(', ')}) to display its template.`);
        return undefined;
    }
    /**
     * Bootstrap the CDK Toolkit stack in the accounts used by the specified stack(s).
     *
     * @param userEnvironmentSpecs - environment names that need to have toolkit support
     *             provisioned, as a glob filter. If none is provided, all stacks are implicitly selected.
     * @param options - The name, role ARN, bootstrapping parameters, etc. to be used for the CDK Toolkit stack.
     */
    async bootstrap(userEnvironmentSpecs, options) {
        const bootstrapper = new bootstrap_1.Bootstrapper(options.source, (0, api_private_1.asIoHelper)(this.ioHost, 'bootstrap'));
        // If there is an '--app' argument and an environment looks like a glob, we
        // select the environments from the app. Otherwise, use what the user said.
        const environments = await this.defineEnvironments(userEnvironmentSpecs);
        const limit = pLimit(20);
        // eslint-disable-next-line @cdklabs/promiseall-no-unbounded-parallelism
        await Promise.all(environments.map((environment) => limit(async () => {
            (0, logging_1.success)(' ⏳  Bootstrapping environment %s...', chalk.blue(environment.name));
            try {
                const result = await bootstrapper.bootstrapEnvironment(environment, this.props.sdkProvider, options);
                const message = result.noOp
                    ? ' ✅  Environment %s bootstrapped (no changes).'
                    : ' ✅  Environment %s bootstrapped.';
                (0, logging_1.success)(message, chalk.blue(environment.name));
            }
            catch (e) {
                (0, logging_1.error)(' ❌  Environment %s failed bootstrapping: %s', chalk.blue(environment.name), e);
                throw e;
            }
        })));
    }
    /**
     * Garbage collects assets from a CDK app's environment
     * @param options - Options for Garbage Collection
     */
    async garbageCollect(userEnvironmentSpecs, options) {
        const environments = await this.defineEnvironments(userEnvironmentSpecs);
        for (const environment of environments) {
            (0, logging_1.success)(' ⏳  Garbage Collecting environment %s...', chalk.blue(environment.name));
            const gc = new api_1.GarbageCollector({
                sdkProvider: this.props.sdkProvider,
                ioHelper: (0, api_private_1.asIoHelper)(this.ioHost, 'gc'),
                resolvedEnvironment: environment,
                bootstrapStackName: options.bootstrapStackName,
                rollbackBufferDays: options.rollbackBufferDays,
                createdBufferDays: options.createdBufferDays,
                action: options.action ?? 'full',
                type: options.type ?? 'all',
                confirm: options.confirm ?? true,
            });
            await gc.garbageCollect();
        }
    }
    async defineEnvironments(userEnvironmentSpecs) {
        // By default, glob for everything
        const environmentSpecs = userEnvironmentSpecs.length > 0 ? [...userEnvironmentSpecs] : ['**'];
        // Partition into globs and non-globs (this will mutate environmentSpecs).
        const globSpecs = (0, util_2.partition)(environmentSpecs, cxapp_1.looksLikeGlob);
        if (globSpecs.length > 0 && !this.props.cloudExecutable.hasApp) {
            if (userEnvironmentSpecs.length > 0) {
                // User did request this glob
                throw new toolkit_lib_1.ToolkitError(`'${globSpecs}' is not an environment name. Specify an environment name like 'aws://123456789012/us-east-1', or run in a directory with 'cdk.json' to use wildcards.`);
            }
            else {
                // User did not request anything
                throw new toolkit_lib_1.ToolkitError("Specify an environment name like 'aws://123456789012/us-east-1', or run in a directory with 'cdk.json'.");
            }
        }
        const environments = [...(0, cxapp_1.environmentsFromDescriptors)(environmentSpecs)];
        // If there is an '--app' argument, select the environments from the app.
        if (this.props.cloudExecutable.hasApp) {
            environments.push(...(await (0, cxapp_1.globEnvironmentsFromStacks)(await this.selectStacksForList([]), globSpecs, this.props.sdkProvider)));
        }
        return environments;
    }
    /**
     * Migrates a CloudFormation stack/template to a CDK app
     * @param options - Options for CDK app creation
     */
    async migrate(options) {
        (0, logging_1.warning)('This command is an experimental feature.');
        const language = options.language?.toLowerCase() ?? 'typescript';
        const environment = (0, migrate_1.setEnvironment)(options.account, options.region);
        let generateTemplateOutput;
        let cfn;
        let templateToDelete;
        try {
            // if neither fromPath nor fromStack is provided, generate a template using cloudformation
            const scanType = (0, migrate_1.parseSourceOptions)(options.fromPath, options.fromStack, options.stackName).source;
            if (scanType == migrate_1.TemplateSourceOptions.SCAN) {
                generateTemplateOutput = await (0, migrate_1.generateTemplate)({
                    stackName: options.stackName,
                    filters: options.filter,
                    fromScan: options.fromScan,
                    sdkProvider: this.props.sdkProvider,
                    environment: environment,
                });
                templateToDelete = generateTemplateOutput.templateId;
            }
            else if (scanType == migrate_1.TemplateSourceOptions.PATH) {
                const templateBody = (0, migrate_1.readFromPath)(options.fromPath);
                const parsedTemplate = (0, util_2.deserializeStructure)(templateBody);
                const templateId = parsedTemplate.Metadata?.TemplateId?.toString();
                if (templateId) {
                    // if we have a template id, we can call describe generated template to get the resource identifiers
                    // resource metadata, and template source to generate the template
                    cfn = new migrate_1.CfnTemplateGeneratorProvider(await (0, migrate_1.buildCfnClient)(this.props.sdkProvider, environment));
                    const generatedTemplateSummary = await cfn.describeGeneratedTemplate(templateId);
                    generateTemplateOutput = (0, migrate_1.buildGenertedTemplateOutput)(generatedTemplateSummary, templateBody, generatedTemplateSummary.GeneratedTemplateId);
                }
                else {
                    generateTemplateOutput = {
                        migrateJson: {
                            templateBody: templateBody,
                            source: 'localfile',
                        },
                    };
                }
            }
            else if (scanType == migrate_1.TemplateSourceOptions.STACK) {
                const template = await (0, migrate_1.readFromStack)(options.stackName, this.props.sdkProvider, environment);
                if (!template) {
                    throw new toolkit_lib_1.ToolkitError(`No template found for stack-name: ${options.stackName}`);
                }
                generateTemplateOutput = {
                    migrateJson: {
                        templateBody: template,
                        source: options.stackName,
                    },
                };
            }
            else {
                // We shouldn't ever get here, but just in case.
                throw new toolkit_lib_1.ToolkitError(`Invalid source option provided: ${scanType}`);
            }
            const stack = (0, migrate_1.generateStack)(generateTemplateOutput.migrateJson.templateBody, options.stackName, language);
            (0, logging_1.success)(' ⏳  Generating CDK app for %s...', chalk.blue(options.stackName));
            await (0, migrate_1.generateCdkApp)(options.stackName, stack, language, options.outputPath, options.compress);
            if (generateTemplateOutput) {
                (0, migrate_1.writeMigrateJsonFile)(options.outputPath, options.stackName, generateTemplateOutput.migrateJson);
            }
            if ((0, migrate_1.isThereAWarning)(generateTemplateOutput)) {
                (0, logging_1.warning)(' ⚠️  Some resources could not be migrated completely. Please review the README.md file for more information.');
                (0, migrate_1.appendWarningsToReadme)(`${path.join(options.outputPath ?? process.cwd(), options.stackName)}/README.md`, generateTemplateOutput.resources);
            }
        }
        catch (e) {
            (0, logging_1.error)(' ❌  Migrate failed for `%s`: %s', options.stackName, e.message);
            throw e;
        }
        finally {
            if (templateToDelete) {
                if (!cfn) {
                    cfn = new migrate_1.CfnTemplateGeneratorProvider(await (0, migrate_1.buildCfnClient)(this.props.sdkProvider, environment));
                }
                if (!process.env.MIGRATE_INTEG_TEST) {
                    await cfn.deleteGeneratedTemplate(templateToDelete);
                }
            }
        }
    }
    async refactor(options) {
        if (options.mappingFile && options.excludeFile) {
            throw new toolkit_lib_1.ToolkitError('Cannot use both --exclude-file and mapping-file.');
        }
        if (options.revert && !options.mappingFile) {
            throw new toolkit_lib_1.ToolkitError('The --revert option can only be used with the --mapping-file option.');
        }
        try {
            await this.toolkit.refactor(this.props.cloudExecutable, {
                dryRun: options.dryRun,
                stacks: {
                    patterns: options.selector.patterns,
                    strategy: options.selector.patterns.length > 0 ? toolkit_lib_1.StackSelectionStrategy.PATTERN_MATCH : toolkit_lib_1.StackSelectionStrategy.ALL_STACKS,
                },
                mappingSource: await mappingSource(),
            });
        }
        catch (e) {
            (0, logging_1.error)(e.message);
            return 1;
        }
        return 0;
        async function readMappingFile(filePath) {
            if (filePath == null) {
                return undefined;
            }
            if (!(await fs.pathExists(filePath))) {
                throw new toolkit_lib_1.ToolkitError(`The mapping file ${filePath} does not exist`);
            }
            const content = JSON.parse(fs.readFileSync(filePath).toString('utf-8'));
            if (content.environments) {
                return content.environments;
            }
            else {
                throw new toolkit_lib_1.ToolkitError(`The mapping file ${filePath} does not contain an \`environments\` array`);
            }
        }
        async function readExcludeFile(filePath) {
            if (filePath != null) {
                if (!(await fs.pathExists(filePath))) {
                    throw new toolkit_lib_1.ToolkitError(`The exclude file '${filePath}' does not exist`);
                }
                return fs.readFileSync(filePath).toString('utf-8').split('\n');
            }
            return undefined;
        }
        async function mappingSource() {
            if (options.mappingFile != null) {
                return toolkit_lib_1.MappingSource.explicit(await readMappingFile(options.mappingFile));
            }
            if (options.revert) {
                return toolkit_lib_1.MappingSource.reverse(await readMappingFile(options.mappingFile));
            }
            return toolkit_lib_1.MappingSource.auto((await readExcludeFile(options.excludeFile)) ?? []);
        }
    }
    async selectStacksForList(patterns) {
        const assembly = await this.assembly();
        const stacks = await assembly.selectStacks({ patterns }, { defaultBehavior: cxapp_1.DefaultSelection.AllStacks });
        // No validation
        return stacks;
    }
    async selectStacksForDeploy(selector, exclusively, cacheCloudAssembly, ignoreNoStacks) {
        const assembly = await this.assembly(cacheCloudAssembly);
        const stacks = await assembly.selectStacks(selector, {
            extend: exclusively ? cloud_assembly_1.ExtendedStackSelection.None : cloud_assembly_1.ExtendedStackSelection.Upstream,
            defaultBehavior: cxapp_1.DefaultSelection.OnlySingle,
            ignoreNoStacks,
        });
        this.validateStacksSelected(stacks, selector.patterns);
        await this.validateStacks(stacks);
        return stacks;
    }
    async selectStacksForDiff(stackNames, exclusively, autoValidate) {
        const assembly = await this.assembly();
        const selectedForDiff = await assembly.selectStacks({ patterns: stackNames }, {
            extend: exclusively ? cloud_assembly_1.ExtendedStackSelection.None : cloud_assembly_1.ExtendedStackSelection.Upstream,
            defaultBehavior: cxapp_1.DefaultSelection.MainAssembly,
        });
        const allStacks = await this.selectStacksForList([]);
        const autoValidateStacks = autoValidate
            ? allStacks.filter((art) => art.validateOnSynth ?? false)
            : new cloud_assembly_1.StackCollection(assembly, []);
        this.validateStacksSelected(selectedForDiff.concat(autoValidateStacks), stackNames);
        await this.validateStacks(selectedForDiff.concat(autoValidateStacks));
        return selectedForDiff;
    }
    async selectStacksForDestroy(selector, exclusively) {
        const assembly = await this.assembly();
        const stacks = await assembly.selectStacks(selector, {
            extend: exclusively ? cloud_assembly_1.ExtendedStackSelection.None : cloud_assembly_1.ExtendedStackSelection.Downstream,
            defaultBehavior: cxapp_1.DefaultSelection.OnlySingle,
        });
        // No validation
        return stacks;
    }
    /**
     * Validate the stacks for errors and warnings according to the CLI's current settings
     */
    async validateStacks(stacks) {
        const failAt = this.validateMetadataFailAt();
        await stacks.validateMetadata(failAt, stackMetadataLogger(this.props.verbose));
    }
    validateMetadataFailAt() {
        let failAt = 'error';
        if (this.props.ignoreErrors) {
            failAt = 'none';
        }
        if (this.props.strict) {
            failAt = 'warn';
        }
        return failAt;
    }
    /**
     * Validate that if a user specified a stack name there exists at least 1 stack selected
     */
    validateStacksSelected(stacks, stackNames) {
        if (stackNames.length != 0 && stacks.stackCount == 0) {
            throw new toolkit_lib_1.ToolkitError(`No stacks match the name(s) ${stackNames}`);
        }
    }
    /**
     * Select a single stack by its name
     */
    async selectSingleStackByName(stackName) {
        const assembly = await this.assembly();
        const stacks = await assembly.selectStacks({ patterns: [stackName] }, {
            extend: cloud_assembly_1.ExtendedStackSelection.None,
            defaultBehavior: cxapp_1.DefaultSelection.None,
        });
        // Could have been a glob so check that we evaluated to exactly one
        if (stacks.stackCount > 1) {
            throw new toolkit_lib_1.ToolkitError(`This command requires exactly one stack and we matched more than one: ${stacks.stackIds}`);
        }
        return assembly.stackById(stacks.firstStack.id);
    }
    assembly(cacheCloudAssembly) {
        return this.props.cloudExecutable.synthesize(cacheCloudAssembly);
    }
    patternsArrayForWatch(patterns, options) {
        const patternsArray = patterns !== undefined ? (Array.isArray(patterns) ? patterns : [patterns]) : [];
        return patternsArray.length > 0 ? patternsArray : options.returnRootDirIfEmpty ? [options.rootDir] : [];
    }
    async invokeDeployFromWatch(options, cloudWatchLogMonitor) {
        const deployOptions = {
            ...options,
            requireApproval: cloud_assembly_schema_1.RequireApproval.NEVER,
            // if 'watch' is called by invoking 'cdk deploy --watch',
            // we need to make sure to not call 'deploy' with 'watch' again,
            // as that would lead to a cycle
            watch: false,
            cloudWatchLogMonitor,
            cacheCloudAssembly: false,
            extraUserAgent: `cdk-watch/hotswap-${options.deploymentMethod?.method === 'hotswap' ? 'on' : 'off'}`,
            concurrency: options.concurrency,
        };
        try {
            await this.deploy(deployOptions);
        }
        catch {
            // just continue - deploy will show the error
        }
    }
    /**
     * Remove the asset publishing and building from the work graph for assets that are already in place
     */
    async removePublishedAssets(graph, options) {
        await graph.removeUnnecessaryAssets(assetNode => this.props.deployments.isSingleAssetPublished(assetNode.assetManifest, assetNode.asset, {
            stack: assetNode.parentStack,
            roleArn: options.roleArn,
            stackName: assetNode.parentStack.stackName,
        }));
    }
}
exports.CdkToolkit = CdkToolkit;
/**
 * Print a serialized object (YAML or JSON) to stdout.
 */
function printSerializedObject(obj, json) {
    (0, logging_1.result)((0, util_2.serializeStructure)(obj, json));
}
function buildParameterMap(parameters) {
    const parameterMap = { '*': {} };
    for (const key in parameters) {
        if (parameters.hasOwnProperty(key)) {
            const [stack, parameter] = key.split(':', 2);
            if (!parameter) {
                parameterMap['*'][stack] = parameters[key];
            }
            else {
                if (!parameterMap[stack]) {
                    parameterMap[stack] = {};
                }
                parameterMap[stack][parameter] = parameters[key];
            }
        }
    }
    return parameterMap;
}
/**
 * Ask the user for a yes/no confirmation
 *
 * Automatically fail the confirmation in case we're in a situation where the confirmation
 * cannot be interactively obtained from a human at the keyboard.
 */
async function askUserConfirmation(ioHost, concurrency, motivation, question) {
    await ioHost.withCorkedLogging(async () => {
        // only talk to user if STDIN is a terminal (otherwise, fail)
        if (!TESTING && !process.stdin.isTTY) {
            throw new toolkit_lib_1.ToolkitError(`${motivation}, but terminal (TTY) is not attached so we are unable to get a confirmation from the user`);
        }
        // only talk to user if concurrency is 1 (otherwise, fail)
        if (concurrency > 1) {
            throw new toolkit_lib_1.ToolkitError(`${motivation}, but concurrency is greater than 1 so we are unable to get a confirmation from the user`);
        }
        const confirmed = await promptly.confirm(`${chalk.cyan(question)} (y/n)?`);
        if (!confirmed) {
            throw new toolkit_lib_1.ToolkitError('Aborted by user');
        }
    });
}
/**
 * Logger for processing stack metadata
 */
function stackMetadataLogger(verbose) {
    const makeLogger = (level) => {
        switch (level) {
            case 'error':
                return [logging_1.error, 'Error'];
            case 'warn':
                return [logging_1.warning, 'Warning'];
            default:
                return [logging_1.info, 'Info'];
        }
    };
    return async (level, msg) => {
        const [logFn, prefix] = makeLogger(level);
        logFn(`[${prefix} at ${msg.id}] ${msg.entry.data}`);
        if (verbose && msg.entry.trace) {
            logFn(`  ${msg.entry.trace.join('\n  ')}`);
        }
    };
}
/**
 * Determine if manual approval is required or not. Requires approval for
 * - RequireApproval.ANY_CHANGE
 * - RequireApproval.BROADENING and the changes are indeed broadening permissions
 */
function requiresApproval(requireApproval, permissionChangeType) {
    return requireApproval === cloud_assembly_schema_1.RequireApproval.ANYCHANGE ||
        requireApproval === cloud_assembly_schema_1.RequireApproval.BROADENING && permissionChangeType === toolkit_lib_1.PermissionChangeType.BROADENING;
}
//# sourceMappingURL=data:application/json;base64,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