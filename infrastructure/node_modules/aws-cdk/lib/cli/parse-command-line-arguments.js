"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseCommandLineArguments = parseCommandLineArguments;
const helpers = require("./util/yargs-helpers");
// @ts-ignore TS6133
function parseCommandLineArguments(args) {
    return yargs
        .env('CDK')
        .usage('Usage: cdk -a <cdk-app> COMMAND')
        .option('app', {
        default: undefined,
        type: 'string',
        alias: 'a',
        desc: 'REQUIRED WHEN RUNNING APP: command-line for executing your app or a cloud assembly directory (e.g. "node bin/my-app.js"). Can also be specified in cdk.json or ~/.cdk.json',
        requiresArg: true,
    })
        .option('build', {
        default: undefined,
        type: 'string',
        desc: 'Command-line for a pre-synth build',
    })
        .option('context', {
        type: 'array',
        alias: 'c',
        desc: 'Add contextual string parameter (KEY=VALUE)',
        nargs: 1,
        requiresArg: true,
    })
        .option('plugin', {
        type: 'array',
        alias: 'p',
        desc: 'Name or path of a node package that extend the CDK features. Can be specified multiple times',
        nargs: 1,
        requiresArg: true,
    })
        .option('trace', {
        default: undefined,
        type: 'boolean',
        desc: 'Print trace for stack warnings',
    })
        .option('strict', {
        default: undefined,
        type: 'boolean',
        desc: 'Do not construct stacks with warnings',
    })
        .option('lookups', {
        default: true,
        type: 'boolean',
        desc: 'Perform context lookups (synthesis fails if this is disabled and context lookups need to be performed)',
    })
        .option('ignore-errors', {
        default: false,
        type: 'boolean',
        desc: 'Ignores synthesis errors, which will likely produce an invalid output',
    })
        .option('json', {
        default: false,
        type: 'boolean',
        alias: 'j',
        desc: 'Use JSON output instead of YAML when templates are printed to STDOUT',
    })
        .option('verbose', {
        default: false,
        type: 'boolean',
        alias: 'v',
        desc: 'Show debug logs (specify multiple times to increase verbosity)',
        count: true,
    })
        .option('debug', {
        default: false,
        type: 'boolean',
        desc: 'Debug the CDK app. Log additional information during synthesis, such as creation stack traces of tokens (sets CDK_DEBUG, will slow down synthesis)',
    })
        .option('profile', {
        default: undefined,
        type: 'string',
        desc: 'Use the indicated AWS profile as the default environment',
        requiresArg: true,
    })
        .option('proxy', {
        default: undefined,
        type: 'string',
        desc: 'Use the indicated proxy. Will read from HTTPS_PROXY environment variable if not specified',
        requiresArg: true,
    })
        .option('ca-bundle-path', {
        default: undefined,
        type: 'string',
        desc: 'Path to CA certificate to use when validating HTTPS requests. Will read from AWS_CA_BUNDLE environment variable if not specified',
        requiresArg: true,
    })
        .option('ec2creds', {
        default: undefined,
        type: 'boolean',
        alias: 'i',
        desc: 'Force trying to fetch EC2 instance credentials. Default: guess EC2 instance status',
    })
        .option('version-reporting', {
        default: undefined,
        type: 'boolean',
        desc: 'Include the "AWS::CDK::Metadata" resource in synthesized templates (enabled by default)',
    })
        .option('path-metadata', {
        default: undefined,
        type: 'boolean',
        desc: 'Include "aws:cdk:path" CloudFormation metadata for each resource (enabled by default)',
    })
        .option('asset-metadata', {
        default: undefined,
        type: 'boolean',
        desc: 'Include "aws:asset:*" CloudFormation metadata for resources that uses assets (enabled by default)',
    })
        .option('role-arn', {
        default: undefined,
        type: 'string',
        alias: 'r',
        desc: 'ARN of Role to use when invoking CloudFormation',
        requiresArg: true,
    })
        .option('staging', {
        default: true,
        type: 'boolean',
        desc: 'Copy assets to the output directory (use --no-staging to disable the copy of assets which allows local debugging via the SAM CLI to reference the original source files)',
    })
        .option('output', {
        default: undefined,
        type: 'string',
        alias: 'o',
        desc: 'Emits the synthesized cloud assembly into a directory (default: cdk.out)',
        requiresArg: true,
    })
        .option('notices', {
        default: helpers.shouldDisplayNotices(),
        type: 'boolean',
        desc: 'Show relevant notices',
    })
        .option('no-color', {
        default: false,
        type: 'boolean',
        desc: 'Removes colors and other style from console output',
    })
        .option('ci', {
        default: helpers.isCI(),
        type: 'boolean',
        desc: 'Force CI detection. If CI=true then logs will be sent to stdout instead of stderr',
    })
        .option('unstable', {
        type: 'array',
        desc: 'Opt in to unstable features. The flag indicates that the scope and API of a feature might still change. Otherwise the feature is generally production ready and fully supported. Can be specified multiple times.',
        default: [],
        nargs: 1,
        requiresArg: true,
    })
        .command(['list [STACKS..]', 'ls [STACKS..]'], 'Lists all stacks in the app', (yargs) => yargs
        .option('long', {
        default: false,
        type: 'boolean',
        alias: 'l',
        desc: 'Display environment information for each stack',
    })
        .option('show-dependencies', {
        default: false,
        type: 'boolean',
        alias: 'd',
        desc: 'Display stack dependency information for each stack',
    }))
        .command(['synth [STACKS..]', 'synthesize [STACKS..]'], 'Synthesizes and prints the CloudFormation template for this stack', (yargs) => yargs
        .option('exclusively', {
        default: undefined,
        type: 'boolean',
        alias: 'e',
        desc: "Only synthesize requested stacks, don't include dependencies",
    })
        .option('validation', {
        default: true,
        type: 'boolean',
        desc: 'After synthesis, validate stacks with the "validateOnSynth" attribute set (can also be controlled with CDK_VALIDATION)',
    })
        .option('quiet', {
        default: false,
        type: 'boolean',
        alias: 'q',
        desc: 'Do not output CloudFormation Template to stdout',
    }))
        .command('bootstrap [ENVIRONMENTS..]', 'Deploys the CDK toolkit stack into an AWS environment', (yargs) => yargs
        .option('bootstrap-bucket-name', {
        default: undefined,
        type: 'string',
        alias: ['b', 'toolkit-bucket-name'],
        desc: 'The name of the CDK toolkit bucket; bucket will be created and must not exist',
    })
        .option('bootstrap-kms-key-id', {
        default: undefined,
        type: 'string',
        desc: 'AWS KMS master key ID used for the SSE-KMS encryption (specify AWS_MANAGED_KEY to use an AWS-managed key)',
        conflicts: 'bootstrap-customer-key',
    })
        .option('example-permissions-boundary', {
        default: undefined,
        type: 'boolean',
        alias: 'epb',
        desc: 'Use the example permissions boundary.',
        conflicts: 'custom-permissions-boundary',
    })
        .option('custom-permissions-boundary', {
        default: undefined,
        type: 'string',
        alias: 'cpb',
        desc: 'Use the permissions boundary specified by name.',
        conflicts: 'example-permissions-boundary',
    })
        .option('bootstrap-customer-key', {
        default: undefined,
        type: 'boolean',
        desc: 'Create a Customer Master Key (CMK) for the bootstrap bucket (you will be charged but can customize permissions, modern bootstrapping only)',
        conflicts: 'bootstrap-kms-key-id',
    })
        .option('qualifier', {
        default: undefined,
        type: 'string',
        desc: 'String which must be unique for each bootstrap stack. You must configure it on your CDK app if you change this from the default.',
    })
        .option('public-access-block-configuration', {
        default: undefined,
        type: 'boolean',
        desc: 'Block public access configuration on CDK toolkit bucket (enabled by default) ',
    })
        .option('tags', {
        type: 'array',
        alias: 't',
        desc: 'Tags to add for the stack (KEY=VALUE)',
        default: [],
        nargs: 1,
        requiresArg: true,
    })
        .option('execute', {
        default: true,
        type: 'boolean',
        desc: 'Whether to execute ChangeSet (--no-execute will NOT execute the ChangeSet)',
    })
        .option('trust', {
        type: 'array',
        desc: 'The AWS account IDs that should be trusted to perform deployments into this environment (may be repeated, modern bootstrapping only)',
        default: [],
        nargs: 1,
        requiresArg: true,
    })
        .option('trust-for-lookup', {
        type: 'array',
        desc: 'The AWS account IDs that should be trusted to look up values in this environment (may be repeated, modern bootstrapping only)',
        default: [],
        nargs: 1,
        requiresArg: true,
    })
        .option('untrust', {
        type: 'array',
        desc: 'The AWS account IDs that should not be trusted by this environment (may be repeated, modern bootstrapping only)',
        default: [],
        nargs: 1,
        requiresArg: true,
    })
        .option('cloudformation-execution-policies', {
        type: 'array',
        desc: 'The Managed Policy ARNs that should be attached to the role performing deployments into this environment (may be repeated, modern bootstrapping only)',
        default: [],
        nargs: 1,
        requiresArg: true,
    })
        .option('force', {
        default: false,
        alias: 'f',
        type: 'boolean',
        desc: 'Always bootstrap even if it would downgrade template version',
    })
        .option('termination-protection', {
        default: undefined,
        type: 'boolean',
        desc: 'Toggle CloudFormation termination protection on the bootstrap stacks',
    })
        .option('show-template', {
        default: false,
        type: 'boolean',
        desc: "Instead of actual bootstrapping, print the current CLI's bootstrapping template to stdout for customization",
    })
        .option('toolkit-stack-name', {
        default: undefined,
        type: 'string',
        desc: 'The name of the CDK toolkit stack to create',
        requiresArg: true,
    })
        .option('template', {
        default: undefined,
        type: 'string',
        requiresArg: true,
        desc: 'Use the template from the given file instead of the built-in one (use --show-template to obtain an example)',
    })
        .option('previous-parameters', {
        default: true,
        type: 'boolean',
        desc: 'Use previous values for existing parameters (you must specify all parameters on every deployment if this is disabled)',
    }))
        .command('gc [ENVIRONMENTS..]', 'Garbage collect assets. Options detailed here: https://github.com/aws/aws-cdk-cli/tree/main/packages/aws-cdk#cdk-gc', (yargs) => yargs
        .option('action', {
        default: 'full',
        type: 'string',
        desc: 'The action (or sub-action) you want to perform. Valid entires are "print", "tag", "delete-tagged", "full".',
    })
        .option('type', {
        default: 'all',
        type: 'string',
        desc: 'Specify either ecr, s3, or all',
    })
        .option('rollback-buffer-days', {
        default: 0,
        type: 'number',
        desc: 'Delete assets that have been marked as isolated for this many days',
    })
        .option('created-buffer-days', {
        default: 1,
        type: 'number',
        desc: 'Never delete assets younger than this (in days)',
    })
        .option('confirm', {
        default: true,
        type: 'boolean',
        desc: 'Confirm via manual prompt before deletion',
    })
        .option('bootstrap-stack-name', {
        default: undefined,
        type: 'string',
        desc: 'The name of the CDK toolkit stack, if different from the default "CDKToolkit"',
        requiresArg: true,
    }))
        .command('deploy [STACKS..]', 'Deploys the stack(s) named STACKS into your AWS account', (yargs) => yargs
        .option('all', {
        default: false,
        type: 'boolean',
        desc: 'Deploy all available stacks',
    })
        .option('build-exclude', {
        type: 'array',
        alias: 'E',
        desc: 'Do not rebuild asset with the given ID. Can be specified multiple times',
        default: [],
        nargs: 1,
        requiresArg: true,
    })
        .option('exclusively', {
        default: undefined,
        type: 'boolean',
        alias: 'e',
        desc: "Only deploy requested stacks, don't include dependencies",
    })
        .option('require-approval', {
        default: undefined,
        type: 'string',
        choices: ['never', 'any-change', 'broadening'],
        desc: 'What security-sensitive changes need manual approval',
    })
        .option('notification-arns', {
        type: 'array',
        desc: "ARNs of SNS topics that CloudFormation will notify with stack related events. These will be added to ARNs specified with the 'notificationArns' stack property.",
        nargs: 1,
        requiresArg: true,
    })
        .option('tags', {
        type: 'array',
        alias: 't',
        desc: 'Tags to add to the stack (KEY=VALUE), overrides tags from Cloud Assembly (deprecated)',
        nargs: 1,
        requiresArg: true,
    })
        .option('execute', {
        default: undefined,
        type: 'boolean',
        desc: 'Whether to execute ChangeSet (--no-execute will NOT execute the ChangeSet) (deprecated)',
        deprecated: true,
    })
        .option('change-set-name', {
        default: undefined,
        type: 'string',
        desc: 'Name of the CloudFormation change set to create (only if method is not direct)',
    })
        .option('method', {
        default: undefined,
        alias: 'm',
        type: 'string',
        choices: ['direct', 'change-set', 'prepare-change-set'],
        requiresArg: true,
        desc: 'How to perform the deployment. Direct is a bit faster but lacks progress information',
    })
        .option('import-existing-resources', {
        default: false,
        type: 'boolean',
        desc: 'Indicates if the stack set imports resources that already exist.',
    })
        .option('force', {
        default: false,
        alias: 'f',
        type: 'boolean',
        desc: 'Always deploy stack even if templates are identical',
    })
        .option('parameters', {
        type: 'array',
        desc: 'Additional parameters passed to CloudFormation at deploy time (STACK:KEY=VALUE)',
        default: {},
        nargs: 1,
        requiresArg: true,
    })
        .option('outputs-file', {
        default: undefined,
        type: 'string',
        alias: 'O',
        desc: 'Path to file where stack outputs will be written as JSON',
        requiresArg: true,
    })
        .option('previous-parameters', {
        default: true,
        type: 'boolean',
        desc: 'Use previous values for existing parameters (you must specify all parameters on every deployment if this is disabled)',
    })
        .option('toolkit-stack-name', {
        default: undefined,
        type: 'string',
        desc: 'The name of the existing CDK toolkit stack (only used for app using legacy synthesis)',
        requiresArg: true,
    })
        .option('progress', {
        default: undefined,
        type: 'string',
        choices: ['bar', 'events'],
        desc: 'Display mode for stack activity events',
    })
        .option('rollback', {
        default: undefined,
        type: 'boolean',
        desc: "Rollback stack to stable state on failure. Defaults to 'true', iterate more rapidly with --no-rollback or -R. Note: do **not** disable this flag for deployments with resource replacements, as that will always fail",
    })
        .option('R', { type: 'boolean', hidden: true })
        .middleware(helpers.yargsNegativeAlias('R', 'rollback'), true)
        .option('hotswap', {
        default: undefined,
        type: 'boolean',
        desc: "Attempts to perform a 'hotswap' deployment, but does not fall back to a full deployment if that is not possible. Instead, changes to any non-hotswappable properties are ignored.Do not use this in production environments",
    })
        .option('hotswap-fallback', {
        default: undefined,
        type: 'boolean',
        desc: "Attempts to perform a 'hotswap' deployment, which skips CloudFormation and updates the resources directly, and falls back to a full deployment if that is not possible. Do not use this in production environments",
    })
        .option('hotswap-ecs-minimum-healthy-percent', {
        default: undefined,
        type: 'string',
        desc: "Lower limit on the number of your service's tasks that must remain in the RUNNING state during a deployment, as a percentage of the desiredCount",
    })
        .option('hotswap-ecs-maximum-healthy-percent', {
        default: undefined,
        type: 'string',
        desc: "Upper limit on the number of your service's tasks that are allowed in the RUNNING or PENDING state during a deployment, as a percentage of the desiredCount",
    })
        .option('hotswap-ecs-stabilization-timeout-seconds', {
        default: undefined,
        type: 'string',
        desc: 'Number of seconds to wait for a single service to reach stable state, where the desiredCount is equal to the runningCount',
    })
        .option('watch', {
        default: undefined,
        type: 'boolean',
        desc: 'Continuously observe the project files, and deploy the given stack(s) automatically when changes are detected. Implies --hotswap by default',
    })
        .option('logs', {
        default: true,
        type: 'boolean',
        desc: "Show CloudWatch log events from all resources in the selected Stacks in the terminal. 'true' by default, use --no-logs to turn off. Only in effect if specified alongside the '--watch' option",
    })
        .option('concurrency', {
        default: 1,
        type: 'number',
        desc: 'Maximum number of simultaneous deployments (dependency permitting) to execute.',
        requiresArg: true,
    })
        .option('asset-parallelism', {
        default: undefined,
        type: 'boolean',
        desc: 'Whether to build/publish assets in parallel',
    })
        .option('asset-prebuild', {
        default: true,
        type: 'boolean',
        desc: 'Whether to build all assets before deploying the first stack (useful for failing Docker builds)',
    })
        .option('ignore-no-stacks', {
        default: false,
        type: 'boolean',
        desc: 'Whether to deploy if the app contains no stacks',
    }))
        .command('rollback [STACKS..]', 'Rolls back the stack(s) named STACKS to their last stable state', (yargs) => yargs
        .option('all', {
        default: false,
        type: 'boolean',
        desc: 'Roll back all available stacks',
    })
        .option('toolkit-stack-name', {
        default: undefined,
        type: 'string',
        desc: 'The name of the CDK toolkit stack the environment is bootstrapped with',
        requiresArg: true,
    })
        .option('force', {
        default: undefined,
        alias: 'f',
        type: 'boolean',
        desc: 'Orphan all resources for which the rollback operation fails.',
    })
        .option('validate-bootstrap-version', {
        default: undefined,
        type: 'boolean',
        desc: "Whether to validate the bootstrap stack version. Defaults to 'true', disable with --no-validate-bootstrap-version.",
    })
        .option('orphan', {
        type: 'array',
        desc: 'Orphan the given resources, identified by their logical ID (can be specified multiple times)',
        default: [],
        nargs: 1,
        requiresArg: true,
    }))
        .command('import [STACK]', 'Import existing resource(s) into the given STACK', (yargs) => yargs
        .option('execute', {
        default: true,
        type: 'boolean',
        desc: 'Whether to execute ChangeSet (--no-execute will NOT execute the ChangeSet)',
    })
        .option('change-set-name', {
        default: undefined,
        type: 'string',
        desc: 'Name of the CloudFormation change set to create',
    })
        .option('toolkit-stack-name', {
        default: undefined,
        type: 'string',
        desc: 'The name of the CDK toolkit stack to create',
        requiresArg: true,
    })
        .option('rollback', {
        default: undefined,
        type: 'boolean',
        desc: "Rollback stack to stable state on failure. Defaults to 'true', iterate more rapidly with --no-rollback or -R. Note: do **not** disable this flag for deployments with resource replacements, as that will always fail",
    })
        .option('force', {
        default: undefined,
        alias: 'f',
        type: 'boolean',
        desc: "Do not abort if the template diff includes updates or deletes. This is probably safe but we're not sure, let us know how it goes.",
    })
        .option('record-resource-mapping', {
        default: undefined,
        type: 'string',
        alias: 'r',
        requiresArg: true,
        desc: 'If specified, CDK will generate a mapping of existing physical resources to CDK resources to be imported as. The mapping will be written in the given file path. No actual import operation will be performed',
    })
        .option('resource-mapping', {
        default: undefined,
        type: 'string',
        alias: 'm',
        requiresArg: true,
        desc: 'If specified, CDK will use the given file to map physical resources to CDK resources for import, instead of interactively asking the user. Can be run from scripts',
    }))
        .command('watch [STACKS..]', "Shortcut for 'deploy --watch'", (yargs) => yargs
        .option('build-exclude', {
        type: 'array',
        alias: 'E',
        desc: 'Do not rebuild asset with the given ID. Can be specified multiple times',
        default: [],
        nargs: 1,
        requiresArg: true,
    })
        .option('exclusively', {
        default: undefined,
        type: 'boolean',
        alias: 'e',
        desc: "Only deploy requested stacks, don't include dependencies",
    })
        .option('change-set-name', {
        default: undefined,
        type: 'string',
        desc: 'Name of the CloudFormation change set to create',
    })
        .option('force', {
        default: false,
        alias: 'f',
        type: 'boolean',
        desc: 'Always deploy stack even if templates are identical',
    })
        .option('toolkit-stack-name', {
        default: undefined,
        type: 'string',
        desc: 'The name of the existing CDK toolkit stack (only used for app using legacy synthesis)',
        requiresArg: true,
    })
        .option('progress', {
        default: undefined,
        type: 'string',
        choices: ['bar', 'events'],
        desc: 'Display mode for stack activity events',
    })
        .option('rollback', {
        default: undefined,
        type: 'boolean',
        desc: "Rollback stack to stable state on failure. Defaults to 'true', iterate more rapidly with --no-rollback or -R. Note: do **not** disable this flag for deployments with resource replacements, as that will always fail",
    })
        .option('R', { type: 'boolean', hidden: true })
        .middleware(helpers.yargsNegativeAlias('R', 'rollback'), true)
        .option('hotswap', {
        default: undefined,
        type: 'boolean',
        desc: "Attempts to perform a 'hotswap' deployment, but does not fall back to a full deployment if that is not possible. Instead, changes to any non-hotswappable properties are ignored.'true' by default, use --no-hotswap to turn off",
    })
        .option('hotswap-fallback', {
        default: undefined,
        type: 'boolean',
        desc: "Attempts to perform a 'hotswap' deployment, which skips CloudFormation and updates the resources directly, and falls back to a full deployment if that is not possible.",
    })
        .option('hotswap-ecs-minimum-healthy-percent', {
        default: undefined,
        type: 'string',
        desc: "Lower limit on the number of your service's tasks that must remain in the RUNNING state during a deployment, as a percentage of the desiredCount",
    })
        .option('hotswap-ecs-maximum-healthy-percent', {
        default: undefined,
        type: 'string',
        desc: "Upper limit on the number of your service's tasks that are allowed in the RUNNING or PENDING state during a deployment, as a percentage of the desiredCount",
    })
        .option('hotswap-ecs-stabilization-timeout-seconds', {
        default: undefined,
        type: 'string',
        desc: 'Number of seconds to wait for a single service to reach stable state, where the desiredCount is equal to the runningCount',
    })
        .option('logs', {
        default: true,
        type: 'boolean',
        desc: "Show CloudWatch log events from all resources in the selected Stacks in the terminal. 'true' by default, use --no-logs to turn off",
    })
        .option('concurrency', {
        default: 1,
        type: 'number',
        desc: 'Maximum number of simultaneous deployments (dependency permitting) to execute.',
        requiresArg: true,
    }))
        .command('destroy [STACKS..]', 'Destroy the stack(s) named STACKS', (yargs) => yargs
        .option('all', {
        default: false,
        type: 'boolean',
        desc: 'Destroy all available stacks',
    })
        .option('exclusively', {
        default: undefined,
        type: 'boolean',
        alias: 'e',
        desc: "Only destroy requested stacks, don't include dependees",
    })
        .option('force', {
        default: undefined,
        type: 'boolean',
        alias: 'f',
        desc: 'Do not ask for confirmation before destroying the stacks',
    }))
        .command('diff [STACKS..]', 'Compares the specified stack with the deployed stack or a local template file, and returns with status 1 if any difference is found', (yargs) => yargs
        .option('exclusively', {
        default: undefined,
        type: 'boolean',
        alias: 'e',
        desc: "Only diff requested stacks, don't include dependencies",
    })
        .option('context-lines', {
        default: 3,
        type: 'number',
        desc: 'Number of context lines to include in arbitrary JSON diff rendering',
        requiresArg: true,
    })
        .option('template', {
        default: undefined,
        type: 'string',
        desc: 'The path to the CloudFormation template to compare with',
        requiresArg: true,
    })
        .option('strict', {
        default: false,
        type: 'boolean',
        desc: 'Do not filter out AWS::CDK::Metadata resources, mangled non-ASCII characters, or the CheckBootstrapVersionRule',
    })
        .option('security-only', {
        default: false,
        type: 'boolean',
        desc: 'Only diff for broadened security changes',
    })
        .option('fail', {
        default: undefined,
        type: 'boolean',
        desc: 'Fail with exit code 1 in case of diff',
    })
        .option('processed', {
        default: false,
        type: 'boolean',
        desc: 'Whether to compare against the template with Transforms already processed',
    })
        .option('quiet', {
        default: false,
        type: 'boolean',
        alias: 'q',
        desc: 'Do not print stack name and default message when there is no diff to stdout',
    })
        .option('change-set', {
        default: true,
        type: 'boolean',
        alias: 'changeset',
        desc: 'Whether to create a changeset to analyze resource replacements. In this mode, diff will use the deploy role instead of the lookup role.',
    })
        .option('import-existing-resources', {
        default: false,
        type: 'boolean',
        desc: 'Whether or not the change set imports resources that already exist',
    }))
        .command('drift [STACKS..]', 'Detect drifts in the given CloudFormation stack(s)', (yargs) => yargs.option('fail', {
        default: undefined,
        type: 'boolean',
        desc: 'Fail with exit code 1 if drift is detected',
    }))
        .command('metadata [STACK]', 'Returns all metadata associated with this stack')
        .command(['acknowledge [ID]', 'ack [ID]'], 'Acknowledge a notice so that it does not show up anymore')
        .command('notices', 'Returns a list of relevant notices', (yargs) => yargs.option('unacknowledged', {
        default: false,
        type: 'boolean',
        alias: 'u',
        desc: 'Returns a list of unacknowledged notices',
    }))
        .command('init [TEMPLATE]', 'Create a new, empty CDK project from a template.', (yargs) => yargs
        .option('language', {
        default: undefined,
        type: 'string',
        alias: 'l',
        desc: 'The language to be used for the new project (default can be configured in ~/.cdk.json)',
        choices: ['csharp', 'fsharp', 'go', 'java', 'javascript', 'python', 'typescript'],
    })
        .option('list', {
        default: undefined,
        type: 'boolean',
        desc: 'List the available templates',
    })
        .option('generate-only', {
        default: false,
        type: 'boolean',
        desc: 'If true, only generates project files, without executing additional operations such as setting up a git repo, installing dependencies or compiling the project',
    })
        .option('lib-version', {
        default: undefined,
        type: 'string',
        alias: 'V',
        desc: 'The version of the CDK library (aws-cdk-lib) to initialize the project with. Defaults to the version that was current when this CLI was built.',
    }))
        .command('migrate', 'Migrate existing AWS resources into a CDK app', (yargs) => yargs
        .option('stack-name', {
        default: undefined,
        type: 'string',
        alias: 'n',
        desc: 'The name assigned to the stack created in the new project. The name of the app will be based off this name as well.',
        requiresArg: true,
    })
        .option('language', {
        default: 'typescript',
        type: 'string',
        alias: 'l',
        desc: 'The language to be used for the new project',
        choices: ['typescript', 'go', 'java', 'python', 'csharp'],
    })
        .option('account', {
        default: undefined,
        type: 'string',
        desc: 'The account to retrieve the CloudFormation stack template from',
    })
        .option('region', {
        default: undefined,
        type: 'string',
        desc: 'The region to retrieve the CloudFormation stack template from',
    })
        .option('from-path', {
        default: undefined,
        type: 'string',
        desc: 'The path to the CloudFormation template to migrate. Use this for locally stored templates',
    })
        .option('from-stack', {
        default: undefined,
        type: 'boolean',
        desc: 'Use this flag to retrieve the template for an existing CloudFormation stack',
    })
        .option('output-path', {
        default: undefined,
        type: 'string',
        desc: 'The output path for the migrated CDK app',
    })
        .option('from-scan', {
        default: undefined,
        type: 'string',
        desc: 'Determines if a new scan should be created, or the last successful existing scan should be used \n options are "new" or "most-recent"',
    })
        .option('filter', {
        type: 'array',
        desc: 'Filters the resource scan based on the provided criteria in the following format: "key1=value1,key2=value2"\n This field can be passed multiple times for OR style filtering: \n filtering options: \n resource-identifier: A key-value pair that identifies the target resource. i.e. {"ClusterName", "myCluster"}\n resource-type-prefix: A string that represents a type-name prefix. i.e. "AWS::DynamoDB::"\n tag-key: a string that matches resources with at least one tag with the provided key. i.e. "myTagKey"\n tag-value: a string that matches resources with at least one tag with the provided value. i.e. "myTagValue"',
        nargs: 1,
        requiresArg: true,
    })
        .option('compress', {
        default: undefined,
        type: 'boolean',
        desc: 'Use this flag to zip the generated CDK app',
    }))
        .command('context', 'Manage cached context values', (yargs) => yargs
        .option('reset', {
        default: undefined,
        alias: 'e',
        desc: 'The context key (or its index) to reset',
        type: 'string',
        requiresArg: true,
    })
        .option('force', {
        default: false,
        alias: 'f',
        desc: 'Ignore missing key error',
        type: 'boolean',
    })
        .option('clear', {
        default: false,
        desc: 'Clear all context',
        type: 'boolean',
    }))
        .command(['docs', 'doc'], 'Opens the reference documentation in a browser', (yargs) => yargs.option('browser', {
        default: helpers.browserForPlatform(),
        alias: 'b',
        desc: 'the command to use to open the browser, using %u as a placeholder for the path of the file to open',
        type: 'string',
    }))
        .command('doctor', 'Check your set-up for potential problems')
        .command('refactor [STACKS..]', 'Moves resources between stacks or within the same stack', (yargs) => yargs
        .option('dry-run', {
        default: false,
        type: 'boolean',
        desc: 'Do not perform any changes, just show what would be done',
    })
        .option('exclude-file', {
        default: undefined,
        type: 'string',
        requiresArg: true,
        desc: 'If specified, CDK will use the given file to exclude resources from the refactor',
    })
        .option('mapping-file', {
        default: undefined,
        type: 'string',
        requiresArg: true,
        desc: 'A file that declares an explicit mapping to be applied. If provided, the command will use it instead of computing the mapping.',
    })
        .option('revert', {
        default: false,
        type: 'boolean',
        desc: 'If specified, the command will revert the refactor operation. This is only valid if a mapping file was provided.',
    }))
        .command('cli-telemetry', 'Enable or disable anonymous telemetry', (yargs) => yargs
        .option('enable', {
        default: undefined,
        type: 'boolean',
        desc: 'Enable anonymous telemetry',
        conflicts: 'disable',
    })
        .option('disable', {
        default: undefined,
        type: 'boolean',
        desc: 'Disable anonymous telemetry',
        conflicts: 'enable',
    }))
        .version(helpers.cliVersion())
        .demandCommand(1, '')
        .recommendCommands()
        .help()
        .alias('h', 'help')
        .epilogue('If your app has a single stack, there is no need to specify the stack name\n\nIf one of cdk.json or ~/.cdk.json exists, options specified there will be used as defaults. Settings in cdk.json take precedence.')
        .parse(args);
} // eslint-disable-next-line @typescript-eslint/no-require-imports
const yargs = require('yargs');
//# sourceMappingURL=data:application/json;base64,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