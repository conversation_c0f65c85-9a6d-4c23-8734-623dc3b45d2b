"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isCI = void 0;
exports.yargsNegativeAlias = yargsNegativeAlias;
exports.cliVersion = cliVersion;
exports.browserForPlatform = browserForPlatform;
exports.shouldDisplayNotices = shouldDisplayNotices;
const ci_systems_1 = require("../ci-systems");
const io_host_1 = require("../io-host");
const version = require("../version");
var io_host_2 = require("../io-host");
Object.defineProperty(exports, "isCI", { enumerable: true, get: function () { return io_host_2.isCI; } });
/**
 * yargs middleware to negate an option if a negative alias is provided
 * E.g. `-R` will imply `--rollback=false`
 *
 * @param optionToNegate - The name of the option to negate, e.g. `rollback`
 * @param negativeAlias - The alias that should negate the option, e.g. `R`
 * @returns a middleware function that can be passed to yargs
 */
function yargsNegativeAlias(negativeAlias, optionToNegate) {
    return (argv) => {
        // if R in argv && argv[R]
        // then argv[rollback] = false
        if (negativeAlias in argv && argv[negativeAlias]) {
            argv[optionToNegate] = false;
        }
        return argv;
    };
}
/**
 * Returns the current version of the CLI
 * @returns the current version of the CLI
 */
function cliVersion() {
    return version.displayVersion();
}
/**
 * Returns the default browser command for the current platform
 * @returns the default browser command for the current platform
 */
function browserForPlatform() {
    switch (process.platform) {
        case 'darwin':
            return 'open %u';
        case 'win32':
            return 'start %u';
        default:
            return 'xdg-open %u';
    }
}
/**
 * The default value for displaying (and refreshing) notices on all commands.
 *
 * If the user didn't supply either `--notices` or `--no-notices`, we do
 * autodetection. The autodetection currently is: do write notices if we are
 * not on CI, or are on a CI system where we know that writing to stderr is
 * safe. We fail "closed"; that is, we decide to NOT print for unknown CI
 * systems, even though technically we maybe could.
 */
function shouldDisplayNotices() {
    return !(0, io_host_1.isCI)() || Boolean((0, ci_systems_1.ciSystemIsStdErrSafe)());
}
//# sourceMappingURL=data:application/json;base64,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