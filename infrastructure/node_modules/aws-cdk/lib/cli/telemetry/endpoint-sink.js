"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EndpointTelemetrySink = void 0;
const https_1 = require("https");
const toolkit_lib_1 = require("@aws-cdk/toolkit-lib");
const api_private_1 = require("../../api-private");
const REQUEST_ATTEMPT_TIMEOUT_MS = 500;
/**
 * The telemetry client that hits an external endpoint.
 */
class EndpointTelemetrySink {
    constructor(props) {
        this.events = [];
        this.endpoint = props.endpoint;
        this.ioHelper = api_private_1.IoHelper.fromActionAwareIoHost(props.ioHost);
        this.agent = props.agent;
        // Batch events every 30 seconds
        setInterval(() => this.flush(), 30000).unref();
    }
    /**
     * Add an event to the collection.
     */
    async emit(event) {
        try {
            this.events.push(event);
        }
        catch (e) {
            // Never throw errors, just log them via ioHost
            await this.ioHelper.defaults.trace(`Failed to add telemetry event: ${e.message}`);
        }
    }
    async flush() {
        try {
            if (this.events.length === 0) {
                return;
            }
            const res = await this.https(this.endpoint, this.events);
            // Clear the events array after successful output
            if (res) {
                this.events = [];
            }
        }
        catch (e) {
            // Never throw errors, just log them via ioHost
            await this.ioHelper.defaults.trace(`Failed to add telemetry event: ${e.message}`);
        }
    }
    /**
     * Returns true if telemetry successfully posted, false otherwise.
     */
    async https(url, body) {
        try {
            const res = await doRequest(url, body, this.agent);
            // Successfully posted
            if (res.statusCode && res.statusCode >= 200 && res.statusCode < 300) {
                return true;
            }
            await this.ioHelper.defaults.trace(`Telemetry Unsuccessful: POST ${url.hostname}${url.pathname}: ${res.statusCode}:${res.statusMessage}`);
            return false;
        }
        catch (e) {
            await this.ioHelper.defaults.trace(`Telemetry Error: POST ${url.hostname}${url.pathname}: ${JSON.stringify(e)}`);
            return false;
        }
    }
}
exports.EndpointTelemetrySink = EndpointTelemetrySink;
/**
 * A Promisified version of `https.request()`
 */
function doRequest(url, data, agent) {
    return new Promise((ok, ko) => {
        const payload = JSON.stringify(data);
        const req = (0, https_1.request)({
            hostname: url.hostname,
            port: url.port,
            path: url.pathname,
            method: 'POST',
            headers: {
                'content-type': 'application/json',
                'content-length': payload.length,
            },
            agent,
            timeout: REQUEST_ATTEMPT_TIMEOUT_MS,
        }, ok);
        req.on('error', ko);
        req.on('timeout', () => {
            const error = new toolkit_lib_1.ToolkitError(`Timeout after ${REQUEST_ATTEMPT_TIMEOUT_MS}ms, aborting request`);
            req.destroy(error);
        });
        req.end(payload);
    });
}
//# sourceMappingURL=data:application/json;base64,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