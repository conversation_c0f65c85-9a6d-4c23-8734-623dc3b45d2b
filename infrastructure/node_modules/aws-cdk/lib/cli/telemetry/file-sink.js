"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileTelemetrySink = void 0;
const fs = require("fs");
const path = require("path");
const toolkit_lib_1 = require("@aws-cdk/toolkit-lib");
const api_private_1 = require("../../api-private");
/**
 * A telemetry client that collects events writes them to a file
 */
class FileTelemetrySink {
    /**
     * Create a new FileTelemetryClient
     */
    constructor(props) {
        this.ioHelper = api_private_1.IoHelper.fromActionAwareIoHost(props.ioHost);
        this.logFilePath = props.logFilePath;
        if (fs.existsSync(this.logFilePath)) {
            throw new toolkit_lib_1.ToolkitError(`Telemetry file already exists at ${this.logFilePath}`);
        }
        // Create the file if necessary
        const directory = path.dirname(this.logFilePath);
        if (!fs.existsSync(directory)) {
            fs.mkdirSync(directory, { recursive: true });
        }
    }
    /**
     * Emit an event.
     */
    async emit(event) {
        try {
            // Format the events as a JSON string with pretty printing
            const output = JSON.stringify(event, null, 2) + '\n';
            // Write to file
            fs.appendFileSync(this.logFilePath, output);
        }
        catch (e) {
            // Never throw errors, just log them via ioHost
            await this.ioHelper.defaults.trace(`Failed to add telemetry event: ${e.message}`);
        }
    }
    async flush() {
        return;
    }
}
exports.FileTelemetrySink = FileTelemetrySink;
//# sourceMappingURL=data:application/json;base64,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