"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IoHostTelemetrySink = void 0;
const api_private_1 = require("../../api-private");
/**
 * A telemetry client that collects events and flushes them to stdout.
 */
class IoHostTelemetrySink {
    /**
     * Create a new StdoutTelemetryClient
     */
    constructor(props) {
        this.ioHelper = api_private_1.IoHelper.fromActionAwareIoHost(props.ioHost);
    }
    /**
     * Emit an event
     */
    async emit(event) {
        try {
            // Format the events as a JSON string with pretty printing
            const output = JSON.stringify(event, null, 2);
            // Write to IoHost
            await this.ioHelper.defaults.trace(`--- TELEMETRY EVENT ---\n${output}\n-----------------------\n`);
        }
        catch (e) {
            // Never throw errors, just log them via ioHost
            await this.ioHelper.defaults.trace(`Failed to add telemetry event: ${e.message}`);
        }
    }
    async flush() {
        return;
    }
}
exports.IoHostTelemetrySink = IoHostTelemetrySink;
//# sourceMappingURL=data:application/json;base64,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