"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkForPlatformWarnings = checkForPlatformWarnings;
exports.isVersionBetween = isVersionBetween;
const os = require("os");
const fs = require("fs-extra");
const logging = require("../logging");
async function checkForPlatformWarnings() {
    if (await hasDockerCopyBug()) {
        logging.warning('`cdk synth` may hang in Docker on Linux 5.6-5.10. See https://github.com/aws/aws-cdk/issues/21379 for workarounds.');
    }
}
async function hasDockerCopyBug() {
    return await runningInDocker() && os.platform() === 'linux' && isVersionBetween(os.release(), '5.6', '5.10');
}
async function runningInDocker() {
    return fs.pathExists('/.dockerenv');
}
function isVersionBetween(version, lower, upper) {
    const ver = splitVersion(version);
    const lo = splitVersion(lower);
    const up = splitVersion(upper);
    while (lo.length < ver.length) {
        lo.push(0);
    }
    while (up.length < ver.length) {
        up.push(9999999);
    }
    let n = ver.length;
    for (let i = 0; i < n; i++) {
        if (lo[i] < ver[i] && ver[i] < up[i]) {
            return true;
        }
        if (lo[i] > ver[i] || ver[i] > up[i]) {
            return false;
        }
    }
    return false;
}
function splitVersion(version) {
    return `${version}`.split('.')
        .map(x => parseInt(x, 10))
        .map(x => isNaN(x) ? 0 : x);
}
//# sourceMappingURL=data:application/json;base64,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