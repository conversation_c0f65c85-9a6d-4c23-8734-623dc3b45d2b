"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.YARGS_HELPERS = void 0;
exports.makeConfig = makeConfig;
const cloud_assembly_schema_1 = require("@aws-cdk/cloud-assembly-schema");
// eslint-disable-next-line import/no-extraneous-dependencies
const user_input_gen_1 = require("@aws-cdk/user-input-gen");
const cdk_from_cfn = require("cdk-from-cfn");
const deploy_1 = require("../commands/deploy");
const init_1 = require("../commands/init");
exports.YARGS_HELPERS = new user_input_gen_1.CliHelpers('./util/yargs-helpers');
/**
 * Source of truth for all CDK CLI commands. `user-input-gen` translates this into:
 *
 * - the `yargs` definition in `lib/parse-command-line-arguments.ts`.
 * - the `UserInput` type in `lib/user-input.ts`.
 * - the `convertXxxToUserInput` functions in `lib/convert-to-user-input.ts`.
 */
async function makeConfig() {
    return {
        globalOptions: {
            'app': { type: 'string', alias: 'a', desc: 'REQUIRED WHEN RUNNING APP: command-line for executing your app or a cloud assembly directory (e.g. "node bin/my-app.js"). Can also be specified in cdk.json or ~/.cdk.json', requiresArg: true },
            'build': { type: 'string', desc: 'Command-line for a pre-synth build' },
            'context': { type: 'array', alias: 'c', desc: 'Add contextual string parameter (KEY=VALUE)' },
            'plugin': { type: 'array', alias: 'p', desc: 'Name or path of a node package that extend the CDK features. Can be specified multiple times' },
            'trace': { type: 'boolean', desc: 'Print trace for stack warnings' },
            'strict': { type: 'boolean', desc: 'Do not construct stacks with warnings' },
            'lookups': { type: 'boolean', desc: 'Perform context lookups (synthesis fails if this is disabled and context lookups need to be performed)', default: true },
            'ignore-errors': { type: 'boolean', default: false, desc: 'Ignores synthesis errors, which will likely produce an invalid output' },
            'json': { type: 'boolean', alias: 'j', desc: 'Use JSON output instead of YAML when templates are printed to STDOUT', default: false },
            'verbose': { type: 'boolean', alias: 'v', desc: 'Show debug logs (specify multiple times to increase verbosity)', default: false, count: true },
            'debug': { type: 'boolean', desc: 'Debug the CDK app. Log additional information during synthesis, such as creation stack traces of tokens (sets CDK_DEBUG, will slow down synthesis)', default: false },
            'profile': { type: 'string', desc: 'Use the indicated AWS profile as the default environment', requiresArg: true },
            'proxy': { type: 'string', desc: 'Use the indicated proxy. Will read from HTTPS_PROXY environment variable if not specified', requiresArg: true },
            'ca-bundle-path': { type: 'string', desc: 'Path to CA certificate to use when validating HTTPS requests. Will read from AWS_CA_BUNDLE environment variable if not specified', requiresArg: true },
            'ec2creds': { type: 'boolean', alias: 'i', default: undefined, desc: 'Force trying to fetch EC2 instance credentials. Default: guess EC2 instance status' },
            'version-reporting': { type: 'boolean', desc: 'Include the "AWS::CDK::Metadata" resource in synthesized templates (enabled by default)', default: undefined },
            'path-metadata': { type: 'boolean', desc: 'Include "aws:cdk:path" CloudFormation metadata for each resource (enabled by default)', default: undefined },
            'asset-metadata': { type: 'boolean', desc: 'Include "aws:asset:*" CloudFormation metadata for resources that uses assets (enabled by default)', default: undefined },
            'role-arn': { type: 'string', alias: 'r', desc: 'ARN of Role to use when invoking CloudFormation', default: undefined, requiresArg: true },
            'staging': { type: 'boolean', desc: 'Copy assets to the output directory (use --no-staging to disable the copy of assets which allows local debugging via the SAM CLI to reference the original source files)', default: true },
            'output': { type: 'string', alias: 'o', desc: 'Emits the synthesized cloud assembly into a directory (default: cdk.out)', requiresArg: true },
            'notices': { type: 'boolean', desc: 'Show relevant notices', default: exports.YARGS_HELPERS.shouldDisplayNotices() },
            'no-color': { type: 'boolean', desc: 'Removes colors and other style from console output', default: false },
            'ci': { type: 'boolean', desc: 'Force CI detection. If CI=true then logs will be sent to stdout instead of stderr', default: exports.YARGS_HELPERS.isCI() },
            'unstable': { type: 'array', desc: 'Opt in to unstable features. The flag indicates that the scope and API of a feature might still change. Otherwise the feature is generally production ready and fully supported. Can be specified multiple times.', default: [] },
        },
        commands: {
            'list': {
                arg: {
                    name: 'STACKS',
                    variadic: true,
                },
                aliases: ['ls'],
                description: 'Lists all stacks in the app',
                options: {
                    'long': { type: 'boolean', default: false, alias: 'l', desc: 'Display environment information for each stack' },
                    'show-dependencies': { type: 'boolean', default: false, alias: 'd', desc: 'Display stack dependency information for each stack' },
                },
            },
            'synth': {
                arg: {
                    name: 'STACKS',
                    variadic: true,
                },
                aliases: ['synthesize'],
                description: 'Synthesizes and prints the CloudFormation template for this stack',
                options: {
                    exclusively: { type: 'boolean', alias: 'e', desc: 'Only synthesize requested stacks, don\'t include dependencies' },
                    validation: { type: 'boolean', desc: 'After synthesis, validate stacks with the "validateOnSynth" attribute set (can also be controlled with CDK_VALIDATION)', default: true },
                    quiet: { type: 'boolean', alias: 'q', desc: 'Do not output CloudFormation Template to stdout', default: false },
                },
            },
            'bootstrap': {
                arg: {
                    name: 'ENVIRONMENTS',
                    variadic: true,
                },
                description: 'Deploys the CDK toolkit stack into an AWS environment',
                options: {
                    'bootstrap-bucket-name': { type: 'string', alias: ['b', 'toolkit-bucket-name'], desc: 'The name of the CDK toolkit bucket; bucket will be created and must not exist', default: undefined },
                    'bootstrap-kms-key-id': { type: 'string', desc: 'AWS KMS master key ID used for the SSE-KMS encryption (specify AWS_MANAGED_KEY to use an AWS-managed key)', default: undefined, conflicts: 'bootstrap-customer-key' },
                    'example-permissions-boundary': { type: 'boolean', alias: 'epb', desc: 'Use the example permissions boundary.', default: undefined, conflicts: 'custom-permissions-boundary' },
                    'custom-permissions-boundary': { type: 'string', alias: 'cpb', desc: 'Use the permissions boundary specified by name.', default: undefined, conflicts: 'example-permissions-boundary' },
                    'bootstrap-customer-key': { type: 'boolean', desc: 'Create a Customer Master Key (CMK) for the bootstrap bucket (you will be charged but can customize permissions, modern bootstrapping only)', default: undefined, conflicts: 'bootstrap-kms-key-id' },
                    'qualifier': { type: 'string', desc: 'String which must be unique for each bootstrap stack. You must configure it on your CDK app if you change this from the default.', default: undefined },
                    'public-access-block-configuration': { type: 'boolean', desc: 'Block public access configuration on CDK toolkit bucket (enabled by default) ', default: undefined },
                    'tags': { type: 'array', alias: 't', desc: 'Tags to add for the stack (KEY=VALUE)', default: [] },
                    'execute': { type: 'boolean', desc: 'Whether to execute ChangeSet (--no-execute will NOT execute the ChangeSet)', default: true },
                    'trust': { type: 'array', desc: 'The AWS account IDs that should be trusted to perform deployments into this environment (may be repeated, modern bootstrapping only)', default: [] },
                    'trust-for-lookup': { type: 'array', desc: 'The AWS account IDs that should be trusted to look up values in this environment (may be repeated, modern bootstrapping only)', default: [] },
                    'untrust': { type: 'array', desc: 'The AWS account IDs that should not be trusted by this environment (may be repeated, modern bootstrapping only)', default: [] },
                    'cloudformation-execution-policies': { type: 'array', desc: 'The Managed Policy ARNs that should be attached to the role performing deployments into this environment (may be repeated, modern bootstrapping only)', default: [] },
                    'force': { alias: 'f', type: 'boolean', desc: 'Always bootstrap even if it would downgrade template version', default: false },
                    'termination-protection': { type: 'boolean', default: undefined, desc: 'Toggle CloudFormation termination protection on the bootstrap stacks' },
                    'show-template': { type: 'boolean', desc: 'Instead of actual bootstrapping, print the current CLI\'s bootstrapping template to stdout for customization', default: false },
                    'toolkit-stack-name': { type: 'string', desc: 'The name of the CDK toolkit stack to create', requiresArg: true },
                    'template': { type: 'string', requiresArg: true, desc: 'Use the template from the given file instead of the built-in one (use --show-template to obtain an example)' },
                    'previous-parameters': { type: 'boolean', default: true, desc: 'Use previous values for existing parameters (you must specify all parameters on every deployment if this is disabled)' },
                },
            },
            'gc': {
                description: 'Garbage collect assets. Options detailed here: https://github.com/aws/aws-cdk-cli/tree/main/packages/aws-cdk#cdk-gc',
                arg: {
                    name: 'ENVIRONMENTS',
                    variadic: true,
                },
                options: {
                    'action': { type: 'string', desc: 'The action (or sub-action) you want to perform. Valid entires are "print", "tag", "delete-tagged", "full".', default: 'full' },
                    'type': { type: 'string', desc: 'Specify either ecr, s3, or all', default: 'all' },
                    'rollback-buffer-days': { type: 'number', desc: 'Delete assets that have been marked as isolated for this many days', default: 0 },
                    'created-buffer-days': { type: 'number', desc: 'Never delete assets younger than this (in days)', default: 1 },
                    'confirm': { type: 'boolean', desc: 'Confirm via manual prompt before deletion', default: true },
                    'bootstrap-stack-name': { type: 'string', desc: 'The name of the CDK toolkit stack, if different from the default "CDKToolkit"', requiresArg: true },
                },
            },
            'deploy': {
                description: 'Deploys the stack(s) named STACKS into your AWS account',
                options: {
                    'all': { type: 'boolean', desc: 'Deploy all available stacks', default: false },
                    'build-exclude': { type: 'array', alias: 'E', desc: 'Do not rebuild asset with the given ID. Can be specified multiple times', default: [] },
                    'exclusively': { type: 'boolean', alias: 'e', desc: 'Only deploy requested stacks, don\'t include dependencies' },
                    'require-approval': { type: 'string', choices: [cloud_assembly_schema_1.RequireApproval.NEVER, cloud_assembly_schema_1.RequireApproval.ANYCHANGE, cloud_assembly_schema_1.RequireApproval.BROADENING], desc: 'What security-sensitive changes need manual approval' },
                    'notification-arns': { type: 'array', desc: 'ARNs of SNS topics that CloudFormation will notify with stack related events. These will be added to ARNs specified with the \'notificationArns\' stack property.' },
                    // @deprecated(v2) -- tags are part of the Cloud Assembly and tags specified here will be overwritten on the next deployment
                    'tags': { type: 'array', alias: 't', desc: 'Tags to add to the stack (KEY=VALUE), overrides tags from Cloud Assembly (deprecated)' },
                    'execute': { type: 'boolean', desc: 'Whether to execute ChangeSet (--no-execute will NOT execute the ChangeSet) (deprecated)', deprecated: true },
                    'change-set-name': { type: 'string', desc: 'Name of the CloudFormation change set to create (only if method is not direct)' },
                    'method': {
                        alias: 'm',
                        type: 'string',
                        choices: ['direct', 'change-set', 'prepare-change-set'],
                        requiresArg: true,
                        desc: 'How to perform the deployment. Direct is a bit faster but lacks progress information',
                    },
                    'import-existing-resources': { type: 'boolean', desc: 'Indicates if the stack set imports resources that already exist.', default: false },
                    'force': { alias: 'f', type: 'boolean', desc: 'Always deploy stack even if templates are identical', default: false },
                    'parameters': { type: 'array', desc: 'Additional parameters passed to CloudFormation at deploy time (STACK:KEY=VALUE)', default: {} },
                    'outputs-file': { type: 'string', alias: 'O', desc: 'Path to file where stack outputs will be written as JSON', requiresArg: true },
                    'previous-parameters': { type: 'boolean', default: true, desc: 'Use previous values for existing parameters (you must specify all parameters on every deployment if this is disabled)' },
                    'toolkit-stack-name': { type: 'string', desc: 'The name of the existing CDK toolkit stack (only used for app using legacy synthesis)', requiresArg: true },
                    'progress': { type: 'string', choices: [deploy_1.StackActivityProgress.BAR, deploy_1.StackActivityProgress.EVENTS], desc: 'Display mode for stack activity events' },
                    'rollback': {
                        type: 'boolean',
                        desc: "Rollback stack to stable state on failure. Defaults to 'true', iterate more rapidly with --no-rollback or -R. " +
                            'Note: do **not** disable this flag for deployments with resource replacements, as that will always fail',
                        negativeAlias: 'R',
                    },
                    'hotswap': {
                        type: 'boolean',
                        desc: "Attempts to perform a 'hotswap' deployment, " +
                            'but does not fall back to a full deployment if that is not possible. ' +
                            'Instead, changes to any non-hotswappable properties are ignored.' +
                            'Do not use this in production environments',
                    },
                    'hotswap-fallback': {
                        type: 'boolean',
                        desc: "Attempts to perform a 'hotswap' deployment, " +
                            'which skips CloudFormation and updates the resources directly, ' +
                            'and falls back to a full deployment if that is not possible. ' +
                            'Do not use this in production environments',
                    },
                    'hotswap-ecs-minimum-healthy-percent': {
                        type: 'string',
                        desc: 'Lower limit on the number of your service\'s tasks that must remain in the RUNNING state during a deployment, as a percentage of the desiredCount',
                    },
                    'hotswap-ecs-maximum-healthy-percent': {
                        type: 'string',
                        desc: 'Upper limit on the number of your service\'s tasks that are allowed in the RUNNING or PENDING state during a deployment, as a percentage of the desiredCount',
                    },
                    'hotswap-ecs-stabilization-timeout-seconds': {
                        type: 'string',
                        desc: 'Number of seconds to wait for a single service to reach stable state, where the desiredCount is equal to the runningCount',
                    },
                    'watch': {
                        type: 'boolean',
                        desc: 'Continuously observe the project files, ' +
                            'and deploy the given stack(s) automatically when changes are detected. ' +
                            'Implies --hotswap by default',
                    },
                    'logs': {
                        type: 'boolean',
                        default: true,
                        desc: 'Show CloudWatch log events from all resources in the selected Stacks in the terminal. ' +
                            "'true' by default, use --no-logs to turn off. " +
                            "Only in effect if specified alongside the '--watch' option",
                    },
                    'concurrency': { type: 'number', desc: 'Maximum number of simultaneous deployments (dependency permitting) to execute.', default: 1, requiresArg: true },
                    'asset-parallelism': { type: 'boolean', desc: 'Whether to build/publish assets in parallel' },
                    'asset-prebuild': { type: 'boolean', desc: 'Whether to build all assets before deploying the first stack (useful for failing Docker builds)', default: true },
                    'ignore-no-stacks': { type: 'boolean', desc: 'Whether to deploy if the app contains no stacks', default: false },
                },
                arg: {
                    name: 'STACKS',
                    variadic: true,
                },
            },
            'rollback': {
                description: 'Rolls back the stack(s) named STACKS to their last stable state',
                arg: {
                    name: 'STACKS',
                    variadic: true,
                },
                options: {
                    'all': { type: 'boolean', default: false, desc: 'Roll back all available stacks' },
                    'toolkit-stack-name': { type: 'string', desc: 'The name of the CDK toolkit stack the environment is bootstrapped with', requiresArg: true },
                    'force': {
                        alias: 'f',
                        type: 'boolean',
                        desc: 'Orphan all resources for which the rollback operation fails.',
                    },
                    'validate-bootstrap-version': {
                        type: 'boolean',
                        desc: 'Whether to validate the bootstrap stack version. Defaults to \'true\', disable with --no-validate-bootstrap-version.',
                    },
                    'orphan': {
                        // alias: 'o' conflicts with --output
                        type: 'array',
                        desc: 'Orphan the given resources, identified by their logical ID (can be specified multiple times)',
                        default: [],
                    },
                },
            },
            'import': {
                description: 'Import existing resource(s) into the given STACK',
                arg: {
                    name: 'STACK',
                    variadic: false,
                },
                options: {
                    'execute': { type: 'boolean', desc: 'Whether to execute ChangeSet (--no-execute will NOT execute the ChangeSet)', default: true },
                    'change-set-name': { type: 'string', desc: 'Name of the CloudFormation change set to create' },
                    'toolkit-stack-name': { type: 'string', desc: 'The name of the CDK toolkit stack to create', requiresArg: true },
                    'rollback': {
                        type: 'boolean',
                        desc: "Rollback stack to stable state on failure. Defaults to 'true', iterate more rapidly with --no-rollback or -R. " +
                            'Note: do **not** disable this flag for deployments with resource replacements, as that will always fail',
                    },
                    'force': {
                        alias: 'f',
                        type: 'boolean',
                        desc: 'Do not abort if the template diff includes updates or deletes. This is probably safe but we\'re not sure, let us know how it goes.',
                    },
                    'record-resource-mapping': {
                        type: 'string',
                        alias: 'r',
                        requiresArg: true,
                        desc: 'If specified, CDK will generate a mapping of existing physical resources to CDK resources to be imported as. The mapping ' +
                            'will be written in the given file path. No actual import operation will be performed',
                    },
                    'resource-mapping': {
                        type: 'string',
                        alias: 'm',
                        requiresArg: true,
                        desc: 'If specified, CDK will use the given file to map physical resources to CDK resources for import, instead of interactively ' +
                            'asking the user. Can be run from scripts',
                    },
                },
            },
            'watch': {
                description: "Shortcut for 'deploy --watch'",
                arg: {
                    name: 'STACKS',
                    variadic: true,
                },
                options: {
                    'build-exclude': { type: 'array', alias: 'E', desc: 'Do not rebuild asset with the given ID. Can be specified multiple times', default: [] },
                    'exclusively': { type: 'boolean', alias: 'e', desc: 'Only deploy requested stacks, don\'t include dependencies' },
                    'change-set-name': { type: 'string', desc: 'Name of the CloudFormation change set to create' },
                    'force': { alias: 'f', type: 'boolean', desc: 'Always deploy stack even if templates are identical', default: false },
                    'toolkit-stack-name': { type: 'string', desc: 'The name of the existing CDK toolkit stack (only used for app using legacy synthesis)', requiresArg: true },
                    'progress': { type: 'string', choices: [deploy_1.StackActivityProgress.BAR, deploy_1.StackActivityProgress.EVENTS], desc: 'Display mode for stack activity events' },
                    'rollback': {
                        type: 'boolean',
                        desc: "Rollback stack to stable state on failure. Defaults to 'true', iterate more rapidly with --no-rollback or -R. " +
                            'Note: do **not** disable this flag for deployments with resource replacements, as that will always fail',
                        negativeAlias: 'R',
                    },
                    'hotswap': {
                        type: 'boolean',
                        desc: "Attempts to perform a 'hotswap' deployment, " +
                            'but does not fall back to a full deployment if that is not possible. ' +
                            'Instead, changes to any non-hotswappable properties are ignored.' +
                            "'true' by default, use --no-hotswap to turn off",
                    },
                    'hotswap-fallback': {
                        type: 'boolean',
                        desc: "Attempts to perform a 'hotswap' deployment, " +
                            'which skips CloudFormation and updates the resources directly, ' +
                            'and falls back to a full deployment if that is not possible.',
                    },
                    'hotswap-ecs-minimum-healthy-percent': {
                        type: 'string',
                        desc: 'Lower limit on the number of your service\'s tasks that must remain in the RUNNING state during a deployment, as a percentage of the desiredCount',
                    },
                    'hotswap-ecs-maximum-healthy-percent': {
                        type: 'string',
                        desc: 'Upper limit on the number of your service\'s tasks that are allowed in the RUNNING or PENDING state during a deployment, as a percentage of the desiredCount',
                    },
                    'hotswap-ecs-stabilization-timeout-seconds': {
                        type: 'string',
                        desc: 'Number of seconds to wait for a single service to reach stable state, where the desiredCount is equal to the runningCount',
                    },
                    'logs': {
                        type: 'boolean',
                        default: true,
                        desc: 'Show CloudWatch log events from all resources in the selected Stacks in the terminal. ' +
                            "'true' by default, use --no-logs to turn off",
                    },
                    'concurrency': { type: 'number', desc: 'Maximum number of simultaneous deployments (dependency permitting) to execute.', default: 1, requiresArg: true },
                },
            },
            'destroy': {
                description: 'Destroy the stack(s) named STACKS',
                arg: {
                    name: 'STACKS',
                    variadic: true,
                },
                options: {
                    all: { type: 'boolean', default: false, desc: 'Destroy all available stacks' },
                    exclusively: { type: 'boolean', alias: 'e', desc: 'Only destroy requested stacks, don\'t include dependees' },
                    force: { type: 'boolean', alias: 'f', desc: 'Do not ask for confirmation before destroying the stacks' },
                },
            },
            'diff': {
                description: 'Compares the specified stack with the deployed stack or a local template file, and returns with status 1 if any difference is found',
                arg: {
                    name: 'STACKS',
                    variadic: true,
                },
                options: {
                    'exclusively': { type: 'boolean', alias: 'e', desc: 'Only diff requested stacks, don\'t include dependencies' },
                    'context-lines': { type: 'number', desc: 'Number of context lines to include in arbitrary JSON diff rendering', default: 3, requiresArg: true },
                    'template': { type: 'string', desc: 'The path to the CloudFormation template to compare with', requiresArg: true },
                    'strict': { type: 'boolean', desc: 'Do not filter out AWS::CDK::Metadata resources, mangled non-ASCII characters, or the CheckBootstrapVersionRule', default: false },
                    'security-only': { type: 'boolean', desc: 'Only diff for broadened security changes', default: false },
                    'fail': { type: 'boolean', desc: 'Fail with exit code 1 in case of diff' },
                    'processed': { type: 'boolean', desc: 'Whether to compare against the template with Transforms already processed', default: false },
                    'quiet': { type: 'boolean', alias: 'q', desc: 'Do not print stack name and default message when there is no diff to stdout', default: false },
                    'change-set': { type: 'boolean', alias: 'changeset', desc: 'Whether to create a changeset to analyze resource replacements. In this mode, diff will use the deploy role instead of the lookup role.', default: true },
                    'import-existing-resources': { type: 'boolean', desc: 'Whether or not the change set imports resources that already exist', default: false },
                },
            },
            'drift': {
                description: 'Detect drifts in the given CloudFormation stack(s)',
                arg: {
                    name: 'STACKS',
                    variadic: true,
                },
                options: {
                    fail: { type: 'boolean', desc: 'Fail with exit code 1 if drift is detected' },
                },
            },
            'metadata': {
                description: 'Returns all metadata associated with this stack',
                arg: {
                    name: 'STACK',
                    variadic: false,
                },
            },
            'acknowledge': {
                aliases: ['ack'],
                description: 'Acknowledge a notice so that it does not show up anymore',
                arg: {
                    name: 'ID',
                    variadic: false,
                },
            },
            'notices': {
                description: 'Returns a list of relevant notices',
                options: {
                    unacknowledged: { type: 'boolean', alias: 'u', default: false, desc: 'Returns a list of unacknowledged notices' },
                },
            },
            'init': {
                description: 'Create a new, empty CDK project from a template.',
                arg: {
                    name: 'TEMPLATE',
                    variadic: false,
                },
                options: {
                    'language': { type: 'string', alias: 'l', desc: 'The language to be used for the new project (default can be configured in ~/.cdk.json)', choices: await (0, init_1.availableInitLanguages)() },
                    'list': { type: 'boolean', desc: 'List the available templates' },
                    'generate-only': { type: 'boolean', default: false, desc: 'If true, only generates project files, without executing additional operations such as setting up a git repo, installing dependencies or compiling the project' },
                    'lib-version': { type: 'string', alias: 'V', default: undefined, desc: 'The version of the CDK library (aws-cdk-lib) to initialize the project with. Defaults to the version that was current when this CLI was built.' },
                },
            },
            'migrate': {
                description: 'Migrate existing AWS resources into a CDK app',
                options: {
                    'stack-name': { type: 'string', alias: 'n', desc: 'The name assigned to the stack created in the new project. The name of the app will be based off this name as well.', requiresArg: true },
                    'language': { type: 'string', default: 'typescript', alias: 'l', desc: 'The language to be used for the new project', choices: cdk_from_cfn.supported_languages() },
                    'account': { type: 'string', desc: 'The account to retrieve the CloudFormation stack template from' },
                    'region': { type: 'string', desc: 'The region to retrieve the CloudFormation stack template from' },
                    'from-path': { type: 'string', desc: 'The path to the CloudFormation template to migrate. Use this for locally stored templates' },
                    'from-stack': { type: 'boolean', desc: 'Use this flag to retrieve the template for an existing CloudFormation stack' },
                    'output-path': { type: 'string', desc: 'The output path for the migrated CDK app' },
                    'from-scan': {
                        type: 'string',
                        desc: 'Determines if a new scan should be created, or the last successful existing scan should be used ' +
                            '\n options are "new" or "most-recent"',
                    },
                    'filter': {
                        type: 'array',
                        desc: 'Filters the resource scan based on the provided criteria in the following format: "key1=value1,key2=value2"' +
                            '\n This field can be passed multiple times for OR style filtering: ' +
                            '\n filtering options: ' +
                            '\n resource-identifier: A key-value pair that identifies the target resource. i.e. {"ClusterName", "myCluster"}' +
                            '\n resource-type-prefix: A string that represents a type-name prefix. i.e. "AWS::DynamoDB::"' +
                            '\n tag-key: a string that matches resources with at least one tag with the provided key. i.e. "myTagKey"' +
                            '\n tag-value: a string that matches resources with at least one tag with the provided value. i.e. "myTagValue"',
                    },
                    'compress': { type: 'boolean', desc: 'Use this flag to zip the generated CDK app' },
                },
            },
            'context': {
                description: 'Manage cached context values',
                options: {
                    reset: { alias: 'e', desc: 'The context key (or its index) to reset', type: 'string', requiresArg: true, default: undefined },
                    force: { alias: 'f', desc: 'Ignore missing key error', type: 'boolean', default: false },
                    clear: { desc: 'Clear all context', type: 'boolean', default: false },
                },
            },
            'docs': {
                aliases: ['doc'],
                description: 'Opens the reference documentation in a browser',
                options: {
                    browser: {
                        alias: 'b',
                        desc: 'the command to use to open the browser, using %u as a placeholder for the path of the file to open',
                        type: 'string',
                        default: exports.YARGS_HELPERS.browserForPlatform(),
                    },
                },
            },
            'doctor': {
                description: 'Check your set-up for potential problems',
            },
            'refactor': {
                description: 'Moves resources between stacks or within the same stack',
                arg: {
                    name: 'STACKS',
                    variadic: true,
                },
                options: {
                    'dry-run': {
                        type: 'boolean',
                        desc: 'Do not perform any changes, just show what would be done',
                        default: false,
                    },
                    'exclude-file': {
                        type: 'string',
                        requiresArg: true,
                        desc: 'If specified, CDK will use the given file to exclude resources from the refactor',
                    },
                    'mapping-file': {
                        type: 'string',
                        requiresArg: true,
                        desc: 'A file that declares an explicit mapping to be applied. If provided, the command will use it instead of computing the mapping.',
                    },
                    'revert': {
                        type: 'boolean',
                        default: false,
                        desc: 'If specified, the command will revert the refactor operation. This is only valid if a mapping file was provided.',
                    },
                },
            },
            'cli-telemetry': {
                description: 'Enable or disable anonymous telemetry',
                options: {
                    enable: {
                        type: 'boolean',
                        desc: 'Enable anonymous telemetry',
                        conflicts: 'disable',
                    },
                    disable: {
                        type: 'boolean',
                        desc: 'Disable anonymous telemetry',
                        conflicts: 'enable',
                    },
                },
            },
        },
    };
}
//# sourceMappingURL=data:application/json;base64,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