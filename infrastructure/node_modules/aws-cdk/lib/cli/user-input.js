"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidXNlci1pbnB1dC5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbInVzZXItaW5wdXQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZXNDb250ZW50IjpbIi8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbi8vIEdFTkVSQVRFRCBGUk9NIHBhY2thZ2VzL2F3cy1jZGsvbGliL2NsaS9jbGktY29uZmlnLnRzLlxuLy8gRG8gbm90IGVkaXQgYnkgaGFuZDsgYWxsIGNoYW5nZXMgd2lsbCBiZSBvdmVyd3JpdHRlbiBhdCBidWlsZCB0aW1lIGZyb20gdGhlIGNvbmZpZyBmaWxlLlxuLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuLyogZXNsaW50LWRpc2FibGUgQHN0eWxpc3RpYy9tYXgtbGVuLCBAdHlwZXNjcmlwdC1lc2xpbnQvY29uc2lzdGVudC10eXBlLWltcG9ydHMgKi9cbmltcG9ydCB7IENvbW1hbmQgfSBmcm9tICcuL3VzZXItY29uZmlndXJhdGlvbic7XG5cbi8qKlxuICogVGhlIHN0cnVjdHVyZSBvZiB0aGUgdXNlciBpbnB1dCAtLSBlaXRoZXIgQ0xJIG9wdGlvbnMgb3IgY2RrLmpzb24gLS0gZ2VuZXJhdGVkIGZyb20gcGFja2FnZXMvYXdzLWNkay9saWIvY29uZmlnLnRzXG4gKlxuICogQHN0cnVjdFxuICovXG5leHBvcnQgaW50ZXJmYWNlIFVzZXJJbnB1dCB7XG4gIC8qKlxuICAgKiBUaGUgQ0xJIGNvbW1hbmQgbmFtZVxuICAgKi9cbiAgcmVhZG9ubHkgY29tbWFuZD86IENvbW1hbmQ7XG5cbiAgLyoqXG4gICAqIEdsb2JhbCBvcHRpb25zIGF2YWlsYWJsZSB0byBhbGwgQ0xJIGNvbW1hbmRzXG4gICAqL1xuICByZWFkb25seSBnbG9iYWxPcHRpb25zPzogR2xvYmFsT3B0aW9ucztcblxuICAvKipcbiAgICogTGlzdHMgYWxsIHN0YWNrcyBpbiB0aGUgYXBwXG4gICAqXG4gICAqIGFsaWFzZXM6IGxzXG4gICAqL1xuICByZWFkb25seSBsaXN0PzogTGlzdE9wdGlvbnM7XG5cbiAgLyoqXG4gICAqIFN5bnRoZXNpemVzIGFuZCBwcmludHMgdGhlIENsb3VkRm9ybWF0aW9uIHRlbXBsYXRlIGZvciB0aGlzIHN0YWNrXG4gICAqXG4gICAqIGFsaWFzZXM6IHN5bnRoZXNpemVcbiAgICovXG4gIHJlYWRvbmx5IHN5bnRoPzogU3ludGhPcHRpb25zO1xuXG4gIC8qKlxuICAgKiBEZXBsb3lzIHRoZSBDREsgdG9vbGtpdCBzdGFjayBpbnRvIGFuIEFXUyBlbnZpcm9ubWVudFxuICAgKi9cbiAgcmVhZG9ubHkgYm9vdHN0cmFwPzogQm9vdHN0cmFwT3B0aW9ucztcblxuICAvKipcbiAgICogR2FyYmFnZSBjb2xsZWN0IGFzc2V0cy4gT3B0aW9ucyBkZXRhaWxlZCBoZXJlOiBodHRwczovL2dpdGh1Yi5jb20vYXdzL2F3cy1jZGstY2xpL3RyZWUvbWFpbi9wYWNrYWdlcy9hd3MtY2RrI2Nkay1nY1xuICAgKi9cbiAgcmVhZG9ubHkgZ2M/OiBHY09wdGlvbnM7XG5cbiAgLyoqXG4gICAqIERlcGxveXMgdGhlIHN0YWNrKHMpIG5hbWVkIFNUQUNLUyBpbnRvIHlvdXIgQVdTIGFjY291bnRcbiAgICovXG4gIHJlYWRvbmx5IGRlcGxveT86IERlcGxveU9wdGlvbnM7XG5cbiAgLyoqXG4gICAqIFJvbGxzIGJhY2sgdGhlIHN0YWNrKHMpIG5hbWVkIFNUQUNLUyB0byB0aGVpciBsYXN0IHN0YWJsZSBzdGF0ZVxuICAgKi9cbiAgcmVhZG9ubHkgcm9sbGJhY2s/OiBSb2xsYmFja09wdGlvbnM7XG5cbiAgLyoqXG4gICAqIEltcG9ydCBleGlzdGluZyByZXNvdXJjZShzKSBpbnRvIHRoZSBnaXZlbiBTVEFDS1xuICAgKi9cbiAgcmVhZG9ubHkgaW1wb3J0PzogSW1wb3J0T3B0aW9ucztcblxuICAvKipcbiAgICogU2hvcnRjdXQgZm9yICdkZXBsb3kgLS13YXRjaCdcbiAgICovXG4gIHJlYWRvbmx5IHdhdGNoPzogV2F0Y2hPcHRpb25zO1xuXG4gIC8qKlxuICAgKiBEZXN0cm95IHRoZSBzdGFjayhzKSBuYW1lZCBTVEFDS1NcbiAgICovXG4gIHJlYWRvbmx5IGRlc3Ryb3k/OiBEZXN0cm95T3B0aW9ucztcblxuICAvKipcbiAgICogQ29tcGFyZXMgdGhlIHNwZWNpZmllZCBzdGFjayB3aXRoIHRoZSBkZXBsb3llZCBzdGFjayBvciBhIGxvY2FsIHRlbXBsYXRlIGZpbGUsIGFuZCByZXR1cm5zIHdpdGggc3RhdHVzIDEgaWYgYW55IGRpZmZlcmVuY2UgaXMgZm91bmRcbiAgICovXG4gIHJlYWRvbmx5IGRpZmY/OiBEaWZmT3B0aW9ucztcblxuICAvKipcbiAgICogRGV0ZWN0IGRyaWZ0cyBpbiB0aGUgZ2l2ZW4gQ2xvdWRGb3JtYXRpb24gc3RhY2socylcbiAgICovXG4gIHJlYWRvbmx5IGRyaWZ0PzogRHJpZnRPcHRpb25zO1xuXG4gIC8qKlxuICAgKiBSZXR1cm5zIGFsbCBtZXRhZGF0YSBhc3NvY2lhdGVkIHdpdGggdGhpcyBzdGFja1xuICAgKi9cbiAgcmVhZG9ubHkgbWV0YWRhdGE/OiBNZXRhZGF0YU9wdGlvbnM7XG5cbiAgLyoqXG4gICAqIEFja25vd2xlZGdlIGEgbm90aWNlIHNvIHRoYXQgaXQgZG9lcyBub3Qgc2hvdyB1cCBhbnltb3JlXG4gICAqXG4gICAqIGFsaWFzZXM6IGFja1xuICAgKi9cbiAgcmVhZG9ubHkgYWNrbm93bGVkZ2U/OiBBY2tub3dsZWRnZU9wdGlvbnM7XG5cbiAgLyoqXG4gICAqIFJldHVybnMgYSBsaXN0IG9mIHJlbGV2YW50IG5vdGljZXNcbiAgICovXG4gIHJlYWRvbmx5IG5vdGljZXM/OiBOb3RpY2VzT3B0aW9ucztcblxuICAvKipcbiAgICogQ3JlYXRlIGEgbmV3LCBlbXB0eSBDREsgcHJvamVjdCBmcm9tIGEgdGVtcGxhdGUuXG4gICAqL1xuICByZWFkb25seSBpbml0PzogSW5pdE9wdGlvbnM7XG5cbiAgLyoqXG4gICAqIE1pZ3JhdGUgZXhpc3RpbmcgQVdTIHJlc291cmNlcyBpbnRvIGEgQ0RLIGFwcFxuICAgKi9cbiAgcmVhZG9ubHkgbWlncmF0ZT86IE1pZ3JhdGVPcHRpb25zO1xuXG4gIC8qKlxuICAgKiBNYW5hZ2UgY2FjaGVkIGNvbnRleHQgdmFsdWVzXG4gICAqL1xuICByZWFkb25seSBjb250ZXh0PzogQ29udGV4dE9wdGlvbnM7XG5cbiAgLyoqXG4gICAqIE9wZW5zIHRoZSByZWZlcmVuY2UgZG9jdW1lbnRhdGlvbiBpbiBhIGJyb3dzZXJcbiAgICpcbiAgICogYWxpYXNlczogZG9jXG4gICAqL1xuICByZWFkb25seSBkb2NzPzogRG9jc09wdGlvbnM7XG5cbiAgLyoqXG4gICAqIENoZWNrIHlvdXIgc2V0LXVwIGZvciBwb3RlbnRpYWwgcHJvYmxlbXNcbiAgICovXG4gIHJlYWRvbmx5IGRvY3Rvcj86IHt9O1xuXG4gIC8qKlxuICAgKiBNb3ZlcyByZXNvdXJjZXMgYmV0d2VlbiBzdGFja3Mgb3Igd2l0aGluIHRoZSBzYW1lIHN0YWNrXG4gICAqL1xuICByZWFkb25seSByZWZhY3Rvcj86IFJlZmFjdG9yT3B0aW9ucztcblxuICAvKipcbiAgICogRW5hYmxlIG9yIGRpc2FibGUgYW5vbnltb3VzIHRlbGVtZXRyeVxuICAgKi9cbiAgcmVhZG9ubHkgY2xpVGVsZW1ldHJ5PzogQ2xpVGVsZW1ldHJ5T3B0aW9ucztcbn1cblxuLyoqXG4gKiBHbG9iYWwgb3B0aW9ucyBhdmFpbGFibGUgdG8gYWxsIENMSSBjb21tYW5kc1xuICpcbiAqIEBzdHJ1Y3RcbiAqL1xuZXhwb3J0IGludGVyZmFjZSBHbG9iYWxPcHRpb25zIHtcbiAgLyoqXG4gICAqIFJFUVVJUkVEIFdIRU4gUlVOTklORyBBUFA6IGNvbW1hbmQtbGluZSBmb3IgZXhlY3V0aW5nIHlvdXIgYXBwIG9yIGEgY2xvdWQgYXNzZW1ibHkgZGlyZWN0b3J5IChlLmcuIFwibm9kZSBiaW4vbXktYXBwLmpzXCIpLiBDYW4gYWxzbyBiZSBzcGVjaWZpZWQgaW4gY2RrLmpzb24gb3Igfi8uY2RrLmpzb25cbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IGFwcD86IHN0cmluZztcblxuICAvKipcbiAgICogQ29tbWFuZC1saW5lIGZvciBhIHByZS1zeW50aCBidWlsZFxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgYnVpbGQ/OiBzdHJpbmc7XG5cbiAgLyoqXG4gICAqIEFkZCBjb250ZXh0dWFsIHN0cmluZyBwYXJhbWV0ZXIgKEtFWT1WQUxVRSlcbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IGNvbnRleHQ/OiBBcnJheTxzdHJpbmc+O1xuXG4gIC8qKlxuICAgKiBOYW1lIG9yIHBhdGggb2YgYSBub2RlIHBhY2thZ2UgdGhhdCBleHRlbmQgdGhlIENESyBmZWF0dXJlcy4gQ2FuIGJlIHNwZWNpZmllZCBtdWx0aXBsZSB0aW1lc1xuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgcGx1Z2luPzogQXJyYXk8c3RyaW5nPjtcblxuICAvKipcbiAgICogUHJpbnQgdHJhY2UgZm9yIHN0YWNrIHdhcm5pbmdzXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdW5kZWZpbmVkXG4gICAqL1xuICByZWFkb25seSB0cmFjZT86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIERvIG5vdCBjb25zdHJ1Y3Qgc3RhY2tzIHdpdGggd2FybmluZ3NcbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IHN0cmljdD86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIFBlcmZvcm0gY29udGV4dCBsb29rdXBzIChzeW50aGVzaXMgZmFpbHMgaWYgdGhpcyBpcyBkaXNhYmxlZCBhbmQgY29udGV4dCBsb29rdXBzIG5lZWQgdG8gYmUgcGVyZm9ybWVkKVxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHRydWVcbiAgICovXG4gIHJlYWRvbmx5IGxvb2t1cHM/OiBib29sZWFuO1xuXG4gIC8qKlxuICAgKiBJZ25vcmVzIHN5bnRoZXNpcyBlcnJvcnMsIHdoaWNoIHdpbGwgbGlrZWx5IHByb2R1Y2UgYW4gaW52YWxpZCBvdXRwdXRcbiAgICpcbiAgICogQGRlZmF1bHQgLSBmYWxzZVxuICAgKi9cbiAgcmVhZG9ubHkgaWdub3JlRXJyb3JzPzogYm9vbGVhbjtcblxuICAvKipcbiAgICogVXNlIEpTT04gb3V0cHV0IGluc3RlYWQgb2YgWUFNTCB3aGVuIHRlbXBsYXRlcyBhcmUgcHJpbnRlZCB0byBTVERPVVRcbiAgICpcbiAgICogQGRlZmF1bHQgLSBmYWxzZVxuICAgKi9cbiAgcmVhZG9ubHkganNvbj86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIFNob3cgZGVidWcgbG9ncyAoc3BlY2lmeSBtdWx0aXBsZSB0aW1lcyB0byBpbmNyZWFzZSB2ZXJib3NpdHkpXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gZmFsc2VcbiAgICovXG4gIHJlYWRvbmx5IHZlcmJvc2U/OiBudW1iZXI7XG5cbiAgLyoqXG4gICAqIERlYnVnIHRoZSBDREsgYXBwLiBMb2cgYWRkaXRpb25hbCBpbmZvcm1hdGlvbiBkdXJpbmcgc3ludGhlc2lzLCBzdWNoIGFzIGNyZWF0aW9uIHN0YWNrIHRyYWNlcyBvZiB0b2tlbnMgKHNldHMgQ0RLX0RFQlVHLCB3aWxsIHNsb3cgZG93biBzeW50aGVzaXMpXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gZmFsc2VcbiAgICovXG4gIHJlYWRvbmx5IGRlYnVnPzogYm9vbGVhbjtcblxuICAvKipcbiAgICogVXNlIHRoZSBpbmRpY2F0ZWQgQVdTIHByb2ZpbGUgYXMgdGhlIGRlZmF1bHQgZW52aXJvbm1lbnRcbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IHByb2ZpbGU/OiBzdHJpbmc7XG5cbiAgLyoqXG4gICAqIFVzZSB0aGUgaW5kaWNhdGVkIHByb3h5LiBXaWxsIHJlYWQgZnJvbSBIVFRQU19QUk9YWSBlbnZpcm9ubWVudCB2YXJpYWJsZSBpZiBub3Qgc3BlY2lmaWVkXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdW5kZWZpbmVkXG4gICAqL1xuICByZWFkb25seSBwcm94eT86IHN0cmluZztcblxuICAvKipcbiAgICogUGF0aCB0byBDQSBjZXJ0aWZpY2F0ZSB0byB1c2Ugd2hlbiB2YWxpZGF0aW5nIEhUVFBTIHJlcXVlc3RzLiBXaWxsIHJlYWQgZnJvbSBBV1NfQ0FfQlVORExFIGVudmlyb25tZW50IHZhcmlhYmxlIGlmIG5vdCBzcGVjaWZpZWRcbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IGNhQnVuZGxlUGF0aD86IHN0cmluZztcblxuICAvKipcbiAgICogRm9yY2UgdHJ5aW5nIHRvIGZldGNoIEVDMiBpbnN0YW5jZSBjcmVkZW50aWFscy4gRGVmYXVsdDogZ3Vlc3MgRUMyIGluc3RhbmNlIHN0YXR1c1xuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgZWMyY3JlZHM/OiBib29sZWFuO1xuXG4gIC8qKlxuICAgKiBJbmNsdWRlIHRoZSBcIkFXUzo6Q0RLOjpNZXRhZGF0YVwiIHJlc291cmNlIGluIHN5bnRoZXNpemVkIHRlbXBsYXRlcyAoZW5hYmxlZCBieSBkZWZhdWx0KVxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgdmVyc2lvblJlcG9ydGluZz86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIEluY2x1ZGUgXCJhd3M6Y2RrOnBhdGhcIiBDbG91ZEZvcm1hdGlvbiBtZXRhZGF0YSBmb3IgZWFjaCByZXNvdXJjZSAoZW5hYmxlZCBieSBkZWZhdWx0KVxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgcGF0aE1ldGFkYXRhPzogYm9vbGVhbjtcblxuICAvKipcbiAgICogSW5jbHVkZSBcImF3czphc3NldDoqXCIgQ2xvdWRGb3JtYXRpb24gbWV0YWRhdGEgZm9yIHJlc291cmNlcyB0aGF0IHVzZXMgYXNzZXRzIChlbmFibGVkIGJ5IGRlZmF1bHQpXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdW5kZWZpbmVkXG4gICAqL1xuICByZWFkb25seSBhc3NldE1ldGFkYXRhPzogYm9vbGVhbjtcblxuICAvKipcbiAgICogQVJOIG9mIFJvbGUgdG8gdXNlIHdoZW4gaW52b2tpbmcgQ2xvdWRGb3JtYXRpb25cbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IHJvbGVBcm4/OiBzdHJpbmc7XG5cbiAgLyoqXG4gICAqIENvcHkgYXNzZXRzIHRvIHRoZSBvdXRwdXQgZGlyZWN0b3J5ICh1c2UgLS1uby1zdGFnaW5nIHRvIGRpc2FibGUgdGhlIGNvcHkgb2YgYXNzZXRzIHdoaWNoIGFsbG93cyBsb2NhbCBkZWJ1Z2dpbmcgdmlhIHRoZSBTQU0gQ0xJIHRvIHJlZmVyZW5jZSB0aGUgb3JpZ2luYWwgc291cmNlIGZpbGVzKVxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHRydWVcbiAgICovXG4gIHJlYWRvbmx5IHN0YWdpbmc/OiBib29sZWFuO1xuXG4gIC8qKlxuICAgKiBFbWl0cyB0aGUgc3ludGhlc2l6ZWQgY2xvdWQgYXNzZW1ibHkgaW50byBhIGRpcmVjdG9yeSAoZGVmYXVsdDogY2RrLm91dClcbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IG91dHB1dD86IHN0cmluZztcblxuICAvKipcbiAgICogU2hvdyByZWxldmFudCBub3RpY2VzXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdW5kZWZpbmVkXG4gICAqL1xuICByZWFkb25seSBub3RpY2VzPzogYm9vbGVhbjtcblxuICAvKipcbiAgICogUmVtb3ZlcyBjb2xvcnMgYW5kIG90aGVyIHN0eWxlIGZyb20gY29uc29sZSBvdXRwdXRcbiAgICpcbiAgICogQGRlZmF1bHQgLSBmYWxzZVxuICAgKi9cbiAgcmVhZG9ubHkgbm9Db2xvcj86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIEZvcmNlIENJIGRldGVjdGlvbi4gSWYgQ0k9dHJ1ZSB0aGVuIGxvZ3Mgd2lsbCBiZSBzZW50IHRvIHN0ZG91dCBpbnN0ZWFkIG9mIHN0ZGVyclxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgY2k/OiBib29sZWFuO1xuXG4gIC8qKlxuICAgKiBPcHQgaW4gdG8gdW5zdGFibGUgZmVhdHVyZXMuIFRoZSBmbGFnIGluZGljYXRlcyB0aGF0IHRoZSBzY29wZSBhbmQgQVBJIG9mIGEgZmVhdHVyZSBtaWdodCBzdGlsbCBjaGFuZ2UuIE90aGVyd2lzZSB0aGUgZmVhdHVyZSBpcyBnZW5lcmFsbHkgcHJvZHVjdGlvbiByZWFkeSBhbmQgZnVsbHkgc3VwcG9ydGVkLiBDYW4gYmUgc3BlY2lmaWVkIG11bHRpcGxlIHRpbWVzLlxuICAgKlxuICAgKiBAZGVmYXVsdCAtIFtdXG4gICAqL1xuICByZWFkb25seSB1bnN0YWJsZT86IEFycmF5PHN0cmluZz47XG59XG5cbi8qKlxuICogTGlzdHMgYWxsIHN0YWNrcyBpbiB0aGUgYXBwXG4gKlxuICogYWxpYXNlczogbHNcbiAqXG4gKiBAc3RydWN0XG4gKi9cbmV4cG9ydCBpbnRlcmZhY2UgTGlzdE9wdGlvbnMge1xuICAvKipcbiAgICogRGlzcGxheSBlbnZpcm9ubWVudCBpbmZvcm1hdGlvbiBmb3IgZWFjaCBzdGFja1xuICAgKlxuICAgKiBhbGlhc2VzOiBsXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gZmFsc2VcbiAgICovXG4gIHJlYWRvbmx5IGxvbmc/OiBib29sZWFuO1xuXG4gIC8qKlxuICAgKiBEaXNwbGF5IHN0YWNrIGRlcGVuZGVuY3kgaW5mb3JtYXRpb24gZm9yIGVhY2ggc3RhY2tcbiAgICpcbiAgICogYWxpYXNlczogZFxuICAgKlxuICAgKiBAZGVmYXVsdCAtIGZhbHNlXG4gICAqL1xuICByZWFkb25seSBzaG93RGVwZW5kZW5jaWVzPzogYm9vbGVhbjtcblxuICAvKipcbiAgICogUG9zaXRpb25hbCBhcmd1bWVudCBmb3IgbGlzdFxuICAgKi9cbiAgcmVhZG9ubHkgU1RBQ0tTPzogQXJyYXk8c3RyaW5nPjtcbn1cblxuLyoqXG4gKiBTeW50aGVzaXplcyBhbmQgcHJpbnRzIHRoZSBDbG91ZEZvcm1hdGlvbiB0ZW1wbGF0ZSBmb3IgdGhpcyBzdGFja1xuICpcbiAqIGFsaWFzZXM6IHN5bnRoZXNpemVcbiAqXG4gKiBAc3RydWN0XG4gKi9cbmV4cG9ydCBpbnRlcmZhY2UgU3ludGhPcHRpb25zIHtcbiAgLyoqXG4gICAqIE9ubHkgc3ludGhlc2l6ZSByZXF1ZXN0ZWQgc3RhY2tzLCBkb24ndCBpbmNsdWRlIGRlcGVuZGVuY2llc1xuICAgKlxuICAgKiBhbGlhc2VzOiBlXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdW5kZWZpbmVkXG4gICAqL1xuICByZWFkb25seSBleGNsdXNpdmVseT86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIEFmdGVyIHN5bnRoZXNpcywgdmFsaWRhdGUgc3RhY2tzIHdpdGggdGhlIFwidmFsaWRhdGVPblN5bnRoXCIgYXR0cmlidXRlIHNldCAoY2FuIGFsc28gYmUgY29udHJvbGxlZCB3aXRoIENES19WQUxJREFUSU9OKVxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHRydWVcbiAgICovXG4gIHJlYWRvbmx5IHZhbGlkYXRpb24/OiBib29sZWFuO1xuXG4gIC8qKlxuICAgKiBEbyBub3Qgb3V0cHV0IENsb3VkRm9ybWF0aW9uIFRlbXBsYXRlIHRvIHN0ZG91dFxuICAgKlxuICAgKiBhbGlhc2VzOiBxXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gZmFsc2VcbiAgICovXG4gIHJlYWRvbmx5IHF1aWV0PzogYm9vbGVhbjtcblxuICAvKipcbiAgICogUG9zaXRpb25hbCBhcmd1bWVudCBmb3Igc3ludGhcbiAgICovXG4gIHJlYWRvbmx5IFNUQUNLUz86IEFycmF5PHN0cmluZz47XG59XG5cbi8qKlxuICogRGVwbG95cyB0aGUgQ0RLIHRvb2xraXQgc3RhY2sgaW50byBhbiBBV1MgZW52aXJvbm1lbnRcbiAqXG4gKiBAc3RydWN0XG4gKi9cbmV4cG9ydCBpbnRlcmZhY2UgQm9vdHN0cmFwT3B0aW9ucyB7XG4gIC8qKlxuICAgKiBUaGUgbmFtZSBvZiB0aGUgQ0RLIHRvb2xraXQgYnVja2V0OyBidWNrZXQgd2lsbCBiZSBjcmVhdGVkIGFuZCBtdXN0IG5vdCBleGlzdFxuICAgKlxuICAgKiBhbGlhc2VzOiBiIHRvb2xraXQtYnVja2V0LW5hbWVcbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IGJvb3RzdHJhcEJ1Y2tldE5hbWU/OiBzdHJpbmc7XG5cbiAgLyoqXG4gICAqIEFXUyBLTVMgbWFzdGVyIGtleSBJRCB1c2VkIGZvciB0aGUgU1NFLUtNUyBlbmNyeXB0aW9uIChzcGVjaWZ5IEFXU19NQU5BR0VEX0tFWSB0byB1c2UgYW4gQVdTLW1hbmFnZWQga2V5KVxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgYm9vdHN0cmFwS21zS2V5SWQ/OiBzdHJpbmc7XG5cbiAgLyoqXG4gICAqIFVzZSB0aGUgZXhhbXBsZSBwZXJtaXNzaW9ucyBib3VuZGFyeS5cbiAgICpcbiAgICogYWxpYXNlczogZXBiXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdW5kZWZpbmVkXG4gICAqL1xuICByZWFkb25seSBleGFtcGxlUGVybWlzc2lvbnNCb3VuZGFyeT86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIFVzZSB0aGUgcGVybWlzc2lvbnMgYm91bmRhcnkgc3BlY2lmaWVkIGJ5IG5hbWUuXG4gICAqXG4gICAqIGFsaWFzZXM6IGNwYlxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgY3VzdG9tUGVybWlzc2lvbnNCb3VuZGFyeT86IHN0cmluZztcblxuICAvKipcbiAgICogQ3JlYXRlIGEgQ3VzdG9tZXIgTWFzdGVyIEtleSAoQ01LKSBmb3IgdGhlIGJvb3RzdHJhcCBidWNrZXQgKHlvdSB3aWxsIGJlIGNoYXJnZWQgYnV0IGNhbiBjdXN0b21pemUgcGVybWlzc2lvbnMsIG1vZGVybiBib290c3RyYXBwaW5nIG9ubHkpXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdW5kZWZpbmVkXG4gICAqL1xuICByZWFkb25seSBib290c3RyYXBDdXN0b21lcktleT86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIFN0cmluZyB3aGljaCBtdXN0IGJlIHVuaXF1ZSBmb3IgZWFjaCBib290c3RyYXAgc3RhY2suIFlvdSBtdXN0IGNvbmZpZ3VyZSBpdCBvbiB5b3VyIENESyBhcHAgaWYgeW91IGNoYW5nZSB0aGlzIGZyb20gdGhlIGRlZmF1bHQuXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdW5kZWZpbmVkXG4gICAqL1xuICByZWFkb25seSBxdWFsaWZpZXI/OiBzdHJpbmc7XG5cbiAgLyoqXG4gICAqIEJsb2NrIHB1YmxpYyBhY2Nlc3MgY29uZmlndXJhdGlvbiBvbiBDREsgdG9vbGtpdCBidWNrZXQgKGVuYWJsZWQgYnkgZGVmYXVsdClcbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IHB1YmxpY0FjY2Vzc0Jsb2NrQ29uZmlndXJhdGlvbj86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIFRhZ3MgdG8gYWRkIGZvciB0aGUgc3RhY2sgKEtFWT1WQUxVRSlcbiAgICpcbiAgICogYWxpYXNlczogdFxuICAgKlxuICAgKiBAZGVmYXVsdCAtIFtdXG4gICAqL1xuICByZWFkb25seSB0YWdzPzogQXJyYXk8c3RyaW5nPjtcblxuICAvKipcbiAgICogV2hldGhlciB0byBleGVjdXRlIENoYW5nZVNldCAoLS1uby1leGVjdXRlIHdpbGwgTk9UIGV4ZWN1dGUgdGhlIENoYW5nZVNldClcbiAgICpcbiAgICogQGRlZmF1bHQgLSB0cnVlXG4gICAqL1xuICByZWFkb25seSBleGVjdXRlPzogYm9vbGVhbjtcblxuICAvKipcbiAgICogVGhlIEFXUyBhY2NvdW50IElEcyB0aGF0IHNob3VsZCBiZSB0cnVzdGVkIHRvIHBlcmZvcm0gZGVwbG95bWVudHMgaW50byB0aGlzIGVudmlyb25tZW50IChtYXkgYmUgcmVwZWF0ZWQsIG1vZGVybiBib290c3RyYXBwaW5nIG9ubHkpXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gW11cbiAgICovXG4gIHJlYWRvbmx5IHRydXN0PzogQXJyYXk8c3RyaW5nPjtcblxuICAvKipcbiAgICogVGhlIEFXUyBhY2NvdW50IElEcyB0aGF0IHNob3VsZCBiZSB0cnVzdGVkIHRvIGxvb2sgdXAgdmFsdWVzIGluIHRoaXMgZW52aXJvbm1lbnQgKG1heSBiZSByZXBlYXRlZCwgbW9kZXJuIGJvb3RzdHJhcHBpbmcgb25seSlcbiAgICpcbiAgICogQGRlZmF1bHQgLSBbXVxuICAgKi9cbiAgcmVhZG9ubHkgdHJ1c3RGb3JMb29rdXA/OiBBcnJheTxzdHJpbmc+O1xuXG4gIC8qKlxuICAgKiBUaGUgQVdTIGFjY291bnQgSURzIHRoYXQgc2hvdWxkIG5vdCBiZSB0cnVzdGVkIGJ5IHRoaXMgZW52aXJvbm1lbnQgKG1heSBiZSByZXBlYXRlZCwgbW9kZXJuIGJvb3RzdHJhcHBpbmcgb25seSlcbiAgICpcbiAgICogQGRlZmF1bHQgLSBbXVxuICAgKi9cbiAgcmVhZG9ubHkgdW50cnVzdD86IEFycmF5PHN0cmluZz47XG5cbiAgLyoqXG4gICAqIFRoZSBNYW5hZ2VkIFBvbGljeSBBUk5zIHRoYXQgc2hvdWxkIGJlIGF0dGFjaGVkIHRvIHRoZSByb2xlIHBlcmZvcm1pbmcgZGVwbG95bWVudHMgaW50byB0aGlzIGVudmlyb25tZW50IChtYXkgYmUgcmVwZWF0ZWQsIG1vZGVybiBib290c3RyYXBwaW5nIG9ubHkpXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gW11cbiAgICovXG4gIHJlYWRvbmx5IGNsb3VkZm9ybWF0aW9uRXhlY3V0aW9uUG9saWNpZXM/OiBBcnJheTxzdHJpbmc+O1xuXG4gIC8qKlxuICAgKiBBbHdheXMgYm9vdHN0cmFwIGV2ZW4gaWYgaXQgd291bGQgZG93bmdyYWRlIHRlbXBsYXRlIHZlcnNpb25cbiAgICpcbiAgICogYWxpYXNlczogZlxuICAgKlxuICAgKiBAZGVmYXVsdCAtIGZhbHNlXG4gICAqL1xuICByZWFkb25seSBmb3JjZT86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIFRvZ2dsZSBDbG91ZEZvcm1hdGlvbiB0ZXJtaW5hdGlvbiBwcm90ZWN0aW9uIG9uIHRoZSBib290c3RyYXAgc3RhY2tzXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdW5kZWZpbmVkXG4gICAqL1xuICByZWFkb25seSB0ZXJtaW5hdGlvblByb3RlY3Rpb24/OiBib29sZWFuO1xuXG4gIC8qKlxuICAgKiBJbnN0ZWFkIG9mIGFjdHVhbCBib290c3RyYXBwaW5nLCBwcmludCB0aGUgY3VycmVudCBDTEkncyBib290c3RyYXBwaW5nIHRlbXBsYXRlIHRvIHN0ZG91dCBmb3IgY3VzdG9taXphdGlvblxuICAgKlxuICAgKiBAZGVmYXVsdCAtIGZhbHNlXG4gICAqL1xuICByZWFkb25seSBzaG93VGVtcGxhdGU/OiBib29sZWFuO1xuXG4gIC8qKlxuICAgKiBUaGUgbmFtZSBvZiB0aGUgQ0RLIHRvb2xraXQgc3RhY2sgdG8gY3JlYXRlXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdW5kZWZpbmVkXG4gICAqL1xuICByZWFkb25seSB0b29sa2l0U3RhY2tOYW1lPzogc3RyaW5nO1xuXG4gIC8qKlxuICAgKiBVc2UgdGhlIHRlbXBsYXRlIGZyb20gdGhlIGdpdmVuIGZpbGUgaW5zdGVhZCBvZiB0aGUgYnVpbHQtaW4gb25lICh1c2UgLS1zaG93LXRlbXBsYXRlIHRvIG9idGFpbiBhbiBleGFtcGxlKVxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgdGVtcGxhdGU/OiBzdHJpbmc7XG5cbiAgLyoqXG4gICAqIFVzZSBwcmV2aW91cyB2YWx1ZXMgZm9yIGV4aXN0aW5nIHBhcmFtZXRlcnMgKHlvdSBtdXN0IHNwZWNpZnkgYWxsIHBhcmFtZXRlcnMgb24gZXZlcnkgZGVwbG95bWVudCBpZiB0aGlzIGlzIGRpc2FibGVkKVxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHRydWVcbiAgICovXG4gIHJlYWRvbmx5IHByZXZpb3VzUGFyYW1ldGVycz86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIFBvc2l0aW9uYWwgYXJndW1lbnQgZm9yIGJvb3RzdHJhcFxuICAgKi9cbiAgcmVhZG9ubHkgRU5WSVJPTk1FTlRTPzogQXJyYXk8c3RyaW5nPjtcbn1cblxuLyoqXG4gKiBHYXJiYWdlIGNvbGxlY3QgYXNzZXRzLiBPcHRpb25zIGRldGFpbGVkIGhlcmU6IGh0dHBzOi8vZ2l0aHViLmNvbS9hd3MvYXdzLWNkay1jbGkvdHJlZS9tYWluL3BhY2thZ2VzL2F3cy1jZGsjY2RrLWdjXG4gKlxuICogQHN0cnVjdFxuICovXG5leHBvcnQgaW50ZXJmYWNlIEdjT3B0aW9ucyB7XG4gIC8qKlxuICAgKiBUaGUgYWN0aW9uIChvciBzdWItYWN0aW9uKSB5b3Ugd2FudCB0byBwZXJmb3JtLiBWYWxpZCBlbnRpcmVzIGFyZSBcInByaW50XCIsIFwidGFnXCIsIFwiZGVsZXRlLXRhZ2dlZFwiLCBcImZ1bGxcIi5cbiAgICpcbiAgICogQGRlZmF1bHQgLSBcImZ1bGxcIlxuICAgKi9cbiAgcmVhZG9ubHkgYWN0aW9uPzogc3RyaW5nO1xuXG4gIC8qKlxuICAgKiBTcGVjaWZ5IGVpdGhlciBlY3IsIHMzLCBvciBhbGxcbiAgICpcbiAgICogQGRlZmF1bHQgLSBcImFsbFwiXG4gICAqL1xuICByZWFkb25seSB0eXBlPzogc3RyaW5nO1xuXG4gIC8qKlxuICAgKiBEZWxldGUgYXNzZXRzIHRoYXQgaGF2ZSBiZWVuIG1hcmtlZCBhcyBpc29sYXRlZCBmb3IgdGhpcyBtYW55IGRheXNcbiAgICpcbiAgICogQGRlZmF1bHQgLSAwXG4gICAqL1xuICByZWFkb25seSByb2xsYmFja0J1ZmZlckRheXM/OiBudW1iZXI7XG5cbiAgLyoqXG4gICAqIE5ldmVyIGRlbGV0ZSBhc3NldHMgeW91bmdlciB0aGFuIHRoaXMgKGluIGRheXMpXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gMVxuICAgKi9cbiAgcmVhZG9ubHkgY3JlYXRlZEJ1ZmZlckRheXM/OiBudW1iZXI7XG5cbiAgLyoqXG4gICAqIENvbmZpcm0gdmlhIG1hbnVhbCBwcm9tcHQgYmVmb3JlIGRlbGV0aW9uXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdHJ1ZVxuICAgKi9cbiAgcmVhZG9ubHkgY29uZmlybT86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIFRoZSBuYW1lIG9mIHRoZSBDREsgdG9vbGtpdCBzdGFjaywgaWYgZGlmZmVyZW50IGZyb20gdGhlIGRlZmF1bHQgXCJDREtUb29sa2l0XCJcbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IGJvb3RzdHJhcFN0YWNrTmFtZT86IHN0cmluZztcblxuICAvKipcbiAgICogUG9zaXRpb25hbCBhcmd1bWVudCBmb3IgZ2NcbiAgICovXG4gIHJlYWRvbmx5IEVOVklST05NRU5UUz86IEFycmF5PHN0cmluZz47XG59XG5cbi8qKlxuICogRGVwbG95cyB0aGUgc3RhY2socykgbmFtZWQgU1RBQ0tTIGludG8geW91ciBBV1MgYWNjb3VudFxuICpcbiAqIEBzdHJ1Y3RcbiAqL1xuZXhwb3J0IGludGVyZmFjZSBEZXBsb3lPcHRpb25zIHtcbiAgLyoqXG4gICAqIERlcGxveSBhbGwgYXZhaWxhYmxlIHN0YWNrc1xuICAgKlxuICAgKiBAZGVmYXVsdCAtIGZhbHNlXG4gICAqL1xuICByZWFkb25seSBhbGw/OiBib29sZWFuO1xuXG4gIC8qKlxuICAgKiBEbyBub3QgcmVidWlsZCBhc3NldCB3aXRoIHRoZSBnaXZlbiBJRC4gQ2FuIGJlIHNwZWNpZmllZCBtdWx0aXBsZSB0aW1lc1xuICAgKlxuICAgKiBhbGlhc2VzOiBFXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gW11cbiAgICovXG4gIHJlYWRvbmx5IGJ1aWxkRXhjbHVkZT86IEFycmF5PHN0cmluZz47XG5cbiAgLyoqXG4gICAqIE9ubHkgZGVwbG95IHJlcXVlc3RlZCBzdGFja3MsIGRvbid0IGluY2x1ZGUgZGVwZW5kZW5jaWVzXG4gICAqXG4gICAqIGFsaWFzZXM6IGVcbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IGV4Y2x1c2l2ZWx5PzogYm9vbGVhbjtcblxuICAvKipcbiAgICogV2hhdCBzZWN1cml0eS1zZW5zaXRpdmUgY2hhbmdlcyBuZWVkIG1hbnVhbCBhcHByb3ZhbFxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgcmVxdWlyZUFwcHJvdmFsPzogc3RyaW5nO1xuXG4gIC8qKlxuICAgKiBBUk5zIG9mIFNOUyB0b3BpY3MgdGhhdCBDbG91ZEZvcm1hdGlvbiB3aWxsIG5vdGlmeSB3aXRoIHN0YWNrIHJlbGF0ZWQgZXZlbnRzLiBUaGVzZSB3aWxsIGJlIGFkZGVkIHRvIEFSTnMgc3BlY2lmaWVkIHdpdGggdGhlICdub3RpZmljYXRpb25Bcm5zJyBzdGFjayBwcm9wZXJ0eS5cbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IG5vdGlmaWNhdGlvbkFybnM/OiBBcnJheTxzdHJpbmc+O1xuXG4gIC8qKlxuICAgKiBUYWdzIHRvIGFkZCB0byB0aGUgc3RhY2sgKEtFWT1WQUxVRSksIG92ZXJyaWRlcyB0YWdzIGZyb20gQ2xvdWQgQXNzZW1ibHkgKGRlcHJlY2F0ZWQpXG4gICAqXG4gICAqIGFsaWFzZXM6IHRcbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IHRhZ3M/OiBBcnJheTxzdHJpbmc+O1xuXG4gIC8qKlxuICAgKiBXaGV0aGVyIHRvIGV4ZWN1dGUgQ2hhbmdlU2V0ICgtLW5vLWV4ZWN1dGUgd2lsbCBOT1QgZXhlY3V0ZSB0aGUgQ2hhbmdlU2V0KSAoZGVwcmVjYXRlZClcbiAgICpcbiAgICogQGRlcHJlY2F0ZWQgdHJ1ZVxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgZXhlY3V0ZT86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIE5hbWUgb2YgdGhlIENsb3VkRm9ybWF0aW9uIGNoYW5nZSBzZXQgdG8gY3JlYXRlIChvbmx5IGlmIG1ldGhvZCBpcyBub3QgZGlyZWN0KVxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgY2hhbmdlU2V0TmFtZT86IHN0cmluZztcblxuICAvKipcbiAgICogSG93IHRvIHBlcmZvcm0gdGhlIGRlcGxveW1lbnQuIERpcmVjdCBpcyBhIGJpdCBmYXN0ZXIgYnV0IGxhY2tzIHByb2dyZXNzIGluZm9ybWF0aW9uXG4gICAqXG4gICAqIGFsaWFzZXM6IG1cbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IG1ldGhvZD86IHN0cmluZztcblxuICAvKipcbiAgICogSW5kaWNhdGVzIGlmIHRoZSBzdGFjayBzZXQgaW1wb3J0cyByZXNvdXJjZXMgdGhhdCBhbHJlYWR5IGV4aXN0LlxuICAgKlxuICAgKiBAZGVmYXVsdCAtIGZhbHNlXG4gICAqL1xuICByZWFkb25seSBpbXBvcnRFeGlzdGluZ1Jlc291cmNlcz86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIEFsd2F5cyBkZXBsb3kgc3RhY2sgZXZlbiBpZiB0ZW1wbGF0ZXMgYXJlIGlkZW50aWNhbFxuICAgKlxuICAgKiBhbGlhc2VzOiBmXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gZmFsc2VcbiAgICovXG4gIHJlYWRvbmx5IGZvcmNlPzogYm9vbGVhbjtcblxuICAvKipcbiAgICogQWRkaXRpb25hbCBwYXJhbWV0ZXJzIHBhc3NlZCB0byBDbG91ZEZvcm1hdGlvbiBhdCBkZXBsb3kgdGltZSAoU1RBQ0s6S0VZPVZBTFVFKVxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHt9XG4gICAqL1xuICByZWFkb25seSBwYXJhbWV0ZXJzPzogQXJyYXk8c3RyaW5nPjtcblxuICAvKipcbiAgICogUGF0aCB0byBmaWxlIHdoZXJlIHN0YWNrIG91dHB1dHMgd2lsbCBiZSB3cml0dGVuIGFzIEpTT05cbiAgICpcbiAgICogYWxpYXNlczogT1xuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgb3V0cHV0c0ZpbGU/OiBzdHJpbmc7XG5cbiAgLyoqXG4gICAqIFVzZSBwcmV2aW91cyB2YWx1ZXMgZm9yIGV4aXN0aW5nIHBhcmFtZXRlcnMgKHlvdSBtdXN0IHNwZWNpZnkgYWxsIHBhcmFtZXRlcnMgb24gZXZlcnkgZGVwbG95bWVudCBpZiB0aGlzIGlzIGRpc2FibGVkKVxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHRydWVcbiAgICovXG4gIHJlYWRvbmx5IHByZXZpb3VzUGFyYW1ldGVycz86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIFRoZSBuYW1lIG9mIHRoZSBleGlzdGluZyBDREsgdG9vbGtpdCBzdGFjayAob25seSB1c2VkIGZvciBhcHAgdXNpbmcgbGVnYWN5IHN5bnRoZXNpcylcbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IHRvb2xraXRTdGFja05hbWU/OiBzdHJpbmc7XG5cbiAgLyoqXG4gICAqIERpc3BsYXkgbW9kZSBmb3Igc3RhY2sgYWN0aXZpdHkgZXZlbnRzXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdW5kZWZpbmVkXG4gICAqL1xuICByZWFkb25seSBwcm9ncmVzcz86IHN0cmluZztcblxuICAvKipcbiAgICogUm9sbGJhY2sgc3RhY2sgdG8gc3RhYmxlIHN0YXRlIG9uIGZhaWx1cmUuIERlZmF1bHRzIHRvICd0cnVlJywgaXRlcmF0ZSBtb3JlIHJhcGlkbHkgd2l0aCAtLW5vLXJvbGxiYWNrIG9yIC1SLiBOb3RlOiBkbyAqKm5vdCoqIGRpc2FibGUgdGhpcyBmbGFnIGZvciBkZXBsb3ltZW50cyB3aXRoIHJlc291cmNlIHJlcGxhY2VtZW50cywgYXMgdGhhdCB3aWxsIGFsd2F5cyBmYWlsXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdW5kZWZpbmVkXG4gICAqL1xuICByZWFkb25seSByb2xsYmFjaz86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIEF0dGVtcHRzIHRvIHBlcmZvcm0gYSAnaG90c3dhcCcgZGVwbG95bWVudCwgYnV0IGRvZXMgbm90IGZhbGwgYmFjayB0byBhIGZ1bGwgZGVwbG95bWVudCBpZiB0aGF0IGlzIG5vdCBwb3NzaWJsZS4gSW5zdGVhZCwgY2hhbmdlcyB0byBhbnkgbm9uLWhvdHN3YXBwYWJsZSBwcm9wZXJ0aWVzIGFyZSBpZ25vcmVkLkRvIG5vdCB1c2UgdGhpcyBpbiBwcm9kdWN0aW9uIGVudmlyb25tZW50c1xuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgaG90c3dhcD86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIEF0dGVtcHRzIHRvIHBlcmZvcm0gYSAnaG90c3dhcCcgZGVwbG95bWVudCwgd2hpY2ggc2tpcHMgQ2xvdWRGb3JtYXRpb24gYW5kIHVwZGF0ZXMgdGhlIHJlc291cmNlcyBkaXJlY3RseSwgYW5kIGZhbGxzIGJhY2sgdG8gYSBmdWxsIGRlcGxveW1lbnQgaWYgdGhhdCBpcyBub3QgcG9zc2libGUuIERvIG5vdCB1c2UgdGhpcyBpbiBwcm9kdWN0aW9uIGVudmlyb25tZW50c1xuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgaG90c3dhcEZhbGxiYWNrPzogYm9vbGVhbjtcblxuICAvKipcbiAgICogTG93ZXIgbGltaXQgb24gdGhlIG51bWJlciBvZiB5b3VyIHNlcnZpY2UncyB0YXNrcyB0aGF0IG11c3QgcmVtYWluIGluIHRoZSBSVU5OSU5HIHN0YXRlIGR1cmluZyBhIGRlcGxveW1lbnQsIGFzIGEgcGVyY2VudGFnZSBvZiB0aGUgZGVzaXJlZENvdW50XG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdW5kZWZpbmVkXG4gICAqL1xuICByZWFkb25seSBob3Rzd2FwRWNzTWluaW11bUhlYWx0aHlQZXJjZW50Pzogc3RyaW5nO1xuXG4gIC8qKlxuICAgKiBVcHBlciBsaW1pdCBvbiB0aGUgbnVtYmVyIG9mIHlvdXIgc2VydmljZSdzIHRhc2tzIHRoYXQgYXJlIGFsbG93ZWQgaW4gdGhlIFJVTk5JTkcgb3IgUEVORElORyBzdGF0ZSBkdXJpbmcgYSBkZXBsb3ltZW50LCBhcyBhIHBlcmNlbnRhZ2Ugb2YgdGhlIGRlc2lyZWRDb3VudFxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgaG90c3dhcEVjc01heGltdW1IZWFsdGh5UGVyY2VudD86IHN0cmluZztcblxuICAvKipcbiAgICogTnVtYmVyIG9mIHNlY29uZHMgdG8gd2FpdCBmb3IgYSBzaW5nbGUgc2VydmljZSB0byByZWFjaCBzdGFibGUgc3RhdGUsIHdoZXJlIHRoZSBkZXNpcmVkQ291bnQgaXMgZXF1YWwgdG8gdGhlIHJ1bm5pbmdDb3VudFxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgaG90c3dhcEVjc1N0YWJpbGl6YXRpb25UaW1lb3V0U2Vjb25kcz86IHN0cmluZztcblxuICAvKipcbiAgICogQ29udGludW91c2x5IG9ic2VydmUgdGhlIHByb2plY3QgZmlsZXMsIGFuZCBkZXBsb3kgdGhlIGdpdmVuIHN0YWNrKHMpIGF1dG9tYXRpY2FsbHkgd2hlbiBjaGFuZ2VzIGFyZSBkZXRlY3RlZC4gSW1wbGllcyAtLWhvdHN3YXAgYnkgZGVmYXVsdFxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgd2F0Y2g/OiBib29sZWFuO1xuXG4gIC8qKlxuICAgKiBTaG93IENsb3VkV2F0Y2ggbG9nIGV2ZW50cyBmcm9tIGFsbCByZXNvdXJjZXMgaW4gdGhlIHNlbGVjdGVkIFN0YWNrcyBpbiB0aGUgdGVybWluYWwuICd0cnVlJyBieSBkZWZhdWx0LCB1c2UgLS1uby1sb2dzIHRvIHR1cm4gb2ZmLiBPbmx5IGluIGVmZmVjdCBpZiBzcGVjaWZpZWQgYWxvbmdzaWRlIHRoZSAnLS13YXRjaCcgb3B0aW9uXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdHJ1ZVxuICAgKi9cbiAgcmVhZG9ubHkgbG9ncz86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIE1heGltdW0gbnVtYmVyIG9mIHNpbXVsdGFuZW91cyBkZXBsb3ltZW50cyAoZGVwZW5kZW5jeSBwZXJtaXR0aW5nKSB0byBleGVjdXRlLlxuICAgKlxuICAgKiBAZGVmYXVsdCAtIDFcbiAgICovXG4gIHJlYWRvbmx5IGNvbmN1cnJlbmN5PzogbnVtYmVyO1xuXG4gIC8qKlxuICAgKiBXaGV0aGVyIHRvIGJ1aWxkL3B1Ymxpc2ggYXNzZXRzIGluIHBhcmFsbGVsXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdW5kZWZpbmVkXG4gICAqL1xuICByZWFkb25seSBhc3NldFBhcmFsbGVsaXNtPzogYm9vbGVhbjtcblxuICAvKipcbiAgICogV2hldGhlciB0byBidWlsZCBhbGwgYXNzZXRzIGJlZm9yZSBkZXBsb3lpbmcgdGhlIGZpcnN0IHN0YWNrICh1c2VmdWwgZm9yIGZhaWxpbmcgRG9ja2VyIGJ1aWxkcylcbiAgICpcbiAgICogQGRlZmF1bHQgLSB0cnVlXG4gICAqL1xuICByZWFkb25seSBhc3NldFByZWJ1aWxkPzogYm9vbGVhbjtcblxuICAvKipcbiAgICogV2hldGhlciB0byBkZXBsb3kgaWYgdGhlIGFwcCBjb250YWlucyBubyBzdGFja3NcbiAgICpcbiAgICogQGRlZmF1bHQgLSBmYWxzZVxuICAgKi9cbiAgcmVhZG9ubHkgaWdub3JlTm9TdGFja3M/OiBib29sZWFuO1xuXG4gIC8qKlxuICAgKiBQb3NpdGlvbmFsIGFyZ3VtZW50IGZvciBkZXBsb3lcbiAgICovXG4gIHJlYWRvbmx5IFNUQUNLUz86IEFycmF5PHN0cmluZz47XG59XG5cbi8qKlxuICogUm9sbHMgYmFjayB0aGUgc3RhY2socykgbmFtZWQgU1RBQ0tTIHRvIHRoZWlyIGxhc3Qgc3RhYmxlIHN0YXRlXG4gKlxuICogQHN0cnVjdFxuICovXG5leHBvcnQgaW50ZXJmYWNlIFJvbGxiYWNrT3B0aW9ucyB7XG4gIC8qKlxuICAgKiBSb2xsIGJhY2sgYWxsIGF2YWlsYWJsZSBzdGFja3NcbiAgICpcbiAgICogQGRlZmF1bHQgLSBmYWxzZVxuICAgKi9cbiAgcmVhZG9ubHkgYWxsPzogYm9vbGVhbjtcblxuICAvKipcbiAgICogVGhlIG5hbWUgb2YgdGhlIENESyB0b29sa2l0IHN0YWNrIHRoZSBlbnZpcm9ubWVudCBpcyBib290c3RyYXBwZWQgd2l0aFxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgdG9vbGtpdFN0YWNrTmFtZT86IHN0cmluZztcblxuICAvKipcbiAgICogT3JwaGFuIGFsbCByZXNvdXJjZXMgZm9yIHdoaWNoIHRoZSByb2xsYmFjayBvcGVyYXRpb24gZmFpbHMuXG4gICAqXG4gICAqIGFsaWFzZXM6IGZcbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IGZvcmNlPzogYm9vbGVhbjtcblxuICAvKipcbiAgICogV2hldGhlciB0byB2YWxpZGF0ZSB0aGUgYm9vdHN0cmFwIHN0YWNrIHZlcnNpb24uIERlZmF1bHRzIHRvICd0cnVlJywgZGlzYWJsZSB3aXRoIC0tbm8tdmFsaWRhdGUtYm9vdHN0cmFwLXZlcnNpb24uXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdW5kZWZpbmVkXG4gICAqL1xuICByZWFkb25seSB2YWxpZGF0ZUJvb3RzdHJhcFZlcnNpb24/OiBib29sZWFuO1xuXG4gIC8qKlxuICAgKiBPcnBoYW4gdGhlIGdpdmVuIHJlc291cmNlcywgaWRlbnRpZmllZCBieSB0aGVpciBsb2dpY2FsIElEIChjYW4gYmUgc3BlY2lmaWVkIG11bHRpcGxlIHRpbWVzKVxuICAgKlxuICAgKiBAZGVmYXVsdCAtIFtdXG4gICAqL1xuICByZWFkb25seSBvcnBoYW4/OiBBcnJheTxzdHJpbmc+O1xuXG4gIC8qKlxuICAgKiBQb3NpdGlvbmFsIGFyZ3VtZW50IGZvciByb2xsYmFja1xuICAgKi9cbiAgcmVhZG9ubHkgU1RBQ0tTPzogQXJyYXk8c3RyaW5nPjtcbn1cblxuLyoqXG4gKiBJbXBvcnQgZXhpc3RpbmcgcmVzb3VyY2UocykgaW50byB0aGUgZ2l2ZW4gU1RBQ0tcbiAqXG4gKiBAc3RydWN0XG4gKi9cbmV4cG9ydCBpbnRlcmZhY2UgSW1wb3J0T3B0aW9ucyB7XG4gIC8qKlxuICAgKiBXaGV0aGVyIHRvIGV4ZWN1dGUgQ2hhbmdlU2V0ICgtLW5vLWV4ZWN1dGUgd2lsbCBOT1QgZXhlY3V0ZSB0aGUgQ2hhbmdlU2V0KVxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHRydWVcbiAgICovXG4gIHJlYWRvbmx5IGV4ZWN1dGU/OiBib29sZWFuO1xuXG4gIC8qKlxuICAgKiBOYW1lIG9mIHRoZSBDbG91ZEZvcm1hdGlvbiBjaGFuZ2Ugc2V0IHRvIGNyZWF0ZVxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgY2hhbmdlU2V0TmFtZT86IHN0cmluZztcblxuICAvKipcbiAgICogVGhlIG5hbWUgb2YgdGhlIENESyB0b29sa2l0IHN0YWNrIHRvIGNyZWF0ZVxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgdG9vbGtpdFN0YWNrTmFtZT86IHN0cmluZztcblxuICAvKipcbiAgICogUm9sbGJhY2sgc3RhY2sgdG8gc3RhYmxlIHN0YXRlIG9uIGZhaWx1cmUuIERlZmF1bHRzIHRvICd0cnVlJywgaXRlcmF0ZSBtb3JlIHJhcGlkbHkgd2l0aCAtLW5vLXJvbGxiYWNrIG9yIC1SLiBOb3RlOiBkbyAqKm5vdCoqIGRpc2FibGUgdGhpcyBmbGFnIGZvciBkZXBsb3ltZW50cyB3aXRoIHJlc291cmNlIHJlcGxhY2VtZW50cywgYXMgdGhhdCB3aWxsIGFsd2F5cyBmYWlsXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdW5kZWZpbmVkXG4gICAqL1xuICByZWFkb25seSByb2xsYmFjaz86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIERvIG5vdCBhYm9ydCBpZiB0aGUgdGVtcGxhdGUgZGlmZiBpbmNsdWRlcyB1cGRhdGVzIG9yIGRlbGV0ZXMuIFRoaXMgaXMgcHJvYmFibHkgc2FmZSBidXQgd2UncmUgbm90IHN1cmUsIGxldCB1cyBrbm93IGhvdyBpdCBnb2VzLlxuICAgKlxuICAgKiBhbGlhc2VzOiBmXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdW5kZWZpbmVkXG4gICAqL1xuICByZWFkb25seSBmb3JjZT86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIElmIHNwZWNpZmllZCwgQ0RLIHdpbGwgZ2VuZXJhdGUgYSBtYXBwaW5nIG9mIGV4aXN0aW5nIHBoeXNpY2FsIHJlc291cmNlcyB0byBDREsgcmVzb3VyY2VzIHRvIGJlIGltcG9ydGVkIGFzLiBUaGUgbWFwcGluZyB3aWxsIGJlIHdyaXR0ZW4gaW4gdGhlIGdpdmVuIGZpbGUgcGF0aC4gTm8gYWN0dWFsIGltcG9ydCBvcGVyYXRpb24gd2lsbCBiZSBwZXJmb3JtZWRcbiAgICpcbiAgICogYWxpYXNlczogclxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgcmVjb3JkUmVzb3VyY2VNYXBwaW5nPzogc3RyaW5nO1xuXG4gIC8qKlxuICAgKiBJZiBzcGVjaWZpZWQsIENESyB3aWxsIHVzZSB0aGUgZ2l2ZW4gZmlsZSB0byBtYXAgcGh5c2ljYWwgcmVzb3VyY2VzIHRvIENESyByZXNvdXJjZXMgZm9yIGltcG9ydCwgaW5zdGVhZCBvZiBpbnRlcmFjdGl2ZWx5IGFza2luZyB0aGUgdXNlci4gQ2FuIGJlIHJ1biBmcm9tIHNjcmlwdHNcbiAgICpcbiAgICogYWxpYXNlczogbVxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgcmVzb3VyY2VNYXBwaW5nPzogc3RyaW5nO1xuXG4gIC8qKlxuICAgKiBQb3NpdGlvbmFsIGFyZ3VtZW50IGZvciBpbXBvcnRcbiAgICovXG4gIHJlYWRvbmx5IFNUQUNLPzogc3RyaW5nO1xufVxuXG4vKipcbiAqIFNob3J0Y3V0IGZvciAnZGVwbG95IC0td2F0Y2gnXG4gKlxuICogQHN0cnVjdFxuICovXG5leHBvcnQgaW50ZXJmYWNlIFdhdGNoT3B0aW9ucyB7XG4gIC8qKlxuICAgKiBEbyBub3QgcmVidWlsZCBhc3NldCB3aXRoIHRoZSBnaXZlbiBJRC4gQ2FuIGJlIHNwZWNpZmllZCBtdWx0aXBsZSB0aW1lc1xuICAgKlxuICAgKiBhbGlhc2VzOiBFXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gW11cbiAgICovXG4gIHJlYWRvbmx5IGJ1aWxkRXhjbHVkZT86IEFycmF5PHN0cmluZz47XG5cbiAgLyoqXG4gICAqIE9ubHkgZGVwbG95IHJlcXVlc3RlZCBzdGFja3MsIGRvbid0IGluY2x1ZGUgZGVwZW5kZW5jaWVzXG4gICAqXG4gICAqIGFsaWFzZXM6IGVcbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IGV4Y2x1c2l2ZWx5PzogYm9vbGVhbjtcblxuICAvKipcbiAgICogTmFtZSBvZiB0aGUgQ2xvdWRGb3JtYXRpb24gY2hhbmdlIHNldCB0byBjcmVhdGVcbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IGNoYW5nZVNldE5hbWU/OiBzdHJpbmc7XG5cbiAgLyoqXG4gICAqIEFsd2F5cyBkZXBsb3kgc3RhY2sgZXZlbiBpZiB0ZW1wbGF0ZXMgYXJlIGlkZW50aWNhbFxuICAgKlxuICAgKiBhbGlhc2VzOiBmXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gZmFsc2VcbiAgICovXG4gIHJlYWRvbmx5IGZvcmNlPzogYm9vbGVhbjtcblxuICAvKipcbiAgICogVGhlIG5hbWUgb2YgdGhlIGV4aXN0aW5nIENESyB0b29sa2l0IHN0YWNrIChvbmx5IHVzZWQgZm9yIGFwcCB1c2luZyBsZWdhY3kgc3ludGhlc2lzKVxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgdG9vbGtpdFN0YWNrTmFtZT86IHN0cmluZztcblxuICAvKipcbiAgICogRGlzcGxheSBtb2RlIGZvciBzdGFjayBhY3Rpdml0eSBldmVudHNcbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IHByb2dyZXNzPzogc3RyaW5nO1xuXG4gIC8qKlxuICAgKiBSb2xsYmFjayBzdGFjayB0byBzdGFibGUgc3RhdGUgb24gZmFpbHVyZS4gRGVmYXVsdHMgdG8gJ3RydWUnLCBpdGVyYXRlIG1vcmUgcmFwaWRseSB3aXRoIC0tbm8tcm9sbGJhY2sgb3IgLVIuIE5vdGU6IGRvICoqbm90KiogZGlzYWJsZSB0aGlzIGZsYWcgZm9yIGRlcGxveW1lbnRzIHdpdGggcmVzb3VyY2UgcmVwbGFjZW1lbnRzLCBhcyB0aGF0IHdpbGwgYWx3YXlzIGZhaWxcbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IHJvbGxiYWNrPzogYm9vbGVhbjtcblxuICAvKipcbiAgICogQXR0ZW1wdHMgdG8gcGVyZm9ybSBhICdob3Rzd2FwJyBkZXBsb3ltZW50LCBidXQgZG9lcyBub3QgZmFsbCBiYWNrIHRvIGEgZnVsbCBkZXBsb3ltZW50IGlmIHRoYXQgaXMgbm90IHBvc3NpYmxlLiBJbnN0ZWFkLCBjaGFuZ2VzIHRvIGFueSBub24taG90c3dhcHBhYmxlIHByb3BlcnRpZXMgYXJlIGlnbm9yZWQuJ3RydWUnIGJ5IGRlZmF1bHQsIHVzZSAtLW5vLWhvdHN3YXAgdG8gdHVybiBvZmZcbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IGhvdHN3YXA/OiBib29sZWFuO1xuXG4gIC8qKlxuICAgKiBBdHRlbXB0cyB0byBwZXJmb3JtIGEgJ2hvdHN3YXAnIGRlcGxveW1lbnQsIHdoaWNoIHNraXBzIENsb3VkRm9ybWF0aW9uIGFuZCB1cGRhdGVzIHRoZSByZXNvdXJjZXMgZGlyZWN0bHksIGFuZCBmYWxscyBiYWNrIHRvIGEgZnVsbCBkZXBsb3ltZW50IGlmIHRoYXQgaXMgbm90IHBvc3NpYmxlLlxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgaG90c3dhcEZhbGxiYWNrPzogYm9vbGVhbjtcblxuICAvKipcbiAgICogTG93ZXIgbGltaXQgb24gdGhlIG51bWJlciBvZiB5b3VyIHNlcnZpY2UncyB0YXNrcyB0aGF0IG11c3QgcmVtYWluIGluIHRoZSBSVU5OSU5HIHN0YXRlIGR1cmluZyBhIGRlcGxveW1lbnQsIGFzIGEgcGVyY2VudGFnZSBvZiB0aGUgZGVzaXJlZENvdW50XG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdW5kZWZpbmVkXG4gICAqL1xuICByZWFkb25seSBob3Rzd2FwRWNzTWluaW11bUhlYWx0aHlQZXJjZW50Pzogc3RyaW5nO1xuXG4gIC8qKlxuICAgKiBVcHBlciBsaW1pdCBvbiB0aGUgbnVtYmVyIG9mIHlvdXIgc2VydmljZSdzIHRhc2tzIHRoYXQgYXJlIGFsbG93ZWQgaW4gdGhlIFJVTk5JTkcgb3IgUEVORElORyBzdGF0ZSBkdXJpbmcgYSBkZXBsb3ltZW50LCBhcyBhIHBlcmNlbnRhZ2Ugb2YgdGhlIGRlc2lyZWRDb3VudFxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgaG90c3dhcEVjc01heGltdW1IZWFsdGh5UGVyY2VudD86IHN0cmluZztcblxuICAvKipcbiAgICogTnVtYmVyIG9mIHNlY29uZHMgdG8gd2FpdCBmb3IgYSBzaW5nbGUgc2VydmljZSB0byByZWFjaCBzdGFibGUgc3RhdGUsIHdoZXJlIHRoZSBkZXNpcmVkQ291bnQgaXMgZXF1YWwgdG8gdGhlIHJ1bm5pbmdDb3VudFxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgaG90c3dhcEVjc1N0YWJpbGl6YXRpb25UaW1lb3V0U2Vjb25kcz86IHN0cmluZztcblxuICAvKipcbiAgICogU2hvdyBDbG91ZFdhdGNoIGxvZyBldmVudHMgZnJvbSBhbGwgcmVzb3VyY2VzIGluIHRoZSBzZWxlY3RlZCBTdGFja3MgaW4gdGhlIHRlcm1pbmFsLiAndHJ1ZScgYnkgZGVmYXVsdCwgdXNlIC0tbm8tbG9ncyB0byB0dXJuIG9mZlxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHRydWVcbiAgICovXG4gIHJlYWRvbmx5IGxvZ3M/OiBib29sZWFuO1xuXG4gIC8qKlxuICAgKiBNYXhpbXVtIG51bWJlciBvZiBzaW11bHRhbmVvdXMgZGVwbG95bWVudHMgKGRlcGVuZGVuY3kgcGVybWl0dGluZykgdG8gZXhlY3V0ZS5cbiAgICpcbiAgICogQGRlZmF1bHQgLSAxXG4gICAqL1xuICByZWFkb25seSBjb25jdXJyZW5jeT86IG51bWJlcjtcblxuICAvKipcbiAgICogUG9zaXRpb25hbCBhcmd1bWVudCBmb3Igd2F0Y2hcbiAgICovXG4gIHJlYWRvbmx5IFNUQUNLUz86IEFycmF5PHN0cmluZz47XG59XG5cbi8qKlxuICogRGVzdHJveSB0aGUgc3RhY2socykgbmFtZWQgU1RBQ0tTXG4gKlxuICogQHN0cnVjdFxuICovXG5leHBvcnQgaW50ZXJmYWNlIERlc3Ryb3lPcHRpb25zIHtcbiAgLyoqXG4gICAqIERlc3Ryb3kgYWxsIGF2YWlsYWJsZSBzdGFja3NcbiAgICpcbiAgICogQGRlZmF1bHQgLSBmYWxzZVxuICAgKi9cbiAgcmVhZG9ubHkgYWxsPzogYm9vbGVhbjtcblxuICAvKipcbiAgICogT25seSBkZXN0cm95IHJlcXVlc3RlZCBzdGFja3MsIGRvbid0IGluY2x1ZGUgZGVwZW5kZWVzXG4gICAqXG4gICAqIGFsaWFzZXM6IGVcbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IGV4Y2x1c2l2ZWx5PzogYm9vbGVhbjtcblxuICAvKipcbiAgICogRG8gbm90IGFzayBmb3IgY29uZmlybWF0aW9uIGJlZm9yZSBkZXN0cm95aW5nIHRoZSBzdGFja3NcbiAgICpcbiAgICogYWxpYXNlczogZlxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgZm9yY2U/OiBib29sZWFuO1xuXG4gIC8qKlxuICAgKiBQb3NpdGlvbmFsIGFyZ3VtZW50IGZvciBkZXN0cm95XG4gICAqL1xuICByZWFkb25seSBTVEFDS1M/OiBBcnJheTxzdHJpbmc+O1xufVxuXG4vKipcbiAqIENvbXBhcmVzIHRoZSBzcGVjaWZpZWQgc3RhY2sgd2l0aCB0aGUgZGVwbG95ZWQgc3RhY2sgb3IgYSBsb2NhbCB0ZW1wbGF0ZSBmaWxlLCBhbmQgcmV0dXJucyB3aXRoIHN0YXR1cyAxIGlmIGFueSBkaWZmZXJlbmNlIGlzIGZvdW5kXG4gKlxuICogQHN0cnVjdFxuICovXG5leHBvcnQgaW50ZXJmYWNlIERpZmZPcHRpb25zIHtcbiAgLyoqXG4gICAqIE9ubHkgZGlmZiByZXF1ZXN0ZWQgc3RhY2tzLCBkb24ndCBpbmNsdWRlIGRlcGVuZGVuY2llc1xuICAgKlxuICAgKiBhbGlhc2VzOiBlXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdW5kZWZpbmVkXG4gICAqL1xuICByZWFkb25seSBleGNsdXNpdmVseT86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIE51bWJlciBvZiBjb250ZXh0IGxpbmVzIHRvIGluY2x1ZGUgaW4gYXJiaXRyYXJ5IEpTT04gZGlmZiByZW5kZXJpbmdcbiAgICpcbiAgICogQGRlZmF1bHQgLSAzXG4gICAqL1xuICByZWFkb25seSBjb250ZXh0TGluZXM/OiBudW1iZXI7XG5cbiAgLyoqXG4gICAqIFRoZSBwYXRoIHRvIHRoZSBDbG91ZEZvcm1hdGlvbiB0ZW1wbGF0ZSB0byBjb21wYXJlIHdpdGhcbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IHRlbXBsYXRlPzogc3RyaW5nO1xuXG4gIC8qKlxuICAgKiBEbyBub3QgZmlsdGVyIG91dCBBV1M6OkNESzo6TWV0YWRhdGEgcmVzb3VyY2VzLCBtYW5nbGVkIG5vbi1BU0NJSSBjaGFyYWN0ZXJzLCBvciB0aGUgQ2hlY2tCb290c3RyYXBWZXJzaW9uUnVsZVxuICAgKlxuICAgKiBAZGVmYXVsdCAtIGZhbHNlXG4gICAqL1xuICByZWFkb25seSBzdHJpY3Q/OiBib29sZWFuO1xuXG4gIC8qKlxuICAgKiBPbmx5IGRpZmYgZm9yIGJyb2FkZW5lZCBzZWN1cml0eSBjaGFuZ2VzXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gZmFsc2VcbiAgICovXG4gIHJlYWRvbmx5IHNlY3VyaXR5T25seT86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIEZhaWwgd2l0aCBleGl0IGNvZGUgMSBpbiBjYXNlIG9mIGRpZmZcbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IGZhaWw/OiBib29sZWFuO1xuXG4gIC8qKlxuICAgKiBXaGV0aGVyIHRvIGNvbXBhcmUgYWdhaW5zdCB0aGUgdGVtcGxhdGUgd2l0aCBUcmFuc2Zvcm1zIGFscmVhZHkgcHJvY2Vzc2VkXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gZmFsc2VcbiAgICovXG4gIHJlYWRvbmx5IHByb2Nlc3NlZD86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIERvIG5vdCBwcmludCBzdGFjayBuYW1lIGFuZCBkZWZhdWx0IG1lc3NhZ2Ugd2hlbiB0aGVyZSBpcyBubyBkaWZmIHRvIHN0ZG91dFxuICAgKlxuICAgKiBhbGlhc2VzOiBxXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gZmFsc2VcbiAgICovXG4gIHJlYWRvbmx5IHF1aWV0PzogYm9vbGVhbjtcblxuICAvKipcbiAgICogV2hldGhlciB0byBjcmVhdGUgYSBjaGFuZ2VzZXQgdG8gYW5hbHl6ZSByZXNvdXJjZSByZXBsYWNlbWVudHMuIEluIHRoaXMgbW9kZSwgZGlmZiB3aWxsIHVzZSB0aGUgZGVwbG95IHJvbGUgaW5zdGVhZCBvZiB0aGUgbG9va3VwIHJvbGUuXG4gICAqXG4gICAqIGFsaWFzZXM6IGNoYW5nZXNldFxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHRydWVcbiAgICovXG4gIHJlYWRvbmx5IGNoYW5nZVNldD86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIFdoZXRoZXIgb3Igbm90IHRoZSBjaGFuZ2Ugc2V0IGltcG9ydHMgcmVzb3VyY2VzIHRoYXQgYWxyZWFkeSBleGlzdFxuICAgKlxuICAgKiBAZGVmYXVsdCAtIGZhbHNlXG4gICAqL1xuICByZWFkb25seSBpbXBvcnRFeGlzdGluZ1Jlc291cmNlcz86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIFBvc2l0aW9uYWwgYXJndW1lbnQgZm9yIGRpZmZcbiAgICovXG4gIHJlYWRvbmx5IFNUQUNLUz86IEFycmF5PHN0cmluZz47XG59XG5cbi8qKlxuICogRGV0ZWN0IGRyaWZ0cyBpbiB0aGUgZ2l2ZW4gQ2xvdWRGb3JtYXRpb24gc3RhY2socylcbiAqXG4gKiBAc3RydWN0XG4gKi9cbmV4cG9ydCBpbnRlcmZhY2UgRHJpZnRPcHRpb25zIHtcbiAgLyoqXG4gICAqIEZhaWwgd2l0aCBleGl0IGNvZGUgMSBpZiBkcmlmdCBpcyBkZXRlY3RlZFxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgZmFpbD86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIFBvc2l0aW9uYWwgYXJndW1lbnQgZm9yIGRyaWZ0XG4gICAqL1xuICByZWFkb25seSBTVEFDS1M/OiBBcnJheTxzdHJpbmc+O1xufVxuXG4vKipcbiAqIFJldHVybnMgYWxsIG1ldGFkYXRhIGFzc29jaWF0ZWQgd2l0aCB0aGlzIHN0YWNrXG4gKlxuICogQHN0cnVjdFxuICovXG5leHBvcnQgaW50ZXJmYWNlIE1ldGFkYXRhT3B0aW9ucyB7XG4gIC8qKlxuICAgKiBQb3NpdGlvbmFsIGFyZ3VtZW50IGZvciBtZXRhZGF0YVxuICAgKi9cbiAgcmVhZG9ubHkgU1RBQ0s/OiBzdHJpbmc7XG59XG5cbi8qKlxuICogQWNrbm93bGVkZ2UgYSBub3RpY2Ugc28gdGhhdCBpdCBkb2VzIG5vdCBzaG93IHVwIGFueW1vcmVcbiAqXG4gKiBhbGlhc2VzOiBhY2tcbiAqXG4gKiBAc3RydWN0XG4gKi9cbmV4cG9ydCBpbnRlcmZhY2UgQWNrbm93bGVkZ2VPcHRpb25zIHtcbiAgLyoqXG4gICAqIFBvc2l0aW9uYWwgYXJndW1lbnQgZm9yIGFja25vd2xlZGdlXG4gICAqL1xuICByZWFkb25seSBJRD86IHN0cmluZztcbn1cblxuLyoqXG4gKiBSZXR1cm5zIGEgbGlzdCBvZiByZWxldmFudCBub3RpY2VzXG4gKlxuICogQHN0cnVjdFxuICovXG5leHBvcnQgaW50ZXJmYWNlIE5vdGljZXNPcHRpb25zIHtcbiAgLyoqXG4gICAqIFJldHVybnMgYSBsaXN0IG9mIHVuYWNrbm93bGVkZ2VkIG5vdGljZXNcbiAgICpcbiAgICogYWxpYXNlczogdVxuICAgKlxuICAgKiBAZGVmYXVsdCAtIGZhbHNlXG4gICAqL1xuICByZWFkb25seSB1bmFja25vd2xlZGdlZD86IGJvb2xlYW47XG59XG5cbi8qKlxuICogQ3JlYXRlIGEgbmV3LCBlbXB0eSBDREsgcHJvamVjdCBmcm9tIGEgdGVtcGxhdGUuXG4gKlxuICogQHN0cnVjdFxuICovXG5leHBvcnQgaW50ZXJmYWNlIEluaXRPcHRpb25zIHtcbiAgLyoqXG4gICAqIFRoZSBsYW5ndWFnZSB0byBiZSB1c2VkIGZvciB0aGUgbmV3IHByb2plY3QgKGRlZmF1bHQgY2FuIGJlIGNvbmZpZ3VyZWQgaW4gfi8uY2RrLmpzb24pXG4gICAqXG4gICAqIGFsaWFzZXM6IGxcbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IGxhbmd1YWdlPzogc3RyaW5nO1xuXG4gIC8qKlxuICAgKiBMaXN0IHRoZSBhdmFpbGFibGUgdGVtcGxhdGVzXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdW5kZWZpbmVkXG4gICAqL1xuICByZWFkb25seSBsaXN0PzogYm9vbGVhbjtcblxuICAvKipcbiAgICogSWYgdHJ1ZSwgb25seSBnZW5lcmF0ZXMgcHJvamVjdCBmaWxlcywgd2l0aG91dCBleGVjdXRpbmcgYWRkaXRpb25hbCBvcGVyYXRpb25zIHN1Y2ggYXMgc2V0dGluZyB1cCBhIGdpdCByZXBvLCBpbnN0YWxsaW5nIGRlcGVuZGVuY2llcyBvciBjb21waWxpbmcgdGhlIHByb2plY3RcbiAgICpcbiAgICogQGRlZmF1bHQgLSBmYWxzZVxuICAgKi9cbiAgcmVhZG9ubHkgZ2VuZXJhdGVPbmx5PzogYm9vbGVhbjtcblxuICAvKipcbiAgICogVGhlIHZlcnNpb24gb2YgdGhlIENESyBsaWJyYXJ5IChhd3MtY2RrLWxpYikgdG8gaW5pdGlhbGl6ZSB0aGUgcHJvamVjdCB3aXRoLiBEZWZhdWx0cyB0byB0aGUgdmVyc2lvbiB0aGF0IHdhcyBjdXJyZW50IHdoZW4gdGhpcyBDTEkgd2FzIGJ1aWx0LlxuICAgKlxuICAgKiBhbGlhc2VzOiBWXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdW5kZWZpbmVkXG4gICAqL1xuICByZWFkb25seSBsaWJWZXJzaW9uPzogc3RyaW5nO1xuXG4gIC8qKlxuICAgKiBQb3NpdGlvbmFsIGFyZ3VtZW50IGZvciBpbml0XG4gICAqL1xuICByZWFkb25seSBURU1QTEFURT86IHN0cmluZztcbn1cblxuLyoqXG4gKiBNaWdyYXRlIGV4aXN0aW5nIEFXUyByZXNvdXJjZXMgaW50byBhIENESyBhcHBcbiAqXG4gKiBAc3RydWN0XG4gKi9cbmV4cG9ydCBpbnRlcmZhY2UgTWlncmF0ZU9wdGlvbnMge1xuICAvKipcbiAgICogVGhlIG5hbWUgYXNzaWduZWQgdG8gdGhlIHN0YWNrIGNyZWF0ZWQgaW4gdGhlIG5ldyBwcm9qZWN0LiBUaGUgbmFtZSBvZiB0aGUgYXBwIHdpbGwgYmUgYmFzZWQgb2ZmIHRoaXMgbmFtZSBhcyB3ZWxsLlxuICAgKlxuICAgKiBhbGlhc2VzOiBuXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdW5kZWZpbmVkXG4gICAqL1xuICByZWFkb25seSBzdGFja05hbWU/OiBzdHJpbmc7XG5cbiAgLyoqXG4gICAqIFRoZSBsYW5ndWFnZSB0byBiZSB1c2VkIGZvciB0aGUgbmV3IHByb2plY3RcbiAgICpcbiAgICogYWxpYXNlczogbFxuICAgKlxuICAgKiBAZGVmYXVsdCAtIFwidHlwZXNjcmlwdFwiXG4gICAqL1xuICByZWFkb25seSBsYW5ndWFnZT86IHN0cmluZztcblxuICAvKipcbiAgICogVGhlIGFjY291bnQgdG8gcmV0cmlldmUgdGhlIENsb3VkRm9ybWF0aW9uIHN0YWNrIHRlbXBsYXRlIGZyb21cbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IGFjY291bnQ/OiBzdHJpbmc7XG5cbiAgLyoqXG4gICAqIFRoZSByZWdpb24gdG8gcmV0cmlldmUgdGhlIENsb3VkRm9ybWF0aW9uIHN0YWNrIHRlbXBsYXRlIGZyb21cbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IHJlZ2lvbj86IHN0cmluZztcblxuICAvKipcbiAgICogVGhlIHBhdGggdG8gdGhlIENsb3VkRm9ybWF0aW9uIHRlbXBsYXRlIHRvIG1pZ3JhdGUuIFVzZSB0aGlzIGZvciBsb2NhbGx5IHN0b3JlZCB0ZW1wbGF0ZXNcbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IGZyb21QYXRoPzogc3RyaW5nO1xuXG4gIC8qKlxuICAgKiBVc2UgdGhpcyBmbGFnIHRvIHJldHJpZXZlIHRoZSB0ZW1wbGF0ZSBmb3IgYW4gZXhpc3RpbmcgQ2xvdWRGb3JtYXRpb24gc3RhY2tcbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IGZyb21TdGFjaz86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIFRoZSBvdXRwdXQgcGF0aCBmb3IgdGhlIG1pZ3JhdGVkIENESyBhcHBcbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IG91dHB1dFBhdGg/OiBzdHJpbmc7XG5cbiAgLyoqXG4gICAqIERldGVybWluZXMgaWYgYSBuZXcgc2NhbiBzaG91bGQgYmUgY3JlYXRlZCwgb3IgdGhlIGxhc3Qgc3VjY2Vzc2Z1bCBleGlzdGluZyBzY2FuIHNob3VsZCBiZSB1c2VkXG4gICAqICBvcHRpb25zIGFyZSBcIm5ld1wiIG9yIFwibW9zdC1yZWNlbnRcIlxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgZnJvbVNjYW4/OiBzdHJpbmc7XG5cbiAgLyoqXG4gICAqIEZpbHRlcnMgdGhlIHJlc291cmNlIHNjYW4gYmFzZWQgb24gdGhlIHByb3ZpZGVkIGNyaXRlcmlhIGluIHRoZSBmb2xsb3dpbmcgZm9ybWF0OiBcImtleTE9dmFsdWUxLGtleTI9dmFsdWUyXCJcbiAgICogIFRoaXMgZmllbGQgY2FuIGJlIHBhc3NlZCBtdWx0aXBsZSB0aW1lcyBmb3IgT1Igc3R5bGUgZmlsdGVyaW5nOlxuICAgKiAgZmlsdGVyaW5nIG9wdGlvbnM6XG4gICAqICByZXNvdXJjZS1pZGVudGlmaWVyOiBBIGtleS12YWx1ZSBwYWlyIHRoYXQgaWRlbnRpZmllcyB0aGUgdGFyZ2V0IHJlc291cmNlLiBpLmUuIHtcIkNsdXN0ZXJOYW1lXCIsIFwibXlDbHVzdGVyXCJ9XG4gICAqICByZXNvdXJjZS10eXBlLXByZWZpeDogQSBzdHJpbmcgdGhhdCByZXByZXNlbnRzIGEgdHlwZS1uYW1lIHByZWZpeC4gaS5lLiBcIkFXUzo6RHluYW1vREI6OlwiXG4gICAqICB0YWcta2V5OiBhIHN0cmluZyB0aGF0IG1hdGNoZXMgcmVzb3VyY2VzIHdpdGggYXQgbGVhc3Qgb25lIHRhZyB3aXRoIHRoZSBwcm92aWRlZCBrZXkuIGkuZS4gXCJteVRhZ0tleVwiXG4gICAqICB0YWctdmFsdWU6IGEgc3RyaW5nIHRoYXQgbWF0Y2hlcyByZXNvdXJjZXMgd2l0aCBhdCBsZWFzdCBvbmUgdGFnIHdpdGggdGhlIHByb3ZpZGVkIHZhbHVlLiBpLmUuIFwibXlUYWdWYWx1ZVwiXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdW5kZWZpbmVkXG4gICAqL1xuICByZWFkb25seSBmaWx0ZXI/OiBBcnJheTxzdHJpbmc+O1xuXG4gIC8qKlxuICAgKiBVc2UgdGhpcyBmbGFnIHRvIHppcCB0aGUgZ2VuZXJhdGVkIENESyBhcHBcbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IGNvbXByZXNzPzogYm9vbGVhbjtcbn1cblxuLyoqXG4gKiBNYW5hZ2UgY2FjaGVkIGNvbnRleHQgdmFsdWVzXG4gKlxuICogQHN0cnVjdFxuICovXG5leHBvcnQgaW50ZXJmYWNlIENvbnRleHRPcHRpb25zIHtcbiAgLyoqXG4gICAqIFRoZSBjb250ZXh0IGtleSAob3IgaXRzIGluZGV4KSB0byByZXNldFxuICAgKlxuICAgKiBhbGlhc2VzOiBlXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdW5kZWZpbmVkXG4gICAqL1xuICByZWFkb25seSByZXNldD86IHN0cmluZztcblxuICAvKipcbiAgICogSWdub3JlIG1pc3Npbmcga2V5IGVycm9yXG4gICAqXG4gICAqIGFsaWFzZXM6IGZcbiAgICpcbiAgICogQGRlZmF1bHQgLSBmYWxzZVxuICAgKi9cbiAgcmVhZG9ubHkgZm9yY2U/OiBib29sZWFuO1xuXG4gIC8qKlxuICAgKiBDbGVhciBhbGwgY29udGV4dFxuICAgKlxuICAgKiBAZGVmYXVsdCAtIGZhbHNlXG4gICAqL1xuICByZWFkb25seSBjbGVhcj86IGJvb2xlYW47XG59XG5cbi8qKlxuICogT3BlbnMgdGhlIHJlZmVyZW5jZSBkb2N1bWVudGF0aW9uIGluIGEgYnJvd3NlclxuICpcbiAqIGFsaWFzZXM6IGRvY1xuICpcbiAqIEBzdHJ1Y3RcbiAqL1xuZXhwb3J0IGludGVyZmFjZSBEb2NzT3B0aW9ucyB7XG4gIC8qKlxuICAgKiB0aGUgY29tbWFuZCB0byB1c2UgdG8gb3BlbiB0aGUgYnJvd3NlciwgdXNpbmcgJXUgYXMgYSBwbGFjZWhvbGRlciBmb3IgdGhlIHBhdGggb2YgdGhlIGZpbGUgdG8gb3BlblxuICAgKlxuICAgKiBhbGlhc2VzOiBiXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdW5kZWZpbmVkXG4gICAqL1xuICByZWFkb25seSBicm93c2VyPzogc3RyaW5nO1xufVxuXG4vKipcbiAqIE1vdmVzIHJlc291cmNlcyBiZXR3ZWVuIHN0YWNrcyBvciB3aXRoaW4gdGhlIHNhbWUgc3RhY2tcbiAqXG4gKiBAc3RydWN0XG4gKi9cbmV4cG9ydCBpbnRlcmZhY2UgUmVmYWN0b3JPcHRpb25zIHtcbiAgLyoqXG4gICAqIERvIG5vdCBwZXJmb3JtIGFueSBjaGFuZ2VzLCBqdXN0IHNob3cgd2hhdCB3b3VsZCBiZSBkb25lXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gZmFsc2VcbiAgICovXG4gIHJlYWRvbmx5IGRyeVJ1bj86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIElmIHNwZWNpZmllZCwgQ0RLIHdpbGwgdXNlIHRoZSBnaXZlbiBmaWxlIHRvIGV4Y2x1ZGUgcmVzb3VyY2VzIGZyb20gdGhlIHJlZmFjdG9yXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdW5kZWZpbmVkXG4gICAqL1xuICByZWFkb25seSBleGNsdWRlRmlsZT86IHN0cmluZztcblxuICAvKipcbiAgICogQSBmaWxlIHRoYXQgZGVjbGFyZXMgYW4gZXhwbGljaXQgbWFwcGluZyB0byBiZSBhcHBsaWVkLiBJZiBwcm92aWRlZCwgdGhlIGNvbW1hbmQgd2lsbCB1c2UgaXQgaW5zdGVhZCBvZiBjb21wdXRpbmcgdGhlIG1hcHBpbmcuXG4gICAqXG4gICAqIEBkZWZhdWx0IC0gdW5kZWZpbmVkXG4gICAqL1xuICByZWFkb25seSBtYXBwaW5nRmlsZT86IHN0cmluZztcblxuICAvKipcbiAgICogSWYgc3BlY2lmaWVkLCB0aGUgY29tbWFuZCB3aWxsIHJldmVydCB0aGUgcmVmYWN0b3Igb3BlcmF0aW9uLiBUaGlzIGlzIG9ubHkgdmFsaWQgaWYgYSBtYXBwaW5nIGZpbGUgd2FzIHByb3ZpZGVkLlxuICAgKlxuICAgKiBAZGVmYXVsdCAtIGZhbHNlXG4gICAqL1xuICByZWFkb25seSByZXZlcnQ/OiBib29sZWFuO1xuXG4gIC8qKlxuICAgKiBQb3NpdGlvbmFsIGFyZ3VtZW50IGZvciByZWZhY3RvclxuICAgKi9cbiAgcmVhZG9ubHkgU1RBQ0tTPzogQXJyYXk8c3RyaW5nPjtcbn1cblxuLyoqXG4gKiBFbmFibGUgb3IgZGlzYWJsZSBhbm9ueW1vdXMgdGVsZW1ldHJ5XG4gKlxuICogQHN0cnVjdFxuICovXG5leHBvcnQgaW50ZXJmYWNlIENsaVRlbGVtZXRyeU9wdGlvbnMge1xuICAvKipcbiAgICogRW5hYmxlIGFub255bW91cyB0ZWxlbWV0cnlcbiAgICpcbiAgICogQGRlZmF1bHQgLSB1bmRlZmluZWRcbiAgICovXG4gIHJlYWRvbmx5IGVuYWJsZT86IGJvb2xlYW47XG5cbiAgLyoqXG4gICAqIERpc2FibGUgYW5vbnltb3VzIHRlbGVtZXRyeVxuICAgKlxuICAgKiBAZGVmYXVsdCAtIHVuZGVmaW5lZFxuICAgKi9cbiAgcmVhZG9ubHkgZGlzYWJsZT86IGJvb2xlYW47XG59XG4iXX0=