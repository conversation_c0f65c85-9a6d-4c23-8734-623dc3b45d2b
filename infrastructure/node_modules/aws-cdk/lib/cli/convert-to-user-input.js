"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.convertYargsToUserInput = convertYargsToUserInput;
exports.convertConfigToUserInput = convertConfigToUserInput;
// @ts-ignore TS6133
function convertYargsToUserInput(args) {
    const globalOptions = {
        app: args.app,
        build: args.build,
        context: args.context,
        plugin: args.plugin,
        trace: args.trace,
        strict: args.strict,
        lookups: args.lookups,
        ignoreErrors: args.ignoreErrors,
        json: args.json,
        verbose: args.verbose,
        debug: args.debug,
        profile: args.profile,
        proxy: args.proxy,
        caBundlePath: args.caBundlePath,
        ec2creds: args.ec2creds,
        versionReporting: args.versionReporting,
        pathMetadata: args.pathMetadata,
        assetMetadata: args.assetMetadata,
        roleArn: args.roleArn,
        staging: args.staging,
        output: args.output,
        notices: args.notices,
        noColor: args.noColor,
        ci: args.ci,
        unstable: args.unstable,
    };
    let commandOptions;
    switch (args._[0]) {
        case 'list':
        case 'ls':
            commandOptions = {
                long: args.long,
                showDependencies: args.showDependencies,
                STACKS: args.STACKS,
            };
            break;
        case 'synth':
        case 'synthesize':
            commandOptions = {
                exclusively: args.exclusively,
                validation: args.validation,
                quiet: args.quiet,
                STACKS: args.STACKS,
            };
            break;
        case 'bootstrap':
            commandOptions = {
                bootstrapBucketName: args.bootstrapBucketName,
                bootstrapKmsKeyId: args.bootstrapKmsKeyId,
                examplePermissionsBoundary: args.examplePermissionsBoundary,
                customPermissionsBoundary: args.customPermissionsBoundary,
                bootstrapCustomerKey: args.bootstrapCustomerKey,
                qualifier: args.qualifier,
                publicAccessBlockConfiguration: args.publicAccessBlockConfiguration,
                tags: args.tags,
                execute: args.execute,
                trust: args.trust,
                trustForLookup: args.trustForLookup,
                untrust: args.untrust,
                cloudformationExecutionPolicies: args.cloudformationExecutionPolicies,
                force: args.force,
                terminationProtection: args.terminationProtection,
                showTemplate: args.showTemplate,
                toolkitStackName: args.toolkitStackName,
                template: args.template,
                previousParameters: args.previousParameters,
                ENVIRONMENTS: args.ENVIRONMENTS,
            };
            break;
        case 'gc':
            commandOptions = {
                action: args.action,
                type: args.type,
                rollbackBufferDays: args.rollbackBufferDays,
                createdBufferDays: args.createdBufferDays,
                confirm: args.confirm,
                bootstrapStackName: args.bootstrapStackName,
                ENVIRONMENTS: args.ENVIRONMENTS,
            };
            break;
        case 'deploy':
            commandOptions = {
                all: args.all,
                buildExclude: args.buildExclude,
                exclusively: args.exclusively,
                requireApproval: args.requireApproval,
                notificationArns: args.notificationArns,
                tags: args.tags,
                execute: args.execute,
                changeSetName: args.changeSetName,
                method: args.method,
                importExistingResources: args.importExistingResources,
                force: args.force,
                parameters: args.parameters,
                outputsFile: args.outputsFile,
                previousParameters: args.previousParameters,
                toolkitStackName: args.toolkitStackName,
                progress: args.progress,
                rollback: args.rollback,
                hotswap: args.hotswap,
                hotswapFallback: args.hotswapFallback,
                hotswapEcsMinimumHealthyPercent: args.hotswapEcsMinimumHealthyPercent,
                hotswapEcsMaximumHealthyPercent: args.hotswapEcsMaximumHealthyPercent,
                hotswapEcsStabilizationTimeoutSeconds: args.hotswapEcsStabilizationTimeoutSeconds,
                watch: args.watch,
                logs: args.logs,
                concurrency: args.concurrency,
                assetParallelism: args.assetParallelism,
                assetPrebuild: args.assetPrebuild,
                ignoreNoStacks: args.ignoreNoStacks,
                STACKS: args.STACKS,
            };
            break;
        case 'rollback':
            commandOptions = {
                all: args.all,
                toolkitStackName: args.toolkitStackName,
                force: args.force,
                validateBootstrapVersion: args.validateBootstrapVersion,
                orphan: args.orphan,
                STACKS: args.STACKS,
            };
            break;
        case 'import':
            commandOptions = {
                execute: args.execute,
                changeSetName: args.changeSetName,
                toolkitStackName: args.toolkitStackName,
                rollback: args.rollback,
                force: args.force,
                recordResourceMapping: args.recordResourceMapping,
                resourceMapping: args.resourceMapping,
                STACK: args.STACK,
            };
            break;
        case 'watch':
            commandOptions = {
                buildExclude: args.buildExclude,
                exclusively: args.exclusively,
                changeSetName: args.changeSetName,
                force: args.force,
                toolkitStackName: args.toolkitStackName,
                progress: args.progress,
                rollback: args.rollback,
                hotswap: args.hotswap,
                hotswapFallback: args.hotswapFallback,
                hotswapEcsMinimumHealthyPercent: args.hotswapEcsMinimumHealthyPercent,
                hotswapEcsMaximumHealthyPercent: args.hotswapEcsMaximumHealthyPercent,
                hotswapEcsStabilizationTimeoutSeconds: args.hotswapEcsStabilizationTimeoutSeconds,
                logs: args.logs,
                concurrency: args.concurrency,
                STACKS: args.STACKS,
            };
            break;
        case 'destroy':
            commandOptions = {
                all: args.all,
                exclusively: args.exclusively,
                force: args.force,
                STACKS: args.STACKS,
            };
            break;
        case 'diff':
            commandOptions = {
                exclusively: args.exclusively,
                contextLines: args.contextLines,
                template: args.template,
                strict: args.strict,
                securityOnly: args.securityOnly,
                fail: args.fail,
                processed: args.processed,
                quiet: args.quiet,
                changeSet: args.changeSet,
                importExistingResources: args.importExistingResources,
                STACKS: args.STACKS,
            };
            break;
        case 'drift':
            commandOptions = {
                fail: args.fail,
                STACKS: args.STACKS,
            };
            break;
        case 'metadata':
            commandOptions = {
                STACK: args.STACK,
            };
            break;
        case 'acknowledge':
        case 'ack':
            commandOptions = {
                ID: args.ID,
            };
            break;
        case 'notices':
            commandOptions = {
                unacknowledged: args.unacknowledged,
            };
            break;
        case 'init':
            commandOptions = {
                language: args.language,
                list: args.list,
                generateOnly: args.generateOnly,
                libVersion: args.libVersion,
                TEMPLATE: args.TEMPLATE,
            };
            break;
        case 'migrate':
            commandOptions = {
                stackName: args.stackName,
                language: args.language,
                account: args.account,
                region: args.region,
                fromPath: args.fromPath,
                fromStack: args.fromStack,
                outputPath: args.outputPath,
                fromScan: args.fromScan,
                filter: args.filter,
                compress: args.compress,
            };
            break;
        case 'context':
            commandOptions = {
                reset: args.reset,
                force: args.force,
                clear: args.clear,
            };
            break;
        case 'docs':
        case 'doc':
            commandOptions = {
                browser: args.browser,
            };
            break;
        case 'doctor':
            commandOptions = {};
            break;
        case 'refactor':
            commandOptions = {
                dryRun: args.dryRun,
                excludeFile: args.excludeFile,
                mappingFile: args.mappingFile,
                revert: args.revert,
                STACKS: args.STACKS,
            };
            break;
        case 'cli-telemetry':
            commandOptions = {
                enable: args.enable,
                disable: args.disable,
            };
            break;
    }
    const userInput = {
        command: args._[0],
        globalOptions,
        [args._[0]]: commandOptions,
    };
    return userInput;
}
// @ts-ignore TS6133
function convertConfigToUserInput(config) {
    const globalOptions = {
        app: config.app,
        build: config.build,
        context: config.context,
        plugin: config.plugin,
        trace: config.trace,
        strict: config.strict,
        lookups: config.lookups,
        ignoreErrors: config.ignoreErrors,
        json: config.json,
        verbose: config.verbose,
        debug: config.debug,
        profile: config.profile,
        proxy: config.proxy,
        caBundlePath: config.caBundlePath,
        ec2creds: config.ec2creds,
        versionReporting: config.versionReporting,
        pathMetadata: config.pathMetadata,
        assetMetadata: config.assetMetadata,
        roleArn: config.roleArn,
        staging: config.staging,
        output: config.output,
        notices: config.notices,
        noColor: config.noColor,
        ci: config.ci,
        unstable: config.unstable,
    };
    const listOptions = {
        long: config.list?.long,
        showDependencies: config.list?.showDependencies,
    };
    const synthOptions = {
        exclusively: config.synth?.exclusively,
        validation: config.synth?.validation,
        quiet: config.synth?.quiet,
    };
    const bootstrapOptions = {
        bootstrapBucketName: config.bootstrap?.bootstrapBucketName,
        bootstrapKmsKeyId: config.bootstrap?.bootstrapKmsKeyId,
        examplePermissionsBoundary: config.bootstrap?.examplePermissionsBoundary,
        customPermissionsBoundary: config.bootstrap?.customPermissionsBoundary,
        bootstrapCustomerKey: config.bootstrap?.bootstrapCustomerKey,
        qualifier: config.bootstrap?.qualifier,
        publicAccessBlockConfiguration: config.bootstrap?.publicAccessBlockConfiguration,
        tags: config.bootstrap?.tags,
        execute: config.bootstrap?.execute,
        trust: config.bootstrap?.trust,
        trustForLookup: config.bootstrap?.trustForLookup,
        untrust: config.bootstrap?.untrust,
        cloudformationExecutionPolicies: config.bootstrap?.cloudformationExecutionPolicies,
        force: config.bootstrap?.force,
        terminationProtection: config.bootstrap?.terminationProtection,
        showTemplate: config.bootstrap?.showTemplate,
        toolkitStackName: config.bootstrap?.toolkitStackName,
        template: config.bootstrap?.template,
        previousParameters: config.bootstrap?.previousParameters,
    };
    const gcOptions = {
        action: config.gc?.action,
        type: config.gc?.type,
        rollbackBufferDays: config.gc?.rollbackBufferDays,
        createdBufferDays: config.gc?.createdBufferDays,
        confirm: config.gc?.confirm,
        bootstrapStackName: config.gc?.bootstrapStackName,
    };
    const deployOptions = {
        all: config.deploy?.all,
        buildExclude: config.deploy?.buildExclude,
        exclusively: config.deploy?.exclusively,
        requireApproval: config.deploy?.requireApproval,
        notificationArns: config.deploy?.notificationArns,
        tags: config.deploy?.tags,
        execute: config.deploy?.execute,
        changeSetName: config.deploy?.changeSetName,
        method: config.deploy?.method,
        importExistingResources: config.deploy?.importExistingResources,
        force: config.deploy?.force,
        parameters: config.deploy?.parameters,
        outputsFile: config.deploy?.outputsFile,
        previousParameters: config.deploy?.previousParameters,
        toolkitStackName: config.deploy?.toolkitStackName,
        progress: config.deploy?.progress,
        rollback: config.deploy?.rollback,
        hotswap: config.deploy?.hotswap,
        hotswapFallback: config.deploy?.hotswapFallback,
        hotswapEcsMinimumHealthyPercent: config.deploy?.hotswapEcsMinimumHealthyPercent,
        hotswapEcsMaximumHealthyPercent: config.deploy?.hotswapEcsMaximumHealthyPercent,
        hotswapEcsStabilizationTimeoutSeconds: config.deploy?.hotswapEcsStabilizationTimeoutSeconds,
        watch: config.deploy?.watch,
        logs: config.deploy?.logs,
        concurrency: config.deploy?.concurrency,
        assetParallelism: config.deploy?.assetParallelism,
        assetPrebuild: config.deploy?.assetPrebuild,
        ignoreNoStacks: config.deploy?.ignoreNoStacks,
    };
    const rollbackOptions = {
        all: config.rollback?.all,
        toolkitStackName: config.rollback?.toolkitStackName,
        force: config.rollback?.force,
        validateBootstrapVersion: config.rollback?.validateBootstrapVersion,
        orphan: config.rollback?.orphan,
    };
    const importOptions = {
        execute: config.import?.execute,
        changeSetName: config.import?.changeSetName,
        toolkitStackName: config.import?.toolkitStackName,
        rollback: config.import?.rollback,
        force: config.import?.force,
        recordResourceMapping: config.import?.recordResourceMapping,
        resourceMapping: config.import?.resourceMapping,
    };
    const watchOptions = {
        buildExclude: config.watch?.buildExclude,
        exclusively: config.watch?.exclusively,
        changeSetName: config.watch?.changeSetName,
        force: config.watch?.force,
        toolkitStackName: config.watch?.toolkitStackName,
        progress: config.watch?.progress,
        rollback: config.watch?.rollback,
        hotswap: config.watch?.hotswap,
        hotswapFallback: config.watch?.hotswapFallback,
        hotswapEcsMinimumHealthyPercent: config.watch?.hotswapEcsMinimumHealthyPercent,
        hotswapEcsMaximumHealthyPercent: config.watch?.hotswapEcsMaximumHealthyPercent,
        hotswapEcsStabilizationTimeoutSeconds: config.watch?.hotswapEcsStabilizationTimeoutSeconds,
        logs: config.watch?.logs,
        concurrency: config.watch?.concurrency,
    };
    const destroyOptions = {
        all: config.destroy?.all,
        exclusively: config.destroy?.exclusively,
        force: config.destroy?.force,
    };
    const diffOptions = {
        exclusively: config.diff?.exclusively,
        contextLines: config.diff?.contextLines,
        template: config.diff?.template,
        strict: config.diff?.strict,
        securityOnly: config.diff?.securityOnly,
        fail: config.diff?.fail,
        processed: config.diff?.processed,
        quiet: config.diff?.quiet,
        changeSet: config.diff?.changeSet,
        importExistingResources: config.diff?.importExistingResources,
    };
    const driftOptions = {
        fail: config.drift?.fail,
    };
    const metadataOptions = {};
    const acknowledgeOptions = {};
    const noticesOptions = {
        unacknowledged: config.notices?.unacknowledged,
    };
    const initOptions = {
        language: config.init?.language,
        list: config.init?.list,
        generateOnly: config.init?.generateOnly,
        libVersion: config.init?.libVersion,
    };
    const migrateOptions = {
        stackName: config.migrate?.stackName,
        language: config.migrate?.language,
        account: config.migrate?.account,
        region: config.migrate?.region,
        fromPath: config.migrate?.fromPath,
        fromStack: config.migrate?.fromStack,
        outputPath: config.migrate?.outputPath,
        fromScan: config.migrate?.fromScan,
        filter: config.migrate?.filter,
        compress: config.migrate?.compress,
    };
    const contextOptions = {
        reset: config.context?.reset,
        force: config.context?.force,
        clear: config.context?.clear,
    };
    const docsOptions = {
        browser: config.docs?.browser,
    };
    const doctorOptions = {};
    const refactorOptions = {
        dryRun: config.refactor?.dryRun,
        excludeFile: config.refactor?.excludeFile,
        mappingFile: config.refactor?.mappingFile,
        revert: config.refactor?.revert,
    };
    const cliTelemetryOptions = {
        enable: config.cliTelemetry?.enable,
        disable: config.cliTelemetry?.disable,
    };
    const userInput = {
        globalOptions,
        list: listOptions,
        synth: synthOptions,
        bootstrap: bootstrapOptions,
        gc: gcOptions,
        deploy: deployOptions,
        rollback: rollbackOptions,
        import: importOptions,
        watch: watchOptions,
        destroy: destroyOptions,
        diff: diffOptions,
        drift: driftOptions,
        metadata: metadataOptions,
        acknowledge: acknowledgeOptions,
        notices: noticesOptions,
        init: initOptions,
        migrate: migrateOptions,
        context: contextOptions,
        docs: docsOptions,
        doctor: doctorOptions,
        refactor: refactorOptions,
        cliTelemetry: cliTelemetryOptions,
    };
    return userInput;
}
//# sourceMappingURL=data:application/json;base64,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