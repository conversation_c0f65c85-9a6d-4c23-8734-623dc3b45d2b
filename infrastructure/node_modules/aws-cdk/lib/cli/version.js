"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VersionCheckTTL = void 0;
exports.displayVersion = displayVersion;
exports.isDeveloperBuild = isDeveloperBuild;
exports.versionNumber = versionNumber;
exports.getVersionMessages = getVersionMessages;
exports.displayVersionMessage = displayVersionMessage;
/* c8 ignore start */
const path = require("path");
const toolkit_lib_1 = require("@aws-cdk/toolkit-lib");
const chalk = require("chalk");
const fs = require("fs-extra");
const semver = require("semver");
const logging_1 = require("../logging");
const util_1 = require("../util");
const root_dir_1 = require("./root-dir");
const console_formatters_1 = require("./util/console-formatters");
const npm_1 = require("./util/npm");
const ONE_DAY_IN_SECONDS = 1 * 24 * 60 * 60;
const UPGRADE_DOCUMENTATION_LINKS = {
    1: 'https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html',
};
function displayVersion() {
    return `${versionNumber()} (build ${commit()})`;
}
function isDeveloperBuild() {
    return versionNumber() === '0.0.0';
}
function versionNumber() {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    return require(path.join((0, root_dir_1.cliRootDir)(), 'package.json')).version.replace(/\+[0-9a-f]+$/, '');
}
function commit() {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    return require(path.join((0, root_dir_1.cliRootDir)(), 'build-info.json')).commit;
}
class VersionCheckTTL {
    static timestampFilePath() {
        // Using the same path from account-cache.ts
        return path.join((0, util_1.cdkCacheDir)(), 'repo-version-ttl');
    }
    constructor(file, ttlSecs) {
        this.file = file || VersionCheckTTL.timestampFilePath();
        try {
            fs.mkdirsSync(path.dirname(this.file));
            fs.accessSync(path.dirname(this.file), fs.constants.W_OK);
        }
        catch {
            throw new toolkit_lib_1.ToolkitError(`Directory (${path.dirname(this.file)}) is not writable.`);
        }
        this.ttlSecs = ttlSecs || ONE_DAY_IN_SECONDS;
    }
    async hasExpired() {
        try {
            const lastCheckTime = (await fs.stat(this.file)).mtimeMs;
            const today = new Date().getTime();
            if ((today - lastCheckTime) / 1000 > this.ttlSecs) { // convert ms to sec
                return true;
            }
            return false;
        }
        catch (err) {
            if (err.code === 'ENOENT') {
                return true;
            }
            else {
                throw err;
            }
        }
    }
    async update(latestVersion) {
        if (!latestVersion) {
            latestVersion = '';
        }
        await fs.writeFile(this.file, latestVersion);
    }
}
exports.VersionCheckTTL = VersionCheckTTL;
// Export for unit testing only.
// Don't use directly, use displayVersionMessage() instead.
async function getVersionMessages(currentVersion, cacheFile) {
    if (!(await cacheFile.hasExpired())) {
        return [];
    }
    const packageInfo = await (0, npm_1.execNpmView)(currentVersion);
    const latestVersion = packageInfo.latestVersion;
    await cacheFile.update(JSON.stringify(packageInfo));
    // If the latest version is the same as the current version, there is no need to display a message
    if (semver.eq(latestVersion, currentVersion)) {
        return [];
    }
    const versionMessage = [
        packageInfo.deprecated ? `${chalk.red(packageInfo.deprecated)}` : undefined,
        `Newer version of CDK is available [${chalk.green(latestVersion)}]`,
        getMajorVersionUpgradeMessage(currentVersion),
        'Upgrade recommended (npm install -g aws-cdk)',
    ].filter(Boolean);
    return versionMessage;
}
function getMajorVersionUpgradeMessage(currentVersion) {
    const currentMajorVersion = semver.major(currentVersion);
    if (UPGRADE_DOCUMENTATION_LINKS[currentMajorVersion]) {
        return `Information about upgrading from version ${currentMajorVersion}.x to version ${currentMajorVersion + 1}.x is available here: ${UPGRADE_DOCUMENTATION_LINKS[currentMajorVersion]}`;
    }
}
async function displayVersionMessage(currentVersion = versionNumber(), versionCheckCache) {
    if (!process.stdout.isTTY || process.env.CDK_DISABLE_VERSION_CHECK) {
        return;
    }
    try {
        const versionMessages = await getVersionMessages(currentVersion, versionCheckCache ?? new VersionCheckTTL());
        (0, console_formatters_1.formatAsBanner)(versionMessages).forEach(e => (0, logging_1.info)(e));
    }
    catch (err) {
        (0, logging_1.debug)(`Could not run version check - ${err.message}`);
    }
}
/* c8 ignore stop */
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidmVyc2lvbi5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbInZlcnNpb24udHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7O0FBa0JBLHdDQUVDO0FBRUQsNENBRUM7QUFFRCxzQ0FHQztBQXlERCxnREFzQkM7QUFTRCxzREFXQztBQWhJRCxxQkFBcUI7QUFDckIsNkJBQTZCO0FBQzdCLHNEQUFvRDtBQUNwRCwrQkFBK0I7QUFDL0IsK0JBQStCO0FBQy9CLGlDQUFpQztBQUNqQyx3Q0FBeUM7QUFDekMsa0NBQXNDO0FBQ3RDLHlDQUF3QztBQUN4QyxrRUFBMkQ7QUFDM0Qsb0NBQXlDO0FBRXpDLE1BQU0sa0JBQWtCLEdBQUcsQ0FBQyxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxDQUFDO0FBRTVDLE1BQU0sMkJBQTJCLEdBQTJCO0lBQzFELENBQUMsRUFBRSw0REFBNEQ7Q0FDaEUsQ0FBQztBQUVGLFNBQWdCLGNBQWM7SUFDNUIsT0FBTyxHQUFHLGFBQWEsRUFBRSxXQUFXLE1BQU0sRUFBRSxHQUFHLENBQUM7QUFDbEQsQ0FBQztBQUVELFNBQWdCLGdCQUFnQjtJQUM5QixPQUFPLGFBQWEsRUFBRSxLQUFLLE9BQU8sQ0FBQztBQUNyQyxDQUFDO0FBRUQsU0FBZ0IsYUFBYTtJQUMzQixpRUFBaUU7SUFDakUsT0FBTyxPQUFPLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFBLHFCQUFVLEdBQUUsRUFBRSxjQUFjLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxPQUFPLENBQUMsY0FBYyxFQUFFLEVBQUUsQ0FBQyxDQUFDO0FBQzlGLENBQUM7QUFFRCxTQUFTLE1BQU07SUFDYixpRUFBaUU7SUFDakUsT0FBTyxPQUFPLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFBLHFCQUFVLEdBQUUsRUFBRSxpQkFBaUIsQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDO0FBQ3BFLENBQUM7QUFFRCxNQUFhLGVBQWU7SUFDbkIsTUFBTSxDQUFDLGlCQUFpQjtRQUM3Qiw0Q0FBNEM7UUFDNUMsT0FBTyxJQUFJLENBQUMsSUFBSSxDQUFDLElBQUEsa0JBQVcsR0FBRSxFQUFFLGtCQUFrQixDQUFDLENBQUM7SUFDdEQsQ0FBQztJQU9ELFlBQVksSUFBYSxFQUFFLE9BQWdCO1FBQ3pDLElBQUksQ0FBQyxJQUFJLEdBQUcsSUFBSSxJQUFJLGVBQWUsQ0FBQyxpQkFBaUIsRUFBRSxDQUFDO1FBQ3hELElBQUksQ0FBQztZQUNILEVBQUUsQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQztZQUN2QyxFQUFFLENBQUMsVUFBVSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxFQUFFLEVBQUUsQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDNUQsQ0FBQztRQUFDLE1BQU0sQ0FBQztZQUNQLE1BQU0sSUFBSSwwQkFBWSxDQUFDLGNBQWMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLG9CQUFvQixDQUFDLENBQUM7UUFDcEYsQ0FBQztRQUNELElBQUksQ0FBQyxPQUFPLEdBQUcsT0FBTyxJQUFJLGtCQUFrQixDQUFDO0lBQy9DLENBQUM7SUFFTSxLQUFLLENBQUMsVUFBVTtRQUNyQixJQUFJLENBQUM7WUFDSCxNQUFNLGFBQWEsR0FBRyxDQUFDLE1BQU0sRUFBRSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUM7WUFDekQsTUFBTSxLQUFLLEdBQUcsSUFBSSxJQUFJLEVBQUUsQ0FBQyxPQUFPLEVBQUUsQ0FBQztZQUVuQyxJQUFJLENBQUMsS0FBSyxHQUFHLGFBQWEsQ0FBQyxHQUFHLElBQUksR0FBRyxJQUFJLENBQUMsT0FBTyxFQUFFLENBQUMsQ0FBQyxvQkFBb0I7Z0JBQ3ZFLE9BQU8sSUFBSSxDQUFDO1lBQ2QsQ0FBQztZQUNELE9BQU8sS0FBSyxDQUFDO1FBQ2YsQ0FBQztRQUFDLE9BQU8sR0FBUSxFQUFFLENBQUM7WUFDbEIsSUFBSSxHQUFHLENBQUMsSUFBSSxLQUFLLFFBQVEsRUFBRSxDQUFDO2dCQUMxQixPQUFPLElBQUksQ0FBQztZQUNkLENBQUM7aUJBQU0sQ0FBQztnQkFDTixNQUFNLEdBQUcsQ0FBQztZQUNaLENBQUM7UUFDSCxDQUFDO0lBQ0gsQ0FBQztJQUVNLEtBQUssQ0FBQyxNQUFNLENBQUMsYUFBc0I7UUFDeEMsSUFBSSxDQUFDLGFBQWEsRUFBRSxDQUFDO1lBQ25CLGFBQWEsR0FBRyxFQUFFLENBQUM7UUFDckIsQ0FBQztRQUNELE1BQU0sRUFBRSxDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUMsSUFBSSxFQUFFLGFBQWEsQ0FBQyxDQUFDO0lBQy9DLENBQUM7Q0FDRjtBQTlDRCwwQ0E4Q0M7QUFFRCxnQ0FBZ0M7QUFDaEMsMkRBQTJEO0FBQ3BELEtBQUssVUFBVSxrQkFBa0IsQ0FBQyxjQUFzQixFQUFFLFNBQTBCO0lBQ3pGLElBQUksQ0FBQyxDQUFDLE1BQU0sU0FBUyxDQUFDLFVBQVUsRUFBRSxDQUFDLEVBQUUsQ0FBQztRQUNwQyxPQUFPLEVBQUUsQ0FBQztJQUNaLENBQUM7SUFFRCxNQUFNLFdBQVcsR0FBRyxNQUFNLElBQUEsaUJBQVcsRUFBQyxjQUFjLENBQUMsQ0FBQztJQUN0RCxNQUFNLGFBQWEsR0FBRyxXQUFXLENBQUMsYUFBYSxDQUFDO0lBQ2hELE1BQU0sU0FBUyxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsU0FBUyxDQUFDLFdBQVcsQ0FBQyxDQUFDLENBQUM7SUFFcEQsa0dBQWtHO0lBQ2xHLElBQUksTUFBTSxDQUFDLEVBQUUsQ0FBQyxhQUFhLEVBQUUsY0FBYyxDQUFDLEVBQUUsQ0FBQztRQUM3QyxPQUFPLEVBQUUsQ0FBQztJQUNaLENBQUM7SUFFRCxNQUFNLGNBQWMsR0FBRztRQUNyQixXQUFXLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQyxHQUFHLEtBQUssQ0FBQyxHQUFHLENBQUMsV0FBVyxDQUFDLFVBQW9CLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxTQUFTO1FBQ3JGLHNDQUFzQyxLQUFLLENBQUMsS0FBSyxDQUFDLGFBQXVCLENBQUMsR0FBRztRQUM3RSw2QkFBNkIsQ0FBQyxjQUFjLENBQUM7UUFDN0MsOENBQThDO0tBQy9DLENBQUMsTUFBTSxDQUFDLE9BQU8sQ0FBYSxDQUFDO0lBRTlCLE9BQU8sY0FBYyxDQUFDO0FBQ3hCLENBQUM7QUFFRCxTQUFTLDZCQUE2QixDQUFDLGNBQXNCO0lBQzNELE1BQU0sbUJBQW1CLEdBQUcsTUFBTSxDQUFDLEtBQUssQ0FBQyxjQUFjLENBQUMsQ0FBQztJQUN6RCxJQUFJLDJCQUEyQixDQUFDLG1CQUFtQixDQUFDLEVBQUUsQ0FBQztRQUNyRCxPQUFPLDRDQUE0QyxtQkFBbUIsaUJBQWlCLG1CQUFtQixHQUFHLENBQUMseUJBQXlCLDJCQUEyQixDQUFDLG1CQUFtQixDQUFDLEVBQUUsQ0FBQztJQUM1TCxDQUFDO0FBQ0gsQ0FBQztBQUVNLEtBQUssVUFBVSxxQkFBcUIsQ0FBQyxjQUFjLEdBQUcsYUFBYSxFQUFFLEVBQUUsaUJBQW1DO0lBQy9HLElBQUksQ0FBQyxPQUFPLENBQUMsTUFBTSxDQUFDLEtBQUssSUFBSSxPQUFPLENBQUMsR0FBRyxDQUFDLHlCQUF5QixFQUFFLENBQUM7UUFDbkUsT0FBTztJQUNULENBQUM7SUFFRCxJQUFJLENBQUM7UUFDSCxNQUFNLGVBQWUsR0FBRyxNQUFNLGtCQUFrQixDQUFDLGNBQWMsRUFBRSxpQkFBaUIsSUFBSSxJQUFJLGVBQWUsRUFBRSxDQUFDLENBQUM7UUFDN0csSUFBQSxtQ0FBYyxFQUFDLGVBQWUsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLElBQUEsY0FBSSxFQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7SUFDeEQsQ0FBQztJQUFDLE9BQU8sR0FBUSxFQUFFLENBQUM7UUFDbEIsSUFBQSxlQUFLLEVBQUMsaUNBQWlDLEdBQUcsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxDQUFDO0lBQ3hELENBQUM7QUFDSCxDQUFDO0FBQ0Qsb0JBQW9CIiwic291cmNlc0NvbnRlbnQiOlsiLyogYzggaWdub3JlIHN0YXJ0ICovXG5pbXBvcnQgKiBhcyBwYXRoIGZyb20gJ3BhdGgnO1xuaW1wb3J0IHsgVG9vbGtpdEVycm9yIH0gZnJvbSAnQGF3cy1jZGsvdG9vbGtpdC1saWInO1xuaW1wb3J0ICogYXMgY2hhbGsgZnJvbSAnY2hhbGsnO1xuaW1wb3J0ICogYXMgZnMgZnJvbSAnZnMtZXh0cmEnO1xuaW1wb3J0ICogYXMgc2VtdmVyIGZyb20gJ3NlbXZlcic7XG5pbXBvcnQgeyBkZWJ1ZywgaW5mbyB9IGZyb20gJy4uL2xvZ2dpbmcnO1xuaW1wb3J0IHsgY2RrQ2FjaGVEaXIgfSBmcm9tICcuLi91dGlsJztcbmltcG9ydCB7IGNsaVJvb3REaXIgfSBmcm9tICcuL3Jvb3QtZGlyJztcbmltcG9ydCB7IGZvcm1hdEFzQmFubmVyIH0gZnJvbSAnLi91dGlsL2NvbnNvbGUtZm9ybWF0dGVycyc7XG5pbXBvcnQgeyBleGVjTnBtVmlldyB9IGZyb20gJy4vdXRpbC9ucG0nO1xuXG5jb25zdCBPTkVfREFZX0lOX1NFQ09ORFMgPSAxICogMjQgKiA2MCAqIDYwO1xuXG5jb25zdCBVUEdSQURFX0RPQ1VNRU5UQVRJT05fTElOS1M6IFJlY29yZDxudW1iZXIsIHN0cmluZz4gPSB7XG4gIDE6ICdodHRwczovL2RvY3MuYXdzLmFtYXpvbi5jb20vY2RrL3YyL2d1aWRlL21pZ3JhdGluZy12Mi5odG1sJyxcbn07XG5cbmV4cG9ydCBmdW5jdGlvbiBkaXNwbGF5VmVyc2lvbigpIHtcbiAgcmV0dXJuIGAke3ZlcnNpb25OdW1iZXIoKX0gKGJ1aWxkICR7Y29tbWl0KCl9KWA7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBpc0RldmVsb3BlckJ1aWxkKCk6IGJvb2xlYW4ge1xuICByZXR1cm4gdmVyc2lvbk51bWJlcigpID09PSAnMC4wLjAnO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gdmVyc2lvbk51bWJlcigpOiBzdHJpbmcge1xuICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L25vLXJlcXVpcmUtaW1wb3J0c1xuICByZXR1cm4gcmVxdWlyZShwYXRoLmpvaW4oY2xpUm9vdERpcigpLCAncGFja2FnZS5qc29uJykpLnZlcnNpb24ucmVwbGFjZSgvXFwrWzAtOWEtZl0rJC8sICcnKTtcbn1cblxuZnVuY3Rpb24gY29tbWl0KCk6IHN0cmluZyB7XG4gIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tcmVxdWlyZS1pbXBvcnRzXG4gIHJldHVybiByZXF1aXJlKHBhdGguam9pbihjbGlSb290RGlyKCksICdidWlsZC1pbmZvLmpzb24nKSkuY29tbWl0O1xufVxuXG5leHBvcnQgY2xhc3MgVmVyc2lvbkNoZWNrVFRMIHtcbiAgcHVibGljIHN0YXRpYyB0aW1lc3RhbXBGaWxlUGF0aCgpOiBzdHJpbmcge1xuICAgIC8vIFVzaW5nIHRoZSBzYW1lIHBhdGggZnJvbSBhY2NvdW50LWNhY2hlLnRzXG4gICAgcmV0dXJuIHBhdGguam9pbihjZGtDYWNoZURpcigpLCAncmVwby12ZXJzaW9uLXR0bCcpO1xuICB9XG5cbiAgcHJpdmF0ZSByZWFkb25seSBmaWxlOiBzdHJpbmc7XG5cbiAgLy8gRmlsZSBtb2RpZnkgdGltZXMgYXJlIGFjY3VyYXRlIG9ubHkgdG8gdGhlIHNlY29uZFxuICBwcml2YXRlIHJlYWRvbmx5IHR0bFNlY3M6IG51bWJlcjtcblxuICBjb25zdHJ1Y3RvcihmaWxlPzogc3RyaW5nLCB0dGxTZWNzPzogbnVtYmVyKSB7XG4gICAgdGhpcy5maWxlID0gZmlsZSB8fCBWZXJzaW9uQ2hlY2tUVEwudGltZXN0YW1wRmlsZVBhdGgoKTtcbiAgICB0cnkge1xuICAgICAgZnMubWtkaXJzU3luYyhwYXRoLmRpcm5hbWUodGhpcy5maWxlKSk7XG4gICAgICBmcy5hY2Nlc3NTeW5jKHBhdGguZGlybmFtZSh0aGlzLmZpbGUpLCBmcy5jb25zdGFudHMuV19PSyk7XG4gICAgfSBjYXRjaCB7XG4gICAgICB0aHJvdyBuZXcgVG9vbGtpdEVycm9yKGBEaXJlY3RvcnkgKCR7cGF0aC5kaXJuYW1lKHRoaXMuZmlsZSl9KSBpcyBub3Qgd3JpdGFibGUuYCk7XG4gICAgfVxuICAgIHRoaXMudHRsU2VjcyA9IHR0bFNlY3MgfHwgT05FX0RBWV9JTl9TRUNPTkRTO1xuICB9XG5cbiAgcHVibGljIGFzeW5jIGhhc0V4cGlyZWQoKTogUHJvbWlzZTxib29sZWFuPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGxhc3RDaGVja1RpbWUgPSAoYXdhaXQgZnMuc3RhdCh0aGlzLmZpbGUpKS5tdGltZU1zO1xuICAgICAgY29uc3QgdG9kYXkgPSBuZXcgRGF0ZSgpLmdldFRpbWUoKTtcblxuICAgICAgaWYgKCh0b2RheSAtIGxhc3RDaGVja1RpbWUpIC8gMTAwMCA+IHRoaXMudHRsU2VjcykgeyAvLyBjb252ZXJ0IG1zIHRvIHNlY1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgIH1cbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9IGNhdGNoIChlcnI6IGFueSkge1xuICAgICAgaWYgKGVyci5jb2RlID09PSAnRU5PRU5UJykge1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRocm93IGVycjtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICBwdWJsaWMgYXN5bmMgdXBkYXRlKGxhdGVzdFZlcnNpb24/OiBzdHJpbmcpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICBpZiAoIWxhdGVzdFZlcnNpb24pIHtcbiAgICAgIGxhdGVzdFZlcnNpb24gPSAnJztcbiAgICB9XG4gICAgYXdhaXQgZnMud3JpdGVGaWxlKHRoaXMuZmlsZSwgbGF0ZXN0VmVyc2lvbik7XG4gIH1cbn1cblxuLy8gRXhwb3J0IGZvciB1bml0IHRlc3Rpbmcgb25seS5cbi8vIERvbid0IHVzZSBkaXJlY3RseSwgdXNlIGRpc3BsYXlWZXJzaW9uTWVzc2FnZSgpIGluc3RlYWQuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0VmVyc2lvbk1lc3NhZ2VzKGN1cnJlbnRWZXJzaW9uOiBzdHJpbmcsIGNhY2hlRmlsZTogVmVyc2lvbkNoZWNrVFRMKTogUHJvbWlzZTxzdHJpbmdbXT4ge1xuICBpZiAoIShhd2FpdCBjYWNoZUZpbGUuaGFzRXhwaXJlZCgpKSkge1xuICAgIHJldHVybiBbXTtcbiAgfVxuXG4gIGNvbnN0IHBhY2thZ2VJbmZvID0gYXdhaXQgZXhlY05wbVZpZXcoY3VycmVudFZlcnNpb24pO1xuICBjb25zdCBsYXRlc3RWZXJzaW9uID0gcGFja2FnZUluZm8ubGF0ZXN0VmVyc2lvbjtcbiAgYXdhaXQgY2FjaGVGaWxlLnVwZGF0ZShKU09OLnN0cmluZ2lmeShwYWNrYWdlSW5mbykpO1xuXG4gIC8vIElmIHRoZSBsYXRlc3QgdmVyc2lvbiBpcyB0aGUgc2FtZSBhcyB0aGUgY3VycmVudCB2ZXJzaW9uLCB0aGVyZSBpcyBubyBuZWVkIHRvIGRpc3BsYXkgYSBtZXNzYWdlXG4gIGlmIChzZW12ZXIuZXEobGF0ZXN0VmVyc2lvbiwgY3VycmVudFZlcnNpb24pKSB7XG4gICAgcmV0dXJuIFtdO1xuICB9XG5cbiAgY29uc3QgdmVyc2lvbk1lc3NhZ2UgPSBbXG4gICAgcGFja2FnZUluZm8uZGVwcmVjYXRlZCA/IGAke2NoYWxrLnJlZChwYWNrYWdlSW5mby5kZXByZWNhdGVkIGFzIHN0cmluZyl9YCA6IHVuZGVmaW5lZCxcbiAgICBgTmV3ZXIgdmVyc2lvbiBvZiBDREsgaXMgYXZhaWxhYmxlIFske2NoYWxrLmdyZWVuKGxhdGVzdFZlcnNpb24gYXMgc3RyaW5nKX1dYCxcbiAgICBnZXRNYWpvclZlcnNpb25VcGdyYWRlTWVzc2FnZShjdXJyZW50VmVyc2lvbiksXG4gICAgJ1VwZ3JhZGUgcmVjb21tZW5kZWQgKG5wbSBpbnN0YWxsIC1nIGF3cy1jZGspJyxcbiAgXS5maWx0ZXIoQm9vbGVhbikgYXMgc3RyaW5nW107XG5cbiAgcmV0dXJuIHZlcnNpb25NZXNzYWdlO1xufVxuXG5mdW5jdGlvbiBnZXRNYWpvclZlcnNpb25VcGdyYWRlTWVzc2FnZShjdXJyZW50VmVyc2lvbjogc3RyaW5nKTogc3RyaW5nIHwgdm9pZCB7XG4gIGNvbnN0IGN1cnJlbnRNYWpvclZlcnNpb24gPSBzZW12ZXIubWFqb3IoY3VycmVudFZlcnNpb24pO1xuICBpZiAoVVBHUkFERV9ET0NVTUVOVEFUSU9OX0xJTktTW2N1cnJlbnRNYWpvclZlcnNpb25dKSB7XG4gICAgcmV0dXJuIGBJbmZvcm1hdGlvbiBhYm91dCB1cGdyYWRpbmcgZnJvbSB2ZXJzaW9uICR7Y3VycmVudE1ham9yVmVyc2lvbn0ueCB0byB2ZXJzaW9uICR7Y3VycmVudE1ham9yVmVyc2lvbiArIDF9LnggaXMgYXZhaWxhYmxlIGhlcmU6ICR7VVBHUkFERV9ET0NVTUVOVEFUSU9OX0xJTktTW2N1cnJlbnRNYWpvclZlcnNpb25dfWA7XG4gIH1cbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGRpc3BsYXlWZXJzaW9uTWVzc2FnZShjdXJyZW50VmVyc2lvbiA9IHZlcnNpb25OdW1iZXIoKSwgdmVyc2lvbkNoZWNrQ2FjaGU/OiBWZXJzaW9uQ2hlY2tUVEwpOiBQcm9taXNlPHZvaWQ+IHtcbiAgaWYgKCFwcm9jZXNzLnN0ZG91dC5pc1RUWSB8fCBwcm9jZXNzLmVudi5DREtfRElTQUJMRV9WRVJTSU9OX0NIRUNLKSB7XG4gICAgcmV0dXJuO1xuICB9XG5cbiAgdHJ5IHtcbiAgICBjb25zdCB2ZXJzaW9uTWVzc2FnZXMgPSBhd2FpdCBnZXRWZXJzaW9uTWVzc2FnZXMoY3VycmVudFZlcnNpb24sIHZlcnNpb25DaGVja0NhY2hlID8/IG5ldyBWZXJzaW9uQ2hlY2tUVEwoKSk7XG4gICAgZm9ybWF0QXNCYW5uZXIodmVyc2lvbk1lc3NhZ2VzKS5mb3JFYWNoKGUgPT4gaW5mbyhlKSk7XG4gIH0gY2F0Y2ggKGVycjogYW55KSB7XG4gICAgZGVidWcoYENvdWxkIG5vdCBydW4gdmVyc2lvbiBjaGVjayAtICR7ZXJyLm1lc3NhZ2V9YCk7XG4gIH1cbn1cbi8qIGM4IGlnbm9yZSBzdG9wICovXG4iXX0=