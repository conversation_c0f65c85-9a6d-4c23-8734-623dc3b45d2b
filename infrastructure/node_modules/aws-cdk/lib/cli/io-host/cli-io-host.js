"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CliIoHost = void 0;
exports.isCI = isCI;
const util = require("node:util");
const cloud_assembly_schema_1 = require("@aws-cdk/cloud-assembly-schema");
const toolkit_lib_1 = require("@aws-cdk/toolkit-lib");
const chalk = require("chalk");
const promptly = require("promptly");
const api_private_1 = require("../../../lib/api-private");
const deploy_1 = require("../../commands/deploy");
/**
 * A simple IO host for the CLI that writes messages to the console.
 */
class CliIoHost {
    /**
     * Returns the singleton instance
     */
    static instance(props = {}, forceNew = false) {
        if (forceNew || !CliIoHost._instance) {
            CliIoHost._instance = new CliIoHost(props);
        }
        return CliIoHost._instance;
    }
    constructor(props = {}) {
        /**
         * Configure the target stream for notices
         *
         * (Not a setter because there's no need for additional logic when this value
         * is changed yet)
         */
        this.noticesDestination = 'stderr';
        this._progress = deploy_1.StackActivityProgress.BAR;
        // Corked Logging
        this.corkedCounter = 0;
        this.corkedLoggingBuffer = [];
        this.currentAction = props.currentAction ?? 'none';
        this.isTTY = props.isTTY ?? process.stdout.isTTY ?? false;
        this.logLevel = props.logLevel ?? 'info';
        this.isCI = props.isCI ?? isCI();
        this.requireDeployApproval = props.requireDeployApproval ?? cloud_assembly_schema_1.RequireApproval.BROADENING;
        this.stackProgress = props.stackProgress ?? deploy_1.StackActivityProgress.BAR;
    }
    /**
     * Returns the singleton instance
     */
    registerIoHost(ioHost) {
        if (ioHost !== this) {
            this._internalIoHost = ioHost;
        }
    }
    /**
     * Update the stackProgress preference.
     */
    set stackProgress(type) {
        this._progress = type;
    }
    /**
     * Gets the stackProgress value.
     *
     * This takes into account other state of the ioHost,
     * like if isTTY and isCI.
     */
    get stackProgress() {
        // We can always use EVENTS
        if (this._progress === deploy_1.StackActivityProgress.EVENTS) {
            return this._progress;
        }
        // if a debug message (and thus any more verbose messages) are relevant to the current log level, we have verbose logging
        const verboseLogging = (0, api_private_1.isMessageRelevantForLevel)({ level: 'debug' }, this.logLevel);
        if (verboseLogging) {
            return deploy_1.StackActivityProgress.EVENTS;
        }
        // On Windows we cannot use fancy output
        const isWindows = process.platform === 'win32';
        if (isWindows) {
            return deploy_1.StackActivityProgress.EVENTS;
        }
        // On some CI systems (such as CircleCI) output still reports as a TTY so we also
        // need an individual check for whether we're running on CI.
        // see: https://discuss.circleci.com/t/circleci-terminal-is-a-tty-but-term-is-not-set/9965
        const fancyOutputAvailable = this.isTTY && !this.isCI;
        if (!fancyOutputAvailable) {
            return deploy_1.StackActivityProgress.EVENTS;
        }
        // Use the user preference
        return this._progress;
    }
    get defaults() {
        return this.asIoHelper().defaults;
    }
    asIoHelper() {
        return (0, api_private_1.asIoHelper)(this, this.currentAction);
    }
    /**
     * Executes a block of code with corked logging. All log messages during execution
     * are buffered and only written when all nested cork blocks complete (when CORK_COUNTER reaches 0).
     * The corking is bound to the specific instance of the CliIoHost.
     *
     * @param block - Async function to execute with corked logging
     * @returns Promise that resolves with the block's return value
     */
    async withCorkedLogging(block) {
        this.corkedCounter++;
        try {
            return await block();
        }
        finally {
            this.corkedCounter--;
            if (this.corkedCounter === 0) {
                // Process each buffered message through notify
                for (const ioMessage of this.corkedLoggingBuffer) {
                    await this.notify(ioMessage);
                }
                // remove all buffered messages in-place
                this.corkedLoggingBuffer.splice(0);
            }
        }
    }
    /**
     * Notifies the host of a message.
     * The caller waits until the notification completes.
     */
    async notify(msg) {
        if (this._internalIoHost) {
            return this._internalIoHost.notify(msg);
        }
        if (this.isStackActivity(msg)) {
            if (!this.activityPrinter) {
                this.activityPrinter = this.makeActivityPrinter();
            }
            await this.activityPrinter.notify(msg);
            return;
        }
        if (!(0, api_private_1.isMessageRelevantForLevel)(msg, this.logLevel)) {
            return;
        }
        if (this.corkedCounter > 0) {
            this.corkedLoggingBuffer.push(msg);
            return;
        }
        const output = this.formatMessage(msg);
        const stream = this.selectStream(msg);
        stream?.write(output);
    }
    /**
     * Detect stack activity messages so they can be send to the printer.
     */
    isStackActivity(msg) {
        return msg.code && [
            'CDK_TOOLKIT_I5501',
            'CDK_TOOLKIT_I5502',
            'CDK_TOOLKIT_I5503',
        ].includes(msg.code);
    }
    /**
     * Detect special messages encode information about whether or not
     * they require approval
     */
    skipApprovalStep(msg) {
        const approvalToolkitCodes = ['CDK_TOOLKIT_I5060'];
        if (!(msg.code && approvalToolkitCodes.includes(msg.code))) {
            false;
        }
        switch (this.requireDeployApproval) {
            // Never require approval
            case cloud_assembly_schema_1.RequireApproval.NEVER:
                return true;
            // Always require approval
            case cloud_assembly_schema_1.RequireApproval.ANYCHANGE:
                return false;
            // Require approval if changes include broadening permissions
            case cloud_assembly_schema_1.RequireApproval.BROADENING:
                return ['none', 'non-broadening'].includes(msg.data?.permissionChangeType);
        }
    }
    /**
     * Determines the output stream, based on message and configuration.
     */
    selectStream(msg) {
        if (isNoticesMessage(msg)) {
            return targetStreamObject(this.noticesDestination);
        }
        return this.selectStreamFromLevel(msg.level);
    }
    /**
     * Determines the output stream, based on message level and configuration.
     */
    selectStreamFromLevel(level) {
        // The stream selection policy for the CLI is the following:
        //
        //   (1) Messages of level `result` always go to `stdout`
        //   (2) Messages of level `error` always go to `stderr`.
        //   (3a) All remaining messages go to `stderr`.
        //   (3b) If we are in CI mode, all remaining messages go to `stdout`.
        //
        switch (level) {
            case 'error':
                return process.stderr;
            case 'result':
                return process.stdout;
            default:
                return this.isCI ? process.stdout : process.stderr;
        }
    }
    /**
     * Notifies the host of a message that requires a response.
     *
     * If the host does not return a response the suggested
     * default response from the input message will be used.
     */
    async requestResponse(msg) {
        // First call out to a registered instance if we have one
        if (this._internalIoHost) {
            return this._internalIoHost.requestResponse(msg);
        }
        // If the request cannot be prompted for by the CliIoHost, we just accept the default
        if (!isPromptableRequest(msg)) {
            await this.notify(msg);
            return msg.defaultResponse;
        }
        const response = await this.withCorkedLogging(async () => {
            // prepare prompt data
            // @todo this format is not defined anywhere, probably should be
            const data = msg.data ?? {};
            const motivation = data.motivation ?? 'User input is needed';
            const concurrency = data.concurrency ?? 0;
            const responseDescription = data.responseDescription;
            // only talk to user if STDIN is a terminal (otherwise, fail)
            if (!this.isTTY) {
                throw new toolkit_lib_1.ToolkitError(`${motivation}, but terminal (TTY) is not attached so we are unable to get a confirmation from the user`);
            }
            // only talk to user if concurrency is 1 (otherwise, fail)
            if (concurrency > 1) {
                throw new toolkit_lib_1.ToolkitError(`${motivation}, but concurrency is greater than 1 so we are unable to get a confirmation from the user`);
            }
            // Special approval prompt
            // Determine if the message needs approval. If it does, continue (it is a basic confirmation prompt)
            // If it does not, return success (true). We only check messages with codes that we are aware
            // are requires approval codes.
            if (this.skipApprovalStep(msg)) {
                return true;
            }
            // Basic confirmation prompt
            // We treat all requests with a boolean response as confirmation prompts
            if (isConfirmationPrompt(msg)) {
                const confirmed = await promptly.confirm(`${chalk.cyan(msg.message)} (y/n)`);
                if (!confirmed) {
                    throw new toolkit_lib_1.ToolkitError('Aborted by user');
                }
                return confirmed;
            }
            // Asking for a specific value
            const prompt = extractPromptInfo(msg);
            const desc = responseDescription ?? prompt.default;
            const answer = await promptly.prompt(`${chalk.cyan(msg.message)}${desc ? ` (${desc})` : ''}`, {
                default: prompt.default,
                trim: true,
            });
            return prompt.convertAnswer(answer);
        });
        // We need to cast this because it is impossible to narrow the generic type
        // isPromptableRequest ensures that the response type is one we can prompt for
        // the remaining code ensure we are indeed returning the correct type
        return response;
    }
    /**
     * Formats a message for console output with optional color support
     */
    formatMessage(msg) {
        // apply provided style or a default style if we're in TTY mode
        let message_text = this.isTTY
            ? styleMap[msg.level](msg.message)
            : msg.message;
        // prepend timestamp if IoMessageLevel is DEBUG or TRACE. Postpend a newline.
        return ((msg.level === 'debug' || msg.level === 'trace')
            ? `[${this.formatTime(msg.time)}] ${message_text}`
            : message_text) + '\n';
    }
    /**
     * Formats date to HH:MM:SS
     */
    formatTime(d) {
        const pad = (n) => n.toString().padStart(2, '0');
        return `${pad(d.getHours())}:${pad(d.getMinutes())}:${pad(d.getSeconds())}`;
    }
    /**
     * Get an instance of the ActivityPrinter
     */
    makeActivityPrinter() {
        const props = {
            stream: this.selectStreamFromLevel('info'),
        };
        switch (this.stackProgress) {
            case deploy_1.StackActivityProgress.EVENTS:
                return new api_private_1.HistoryActivityPrinter(props);
            case deploy_1.StackActivityProgress.BAR:
                return new api_private_1.CurrentActivityPrinter(props);
        }
    }
}
exports.CliIoHost = CliIoHost;
/**
 * This IoHost implementation considers a request promptable, if:
 * - it's a yes/no confirmation
 * - asking for a string or number value
 */
function isPromptableRequest(msg) {
    return isConfirmationPrompt(msg)
        || typeof msg.defaultResponse === 'string'
        || typeof msg.defaultResponse === 'number';
}
/**
 * Check if the request is a confirmation prompt
 * We treat all requests with a boolean response as confirmation prompts
 */
function isConfirmationPrompt(msg) {
    return typeof msg.defaultResponse === 'boolean';
}
/**
 * Helper to extract information for promptly from the request
 */
function extractPromptInfo(msg) {
    const isNumber = (typeof msg.defaultResponse === 'number');
    const defaultResponse = util.format(msg.defaultResponse);
    return {
        default: defaultResponse,
        defaultDesc: 'defaultDescription' in msg && msg.defaultDescription ? util.format(msg.defaultDescription) : defaultResponse,
        convertAnswer: isNumber ? (v) => Number(v) : (v) => String(v),
    };
}
const styleMap = {
    error: chalk.red,
    warn: chalk.yellow,
    result: chalk.white,
    info: chalk.white,
    debug: chalk.gray,
    trace: chalk.gray,
};
/**
 * Returns true if the current process is running in a CI environment
 * @returns true if the current process is running in a CI environment
 */
function isCI() {
    return process.env.CI !== undefined && process.env.CI !== 'false' && process.env.CI !== '0';
}
function targetStreamObject(x) {
    switch (x) {
        case 'stderr':
            return process.stderr;
        case 'stdout':
            return process.stdout;
        case 'drop':
            return undefined;
    }
}
function isNoticesMessage(msg) {
    return api_private_1.IO.CDK_TOOLKIT_I0100.is(msg) || api_private_1.IO.CDK_TOOLKIT_W0101.is(msg) || api_private_1.IO.CDK_TOOLKIT_E0101.is(msg) || api_private_1.IO.CDK_TOOLKIT_I0101.is(msg);
}
//# sourceMappingURL=data:application/json;base64,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