import { RequireApproval } from '@aws-cdk/cloud-assembly-schema';
import type { IIoHost, IoMessage, IoMessageCode, IoMessageLevel, IoRequest, ToolkitAction } from '@aws-cdk/toolkit-lib';
import type { I<PERSON><PERSON><PERSON><PERSON> } from '../../../lib/api-private';
import { StackActivityProgress } from '../../commands/deploy';
export type { IIoHost, IoMessage, IoMessageCode, IoMessageLevel, IoRequest };
/**
 * The current action being performed by the CLI. 'none' represents the absence of an action.
 */
type CliAction = ToolkitAction | 'context' | 'docs' | 'notices' | 'version' | 'cli-telemetry' | 'none';
export interface CliIoHostProps {
    /**
     * The initial Toolkit action the hosts starts with.
     *
     * @default 'none'
     */
    readonly currentAction?: CliAction;
    /**
     * Determines the verbosity of the output.
     *
     * The CliIoHost will still receive all messages and requests,
     * but only the messages included in this level will be printed.
     *
     * @default 'info'
     */
    readonly logLevel?: IoMessageLevel;
    /**
     * Overrides the automatic TTY detection.
     *
     * When TTY is disabled, the CLI will have no interactions or color.
     *
     * @default - determined from the current process
     */
    readonly isTTY?: boolean;
    /**
     * Whether the CliIoHost is running in CI mode.
     *
     * In CI mode, all non-error output goes to stdout instead of stderr.
     * Set to false in the CliIoHost constructor it will be overwritten if the CLI CI argument is passed
     *
     * @default - determined from the environment, specifically based on `process.env.CI`
     */
    readonly isCI?: boolean;
    /**
     * In what scenarios should the CliIoHost ask for approval
     *
     * @default RequireApproval.BROADENING
     */
    readonly requireDeployApproval?: RequireApproval;
    /**
     * The initial Toolkit action the hosts starts with.
     *
     * @default StackActivityProgress.BAR
     */
    readonly stackProgress?: StackActivityProgress;
}
/**
 * A type for configuring a target stream
 */
export type TargetStream = 'stdout' | 'stderr' | 'drop';
/**
 * A simple IO host for the CLI that writes messages to the console.
 */
export declare class CliIoHost implements IIoHost {
    /**
     * Returns the singleton instance
     */
    static instance(props?: CliIoHostProps, forceNew?: boolean): CliIoHost;
    /**
     * Singleton instance of the CliIoHost
     */
    private static _instance;
    /**
     * The current action being performed by the CLI.
     */
    currentAction: CliAction;
    /**
     * Whether the CliIoHost is running in CI mode.
     *
     * In CI mode, all non-error output goes to stdout instead of stderr.
     */
    isCI: boolean;
    /**
     * Whether the host can use interactions and message styling.
     */
    isTTY: boolean;
    /**
     * The current threshold.
     *
     * Messages with a lower priority level will be ignored.
     */
    logLevel: IoMessageLevel;
    /**
     * The conditions for requiring approval in this CliIoHost.
     */
    requireDeployApproval: RequireApproval;
    /**
     * Configure the target stream for notices
     *
     * (Not a setter because there's no need for additional logic when this value
     * is changed yet)
     */
    noticesDestination: TargetStream;
    private _internalIoHost?;
    private _progress;
    private activityPrinter?;
    private corkedCounter;
    private readonly corkedLoggingBuffer;
    private constructor();
    /**
     * Returns the singleton instance
     */
    registerIoHost(ioHost: IIoHost): void;
    /**
     * Update the stackProgress preference.
     */
    set stackProgress(type: StackActivityProgress);
    /**
     * Gets the stackProgress value.
     *
     * This takes into account other state of the ioHost,
     * like if isTTY and isCI.
     */
    get stackProgress(): StackActivityProgress;
    get defaults(): import("@aws-cdk/toolkit-lib/lib/api/io/private/io-default-messages").IoDefaultMessages;
    asIoHelper(): IoHelper;
    /**
     * Executes a block of code with corked logging. All log messages during execution
     * are buffered and only written when all nested cork blocks complete (when CORK_COUNTER reaches 0).
     * The corking is bound to the specific instance of the CliIoHost.
     *
     * @param block - Async function to execute with corked logging
     * @returns Promise that resolves with the block's return value
     */
    withCorkedLogging<T>(block: () => Promise<T>): Promise<T>;
    /**
     * Notifies the host of a message.
     * The caller waits until the notification completes.
     */
    notify(msg: IoMessage<unknown>): Promise<void>;
    /**
     * Detect stack activity messages so they can be send to the printer.
     */
    private isStackActivity;
    /**
     * Detect special messages encode information about whether or not
     * they require approval
     */
    private skipApprovalStep;
    /**
     * Determines the output stream, based on message and configuration.
     */
    private selectStream;
    /**
     * Determines the output stream, based on message level and configuration.
     */
    private selectStreamFromLevel;
    /**
     * Notifies the host of a message that requires a response.
     *
     * If the host does not return a response the suggested
     * default response from the input message will be used.
     */
    requestResponse<DataType, ResponseType>(msg: IoRequest<DataType, ResponseType>): Promise<ResponseType>;
    /**
     * Formats a message for console output with optional color support
     */
    private formatMessage;
    /**
     * Formats date to HH:MM:SS
     */
    private formatTime;
    /**
     * Get an instance of the ActivityPrinter
     */
    private makeActivityPrinter;
}
/**
 * Returns true if the current process is running in a CI environment
 * @returns true if the current process is running in a CI environment
 */
export declare function isCI(): boolean;
