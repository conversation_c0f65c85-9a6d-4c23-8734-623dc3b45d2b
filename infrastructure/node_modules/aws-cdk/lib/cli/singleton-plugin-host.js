"use strict";
/**
 * The singleton plugin host
 *
 * This is only a concept in the CLI, not in the toolkit library.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.GLOBAL_PLUGIN_HOST = void 0;
const plugin_1 = require("../api/plugin");
exports.GLOBAL_PLUGIN_HOST = new plugin_1.PluginHost();
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic2luZ2xldG9uLXBsdWdpbi1ob3N0LmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsic2luZ2xldG9uLXBsdWdpbi1ob3N0LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFBQTs7OztHQUlHOzs7QUFFSCwwQ0FBMkM7QUFFOUIsUUFBQSxrQkFBa0IsR0FBRyxJQUFJLG1CQUFVLEVBQUUsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogVGhlIHNpbmdsZXRvbiBwbHVnaW4gaG9zdFxuICpcbiAqIFRoaXMgaXMgb25seSBhIGNvbmNlcHQgaW4gdGhlIENMSSwgbm90IGluIHRoZSB0b29sa2l0IGxpYnJhcnkuXG4gKi9cblxuaW1wb3J0IHsgUGx1Z2luSG9zdCB9IGZyb20gJy4uL2FwaS9wbHVnaW4nO1xuXG5leHBvcnQgY29uc3QgR0xPQkFMX1BMVUdJTl9IT1NUID0gbmV3IFBsdWdpbkhvc3QoKTtcbiJdfQ==