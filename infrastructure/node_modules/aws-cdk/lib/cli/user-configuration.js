"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Configuration = exports.Command = exports.USER_DEFAULTS = exports.PROJECT_CONTEXT = exports.PROJECT_CONFIG = void 0;
exports.commandLineArgumentsToSettings = commandLineArgumentsToSettings;
const os = require("os");
const fs_path = require("path");
const toolkit_lib_1 = require("@aws-cdk/toolkit-lib");
const fs = require("fs-extra");
const context_1 = require("../api/context");
const settings_1 = require("../api/settings");
const logging_1 = require("../logging");
exports.PROJECT_CONFIG = 'cdk.json';
var context_2 = require("../api/context");
Object.defineProperty(exports, "PROJECT_CONTEXT", { enumerable: true, get: function () { return context_2.PROJECT_CONTEXT; } });
exports.USER_DEFAULTS = '~/.cdk.json';
const CONTEXT_KEY = 'context';
var Command;
(function (Command) {
    Command["LS"] = "ls";
    Command["LIST"] = "list";
    Command["DIFF"] = "diff";
    Command["BOOTSTRAP"] = "bootstrap";
    Command["DEPLOY"] = "deploy";
    Command["DESTROY"] = "destroy";
    Command["SYNTHESIZE"] = "synthesize";
    Command["SYNTH"] = "synth";
    Command["METADATA"] = "metadata";
    Command["INIT"] = "init";
    Command["VERSION"] = "version";
    Command["WATCH"] = "watch";
    Command["GC"] = "gc";
    Command["ROLLBACK"] = "rollback";
    Command["IMPORT"] = "import";
    Command["ACKNOWLEDGE"] = "acknowledge";
    Command["ACK"] = "ack";
    Command["NOTICES"] = "notices";
    Command["MIGRATE"] = "migrate";
    Command["CONTEXT"] = "context";
    Command["DOCS"] = "docs";
    Command["DOC"] = "doc";
    Command["DOCTOR"] = "doctor";
    Command["REFACTOR"] = "refactor";
    Command["DRIFT"] = "drift";
    Command["CLI_TELEMETRY"] = "cli-telemetry";
})(Command || (exports.Command = Command = {}));
const BUNDLING_COMMANDS = [
    Command.DEPLOY,
    Command.DIFF,
    Command.SYNTH,
    Command.SYNTHESIZE,
    Command.WATCH,
    Command.IMPORT,
];
/**
 * All sources of settings combined
 */
class Configuration {
    constructor(props = {}) {
        this.props = props;
        this.settings = new settings_1.Settings();
        this.context = new context_1.Context();
        this.defaultConfig = new settings_1.Settings({
            versionReporting: true,
            assetMetadata: true,
            pathMetadata: true,
            output: 'cdk.out',
        });
        this.loaded = false;
        this.commandLineArguments = props.commandLineArguments
            ? commandLineArgumentsToSettings(props.commandLineArguments)
            : new settings_1.Settings();
        this.commandLineContext = this.commandLineArguments
            .subSettings([CONTEXT_KEY])
            .makeReadOnly();
    }
    get projectConfig() {
        if (!this._projectConfig) {
            throw new toolkit_lib_1.ToolkitError('#load has not been called yet!');
        }
        return this._projectConfig;
    }
    get projectContext() {
        if (!this._projectContext) {
            throw new toolkit_lib_1.ToolkitError('#load has not been called yet!');
        }
        return this._projectContext;
    }
    /**
     * Load all config
     */
    async load() {
        const userConfig = await loadAndLog(exports.USER_DEFAULTS);
        this._projectConfig = await loadAndLog(exports.PROJECT_CONFIG);
        this._projectContext = await loadAndLog(context_1.PROJECT_CONTEXT);
        // @todo cannot currently be disabled by cli users
        const readUserContext = this.props.readUserContext ?? true;
        if (userConfig.get(['build'])) {
            throw new toolkit_lib_1.ToolkitError('The `build` key cannot be specified in the user config (~/.cdk.json), specify it in the project config (cdk.json) instead');
        }
        const contextSources = [
            { bag: this.commandLineContext },
            {
                fileName: exports.PROJECT_CONFIG,
                bag: this.projectConfig.subSettings([CONTEXT_KEY]).makeReadOnly(),
            },
            { fileName: context_1.PROJECT_CONTEXT, bag: this.projectContext },
        ];
        if (readUserContext) {
            contextSources.push({
                fileName: exports.USER_DEFAULTS,
                bag: userConfig.subSettings([CONTEXT_KEY]).makeReadOnly(),
            });
        }
        this.context = new context_1.Context(...contextSources);
        // Build settings from what's left
        this.settings = this.defaultConfig
            .merge(userConfig)
            .merge(this.projectConfig)
            .merge(this.commandLineArguments)
            .makeReadOnly();
        (0, logging_1.debug)('merged settings:', this.settings.all);
        this.loaded = true;
        return this;
    }
    /**
     * Save the project context
     */
    async saveContext() {
        if (!this.loaded) {
            return this;
        } // Avoid overwriting files with nothing
        await this.projectContext.save(context_1.PROJECT_CONTEXT);
        return this;
    }
}
exports.Configuration = Configuration;
async function loadAndLog(fileName) {
    const ret = await settingsFromFile(fileName);
    if (!ret.empty) {
        (0, logging_1.debug)(fileName + ':', JSON.stringify(ret.all, undefined, 2));
    }
    return ret;
}
async function settingsFromFile(fileName) {
    let settings;
    const expanded = expandHomeDir(fileName);
    if (await fs.pathExists(expanded)) {
        const data = await fs.readJson(expanded);
        settings = new settings_1.Settings(data);
    }
    else {
        settings = new settings_1.Settings();
    }
    // See https://github.com/aws/aws-cdk/issues/59
    prohibitContextKeys(settings, ['default-account', 'default-region'], fileName);
    warnAboutContextKey(settings, 'aws:', fileName);
    return settings;
}
function prohibitContextKeys(settings, keys, fileName) {
    const context = settings.get(['context']);
    if (!context || typeof context !== 'object') {
        return;
    }
    for (const key of keys) {
        if (key in context) {
            throw new toolkit_lib_1.ToolkitError(`The 'context.${key}' key was found in ${fs_path.resolve(fileName)}, but it is no longer supported. Please remove it.`);
        }
    }
}
function warnAboutContextKey(settings, prefix, fileName) {
    const context = settings.get(['context']);
    if (!context || typeof context !== 'object') {
        return;
    }
    for (const contextKey of Object.keys(context)) {
        if (contextKey.startsWith(prefix)) {
            (0, logging_1.warning)(`A reserved context key ('context.${prefix}') key was found in ${fs_path.resolve(fileName)}, it might cause surprising behavior and should be removed.`);
        }
    }
}
function expandHomeDir(x) {
    if (x.startsWith('~')) {
        return fs_path.join(os.homedir(), x.slice(1));
    }
    return x;
}
/**
 * Parse CLI arguments into Settings
 *
 * CLI arguments in must be accessed in the CLI code via
 * `configuration.settings.get(['argName'])` instead of via `args.argName`.
 *
 * The advantage is that they can be configured via `cdk.json` and
 * `$HOME/.cdk.json`. Arguments not listed below and accessed via this object
 * can only be specified on the command line.
 *
 * @param argv - the received CLI arguments.
 * @returns a new Settings object.
 */
function commandLineArgumentsToSettings(argv) {
    const context = parseStringContextListToObject(argv);
    const tags = parseStringTagsListToObject(expectStringList(argv.tags));
    // Determine bundling stacks
    let bundlingStacks;
    if (BUNDLING_COMMANDS.includes(argv._[0])) {
        // If we deploy, diff, synth or watch a list of stacks exclusively we skip
        // bundling for all other stacks.
        bundlingStacks = argv.exclusively ? argv.STACKS ?? ['**'] : ['**'];
    }
    else {
        // Skip bundling for all stacks
        bundlingStacks = [];
    }
    return new settings_1.Settings({
        app: argv.app,
        browser: argv.browser,
        build: argv.build,
        caBundlePath: argv.caBundlePath,
        context,
        debug: argv.debug,
        tags,
        language: argv.language,
        pathMetadata: argv.pathMetadata,
        assetMetadata: argv.assetMetadata,
        profile: argv.profile,
        plugin: argv.plugin,
        requireApproval: argv.requireApproval,
        toolkitStackName: argv.toolkitStackName,
        toolkitBucket: {
            bucketName: argv.bootstrapBucketName,
            kmsKeyId: argv.bootstrapKmsKeyId,
        },
        versionReporting: argv.versionReporting,
        staging: argv.staging,
        output: argv.output,
        outputsFile: argv.outputsFile,
        progress: argv.progress,
        proxy: argv.proxy,
        bundlingStacks,
        lookups: argv.lookups,
        rollback: argv.rollback,
        notices: argv.notices,
        assetParallelism: argv['asset-parallelism'],
        assetPrebuild: argv['asset-prebuild'],
        ignoreNoStacks: argv['ignore-no-stacks'],
        hotswap: {
            ecs: {
                minimumHealthyPercent: argv.hotswapEcsMinimumHealthyPercent,
                maximumHealthyPercent: argv.hotswapEcsMaximumHealthyPercent,
                stabilizationTimeoutSeconds: argv.hotswapEcsStabilizationTimeoutSeconds,
            },
        },
        unstable: argv.unstable,
    });
}
function expectStringList(x) {
    if (x === undefined) {
        return undefined;
    }
    if (!Array.isArray(x)) {
        throw new toolkit_lib_1.ToolkitError(`Expected array, got '${x}'`);
    }
    const nonStrings = x.filter((e) => typeof e !== 'string');
    if (nonStrings.length > 0) {
        throw new toolkit_lib_1.ToolkitError(`Expected list of strings, found ${nonStrings}`);
    }
    return x;
}
function parseStringContextListToObject(argv) {
    const context = {};
    for (const assignment of argv.context || []) {
        const parts = assignment.split(/=(.*)/, 2);
        if (parts.length === 2) {
            (0, logging_1.debug)('CLI argument context: %s=%s', parts[0], parts[1]);
            if (parts[0].match(/^aws:.+/)) {
                throw new toolkit_lib_1.ToolkitError(`User-provided context cannot use keys prefixed with 'aws:', but ${parts[0]} was provided.`);
            }
            context[parts[0]] = parts[1];
        }
        else {
            (0, logging_1.warning)('Context argument is not an assignment (key=value): %s', assignment);
        }
    }
    return context;
}
/**
 * Parse tags out of arguments
 *
 * Return undefined if no tags were provided, return an empty array if only empty
 * strings were provided
 */
function parseStringTagsListToObject(argTags) {
    if (argTags === undefined) {
        return undefined;
    }
    if (argTags.length === 0) {
        return undefined;
    }
    const nonEmptyTags = argTags.filter((t) => t !== '');
    if (nonEmptyTags.length === 0) {
        return [];
    }
    const tags = [];
    for (const assignment of nonEmptyTags) {
        const parts = assignment.split(/=(.*)/, 2);
        if (parts.length === 2) {
            (0, logging_1.debug)('CLI argument tags: %s=%s', parts[0], parts[1]);
            tags.push({
                Key: parts[0],
                Value: parts[1],
            });
        }
        else {
            (0, logging_1.warning)('Tags argument is not an assignment (key=value): %s', assignment);
        }
    }
    return tags.length > 0 ? tags : undefined;
}
//# sourceMappingURL=data:application/json;base64,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