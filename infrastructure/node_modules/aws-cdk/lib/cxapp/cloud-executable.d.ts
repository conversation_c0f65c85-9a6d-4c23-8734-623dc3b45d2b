import type * as cxapi from '@aws-cdk/cx-api';
import { CloudAssembly } from './cloud-assembly';
import type { ICloudAssemblySource, IReadableCloudAssembly } from '../../lib/api';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../lib/api-private';
import type { SdkProvider } from '../api/aws-auth';
import type { Configuration } from '../cli/user-configuration';
/**
 * @returns output directory
 */
export type Synthesizer = (aws: SdkProvider, config: Configuration) => Promise<cxapi.CloudAssembly>;
export interface CloudExecutableProps {
    /**
     * Application configuration (settings and context)
     */
    configuration: Configuration;
    /**
     * AWS object (used by synthesizer and contextprovider)
     */
    sdkProvider: SdkProvider;
    /**
     * Messaging helper
     */
    ioHelper: IoHelper;
    /**
     * Callback invoked to synthesize the actual stacks
     */
    synthesizer: Synthesizer;
}
/**
 * Represent the Cloud Executable and the synthesis we can do on it
 */
export declare class CloudExecutable implements ICloudAssemblySource {
    private readonly props;
    private _cloudAssembly?;
    constructor(props: CloudExecutableProps);
    produce(): Promise<IReadableCloudAssembly>;
    /**
     * Return whether there is an app command from the configuration
     */
    get hasApp(): boolean;
    /**
     * Synthesize a set of stacks.
     *
     * @param cacheCloudAssembly - whether to cache the Cloud Assembly after it has been first synthesized.
     *   This is 'true' by default, and only set to 'false' for 'cdk watch',
     *   which needs to re-synthesize the Assembly each time it detects a change to the project files
     */
    synthesize(cacheCloudAssembly?: boolean): Promise<CloudAssembly>;
    private doSynthesize;
    private get canLookup();
}
