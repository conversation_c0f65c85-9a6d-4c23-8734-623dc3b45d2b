import * as cdk from "../../core";
import * as constructs from "constructs";
import * as cfn_parse from "../../core/lib/helpers-internal";
/**
 * Specifies an Amazon Connect Wisdom assistant.
 *
 * @cloudformationResource AWS::Wisdom::Assistant
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-assistant.html
 */
export declare class CfnAssistant extends cdk.CfnResource implements cdk.IInspectable, cdk.ITaggable {
    /**
     * The CloudFormation resource type name for this resource class.
     */
    static readonly CFN_RESOURCE_TYPE_NAME: string;
    /**
     * Build a CfnAssistant from CloudFormation properties
     *
     * A factory method that creates a new instance of this class from an object
     * containing the CloudFormation properties of this resource.
     * Used in the @aws-cdk/cloudformation-include module.
     *
     * @internal
     */
    static _fromCloudFormation(scope: constructs.Construct, id: string, resourceAttributes: any, options: cfn_parse.FromCloudFormationOptions): CfnAssistant;
    /**
     * The Amazon Resource Name (ARN) of the assistant.
     *
     * @cloudformationAttribute AssistantArn
     */
    readonly attrAssistantArn: string;
    /**
     * The ID of the Wisdom assistant.
     *
     * @cloudformationAttribute AssistantId
     */
    readonly attrAssistantId: string;
    /**
     * The description of the assistant.
     */
    description?: string;
    /**
     * The name of the assistant.
     */
    name: string;
    /**
     * The configuration information for the customer managed key used for encryption.
     */
    serverSideEncryptionConfiguration?: cdk.IResolvable | CfnAssistant.ServerSideEncryptionConfigurationProperty;
    /**
     * Tag Manager which manages the tags for this resource
     */
    readonly tags: cdk.TagManager;
    /**
     * The tags used to organize, track, or control access for this resource.
     */
    tagsRaw?: Array<cdk.CfnTag>;
    /**
     * The type of assistant.
     */
    type: string;
    /**
     * @param scope Scope in which this resource is defined
     * @param id Construct identifier for this resource (unique in its scope)
     * @param props Resource properties
     */
    constructor(scope: constructs.Construct, id: string, props: CfnAssistantProps);
    protected get cfnProperties(): Record<string, any>;
    /**
     * Examines the CloudFormation resource and discloses attributes
     *
     * @param inspector tree inspector to collect and process attributes
     */
    inspect(inspector: cdk.TreeInspector): void;
    protected renderProperties(props: Record<string, any>): Record<string, any>;
}
export declare namespace CfnAssistant {
    /**
     * The configuration information for the customer managed key used for encryption.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-assistant-serversideencryptionconfiguration.html
     */
    interface ServerSideEncryptionConfigurationProperty {
        /**
         * The customer managed key used for encryption.
         *
         * The customer managed key must have a policy that allows `kms:CreateGrant` and `kms:DescribeKey` permissions to the IAM identity using the key to invoke Wisdom. To use Wisdom with chat, the key policy must also allow `kms:Decrypt` , `kms:GenerateDataKey*` , and `kms:DescribeKey` permissions to the `connect.amazonaws.com` service principal. For more information about setting up a customer managed key for Wisdom, see [Enable Amazon Connect Wisdom for your instance](https://docs.aws.amazon.com/connect/latest/adminguide/enable-wisdom.html) . For information about valid ID values, see [Key identifiers (KeyId)](https://docs.aws.amazon.com/kms/latest/developerguide/concepts.html#key-id) in the *AWS Key Management Service Developer Guide* .
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-assistant-serversideencryptionconfiguration.html#cfn-wisdom-assistant-serversideencryptionconfiguration-kmskeyid
         */
        readonly kmsKeyId?: string;
    }
}
/**
 * Properties for defining a `CfnAssistant`
 *
 * @struct
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-assistant.html
 */
export interface CfnAssistantProps {
    /**
     * The description of the assistant.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-assistant.html#cfn-wisdom-assistant-description
     */
    readonly description?: string;
    /**
     * The name of the assistant.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-assistant.html#cfn-wisdom-assistant-name
     */
    readonly name: string;
    /**
     * The configuration information for the customer managed key used for encryption.
     *
     * The customer managed key must have a policy that allows `kms:CreateGrant` and `kms:DescribeKey` permissions to the IAM identity using the key to invoke Wisdom. To use Wisdom with chat, the key policy must also allow `kms:Decrypt` , `kms:GenerateDataKey*` , and `kms:DescribeKey` permissions to the `connect.amazonaws.com` service principal. For more information about setting up a customer managed key for Wisdom, see [Enable Amazon Connect Wisdom for your instance](https://docs.aws.amazon.com/connect/latest/adminguide/enable-wisdom.html) .
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-assistant.html#cfn-wisdom-assistant-serversideencryptionconfiguration
     */
    readonly serverSideEncryptionConfiguration?: cdk.IResolvable | CfnAssistant.ServerSideEncryptionConfigurationProperty;
    /**
     * The tags used to organize, track, or control access for this resource.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-assistant.html#cfn-wisdom-assistant-tags
     */
    readonly tags?: Array<cdk.CfnTag>;
    /**
     * The type of assistant.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-assistant.html#cfn-wisdom-assistant-type
     */
    readonly type: string;
}
/**
 * Specifies an association between an Amazon Connect Wisdom assistant and another resource.
 *
 * Currently, the only supported association is with a knowledge base. An assistant can have only a single association.
 *
 * @cloudformationResource AWS::Wisdom::AssistantAssociation
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-assistantassociation.html
 */
export declare class CfnAssistantAssociation extends cdk.CfnResource implements cdk.IInspectable, cdk.ITaggable {
    /**
     * The CloudFormation resource type name for this resource class.
     */
    static readonly CFN_RESOURCE_TYPE_NAME: string;
    /**
     * Build a CfnAssistantAssociation from CloudFormation properties
     *
     * A factory method that creates a new instance of this class from an object
     * containing the CloudFormation properties of this resource.
     * Used in the @aws-cdk/cloudformation-include module.
     *
     * @internal
     */
    static _fromCloudFormation(scope: constructs.Construct, id: string, resourceAttributes: any, options: cfn_parse.FromCloudFormationOptions): CfnAssistantAssociation;
    /**
     * The Amazon Resource Name (ARN) of the Wisdom assistant.
     *
     * @cloudformationAttribute AssistantArn
     */
    readonly attrAssistantArn: string;
    /**
     * The Amazon Resource Name (ARN) of the assistant association.
     *
     * @cloudformationAttribute AssistantAssociationArn
     */
    readonly attrAssistantAssociationArn: string;
    /**
     * The ID of the association.
     *
     * @cloudformationAttribute AssistantAssociationId
     */
    readonly attrAssistantAssociationId: string;
    /**
     * The identifier of the Wisdom assistant.
     */
    assistantId: string;
    /**
     * The identifier of the associated resource.
     */
    association: CfnAssistantAssociation.AssociationDataProperty | cdk.IResolvable;
    /**
     * The type of association.
     */
    associationType: string;
    /**
     * Tag Manager which manages the tags for this resource
     */
    readonly tags: cdk.TagManager;
    /**
     * The tags used to organize, track, or control access for this resource.
     */
    tagsRaw?: Array<cdk.CfnTag>;
    /**
     * @param scope Scope in which this resource is defined
     * @param id Construct identifier for this resource (unique in its scope)
     * @param props Resource properties
     */
    constructor(scope: constructs.Construct, id: string, props: CfnAssistantAssociationProps);
    protected get cfnProperties(): Record<string, any>;
    /**
     * Examines the CloudFormation resource and discloses attributes
     *
     * @param inspector tree inspector to collect and process attributes
     */
    inspect(inspector: cdk.TreeInspector): void;
    protected renderProperties(props: Record<string, any>): Record<string, any>;
}
export declare namespace CfnAssistantAssociation {
    /**
     * A union type that currently has a single argument, which is the knowledge base ID.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-assistantassociation-associationdata.html
     */
    interface AssociationDataProperty {
        /**
         * The identifier of the knowledge base.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-assistantassociation-associationdata.html#cfn-wisdom-assistantassociation-associationdata-knowledgebaseid
         */
        readonly knowledgeBaseId: string;
    }
}
/**
 * Properties for defining a `CfnAssistantAssociation`
 *
 * @struct
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-assistantassociation.html
 */
export interface CfnAssistantAssociationProps {
    /**
     * The identifier of the Wisdom assistant.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-assistantassociation.html#cfn-wisdom-assistantassociation-assistantid
     */
    readonly assistantId: string;
    /**
     * The identifier of the associated resource.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-assistantassociation.html#cfn-wisdom-assistantassociation-association
     */
    readonly association: CfnAssistantAssociation.AssociationDataProperty | cdk.IResolvable;
    /**
     * The type of association.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-assistantassociation.html#cfn-wisdom-assistantassociation-associationtype
     */
    readonly associationType: string;
    /**
     * The tags used to organize, track, or control access for this resource.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-assistantassociation.html#cfn-wisdom-assistantassociation-tags
     */
    readonly tags?: Array<cdk.CfnTag>;
}
/**
 * Specifies a knowledge base.
 *
 * @cloudformationResource AWS::Wisdom::KnowledgeBase
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-knowledgebase.html
 */
export declare class CfnKnowledgeBase extends cdk.CfnResource implements cdk.IInspectable, cdk.ITaggable {
    /**
     * The CloudFormation resource type name for this resource class.
     */
    static readonly CFN_RESOURCE_TYPE_NAME: string;
    /**
     * Build a CfnKnowledgeBase from CloudFormation properties
     *
     * A factory method that creates a new instance of this class from an object
     * containing the CloudFormation properties of this resource.
     * Used in the @aws-cdk/cloudformation-include module.
     *
     * @internal
     */
    static _fromCloudFormation(scope: constructs.Construct, id: string, resourceAttributes: any, options: cfn_parse.FromCloudFormationOptions): CfnKnowledgeBase;
    /**
     * The Amazon Resource Name (ARN) of the knowledge base.
     *
     * @cloudformationAttribute KnowledgeBaseArn
     */
    readonly attrKnowledgeBaseArn: string;
    /**
     * The ID of the knowledge base.
     *
     * @cloudformationAttribute KnowledgeBaseId
     */
    readonly attrKnowledgeBaseId: string;
    /**
     * The description.
     */
    description?: string;
    /**
     * The type of knowledge base.
     */
    knowledgeBaseType: string;
    /**
     * The name of the knowledge base.
     */
    name: string;
    /**
     * Information about how to render the content.
     */
    renderingConfiguration?: cdk.IResolvable | CfnKnowledgeBase.RenderingConfigurationProperty;
    /**
     * This customer managed key must have a policy that allows `kms:CreateGrant` and `kms:DescribeKey` permissions to the IAM identity using the key to invoke Wisdom.
     */
    serverSideEncryptionConfiguration?: cdk.IResolvable | CfnKnowledgeBase.ServerSideEncryptionConfigurationProperty;
    /**
     * The source of the knowledge base content.
     */
    sourceConfiguration?: cdk.IResolvable | CfnKnowledgeBase.SourceConfigurationProperty;
    /**
     * Tag Manager which manages the tags for this resource
     */
    readonly tags: cdk.TagManager;
    /**
     * The tags used to organize, track, or control access for this resource.
     */
    tagsRaw?: Array<cdk.CfnTag>;
    /**
     * Contains details about how to ingest the documents in a data source.
     */
    vectorIngestionConfiguration?: cdk.IResolvable | CfnKnowledgeBase.VectorIngestionConfigurationProperty;
    /**
     * @param scope Scope in which this resource is defined
     * @param id Construct identifier for this resource (unique in its scope)
     * @param props Resource properties
     */
    constructor(scope: constructs.Construct, id: string, props: CfnKnowledgeBaseProps);
    protected get cfnProperties(): Record<string, any>;
    /**
     * Examines the CloudFormation resource and discloses attributes
     *
     * @param inspector tree inspector to collect and process attributes
     */
    inspect(inspector: cdk.TreeInspector): void;
    protected renderProperties(props: Record<string, any>): Record<string, any>;
}
export declare namespace CfnKnowledgeBase {
    /**
     * Configuration information about the external data source.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-sourceconfiguration.html
     */
    interface SourceConfigurationProperty {
        /**
         * Configuration information for Amazon AppIntegrations to automatically ingest content.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-sourceconfiguration.html#cfn-wisdom-knowledgebase-sourceconfiguration-appintegrations
         */
        readonly appIntegrations?: CfnKnowledgeBase.AppIntegrationsConfigurationProperty | cdk.IResolvable;
        /**
         * Source configuration for managed resources.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-sourceconfiguration.html#cfn-wisdom-knowledgebase-sourceconfiguration-managedsourceconfiguration
         */
        readonly managedSourceConfiguration?: cdk.IResolvable | CfnKnowledgeBase.ManagedSourceConfigurationProperty;
    }
    /**
     * Configuration information for Amazon AppIntegrations to automatically ingest content.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-appintegrationsconfiguration.html
     */
    interface AppIntegrationsConfigurationProperty {
        /**
         * The Amazon Resource Name (ARN) of the AppIntegrations DataIntegration to use for ingesting content.
         *
         * - For [Salesforce](https://docs.aws.amazon.com/https://developer.salesforce.com/docs/atlas.en-us.knowledge_dev.meta/knowledge_dev/sforce_api_objects_knowledge__kav.htm) , your AppIntegrations DataIntegration must have an ObjectConfiguration if objectFields is not provided, including at least `Id` , `ArticleNumber` , `VersionNumber` , `Title` , `PublishStatus` , and `IsDeleted` as source fields.
         * - For [ServiceNow](https://docs.aws.amazon.com/https://developer.servicenow.com/dev.do#!/reference/api/rome/rest/knowledge-management-api) , your AppIntegrations DataIntegration must have an ObjectConfiguration if objectFields is not provided, including at least `number` , `short_description` , `sys_mod_count` , `workflow_state` , and `active` as source fields.
         * - For [Zendesk](https://docs.aws.amazon.com/https://developer.zendesk.com/api-reference/help_center/help-center-api/articles/) , your AppIntegrations DataIntegration must have an ObjectConfiguration if `objectFields` is not provided, including at least `id` , `title` , `updated_at` , and `draft` as source fields.
         * - For [SharePoint](https://docs.aws.amazon.com/https://learn.microsoft.com/en-us/sharepoint/dev/sp-add-ins/sharepoint-net-server-csom-jsom-and-rest-api-index) , your AppIntegrations DataIntegration must have a FileConfiguration, including only file extensions that are among `docx` , `pdf` , `html` , `htm` , and `txt` .
         * - For [Amazon S3](https://docs.aws.amazon.com/s3/) , the ObjectConfiguration and FileConfiguration of your AppIntegrations DataIntegration must be null. The `SourceURI` of your DataIntegration must use the following format: `s3://your_s3_bucket_name` .
         *
         * > The bucket policy of the corresponding S3 bucket must allow the AWS principal `app-integrations.amazonaws.com` to perform `s3:ListBucket` , `s3:GetObject` , and `s3:GetBucketLocation` against the bucket.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-appintegrationsconfiguration.html#cfn-wisdom-knowledgebase-appintegrationsconfiguration-appintegrationarn
         */
        readonly appIntegrationArn: string;
        /**
         * The fields from the source that are made available to your agents in Amazon Q in Connect.
         *
         * Optional if ObjectConfiguration is included in the provided DataIntegration.
         *
         * - For [Salesforce](https://docs.aws.amazon.com/https://developer.salesforce.com/docs/atlas.en-us.knowledge_dev.meta/knowledge_dev/sforce_api_objects_knowledge__kav.htm) , you must include at least `Id` , `ArticleNumber` , `VersionNumber` , `Title` , `PublishStatus` , and `IsDeleted` .
         * - For [ServiceNow](https://docs.aws.amazon.com/https://developer.servicenow.com/dev.do#!/reference/api/rome/rest/knowledge-management-api) , you must include at least `number` , `short_description` , `sys_mod_count` , `workflow_state` , and `active` .
         * - For [Zendesk](https://docs.aws.amazon.com/https://developer.zendesk.com/api-reference/help_center/help-center-api/articles/) , you must include at least `id` , `title` , `updated_at` , and `draft` .
         *
         * Make sure to include additional fields. These fields are indexed and used to source recommendations.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-appintegrationsconfiguration.html#cfn-wisdom-knowledgebase-appintegrationsconfiguration-objectfields
         */
        readonly objectFields?: Array<string>;
    }
    /**
     * Source configuration for managed resources.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-managedsourceconfiguration.html
     */
    interface ManagedSourceConfigurationProperty {
        /**
         * Configuration data for web crawler data source.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-managedsourceconfiguration.html#cfn-wisdom-knowledgebase-managedsourceconfiguration-webcrawlerconfiguration
         */
        readonly webCrawlerConfiguration: cdk.IResolvable | CfnKnowledgeBase.WebCrawlerConfigurationProperty;
    }
    /**
     * The configuration details for the web data source.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-webcrawlerconfiguration.html
     */
    interface WebCrawlerConfigurationProperty {
        /**
         * The configuration of crawl limits for the web URLs.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-webcrawlerconfiguration.html#cfn-wisdom-knowledgebase-webcrawlerconfiguration-crawlerlimits
         */
        readonly crawlerLimits?: CfnKnowledgeBase.CrawlerLimitsProperty | cdk.IResolvable;
        /**
         * A list of one or more exclusion regular expression patterns to exclude certain URLs.
         *
         * If you specify an inclusion and exclusion filter/pattern and both match a URL, the exclusion filter takes precedence and the web content of the URL isn’t crawled.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-webcrawlerconfiguration.html#cfn-wisdom-knowledgebase-webcrawlerconfiguration-exclusionfilters
         */
        readonly exclusionFilters?: Array<string>;
        /**
         * A list of one or more inclusion regular expression patterns to include certain URLs.
         *
         * If you specify an inclusion and exclusion filter/pattern and both match a URL, the exclusion filter takes precedence and the web content of the URL isn’t crawled.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-webcrawlerconfiguration.html#cfn-wisdom-knowledgebase-webcrawlerconfiguration-inclusionfilters
         */
        readonly inclusionFilters?: Array<string>;
        /**
         * The scope of what is crawled for your URLs.
         *
         * You can choose to crawl only web pages that belong to the same host or primary domain. For example, only web pages that contain the seed URL `https://docs.aws.amazon.com/bedrock/latest/userguide/` and no other domains. You can choose to include sub domains in addition to the host or primary domain. For example, web pages that contain `aws.amazon.com` can also include sub domain `docs.aws.amazon.com` .
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-webcrawlerconfiguration.html#cfn-wisdom-knowledgebase-webcrawlerconfiguration-scope
         */
        readonly scope?: string;
        /**
         * The configuration of the URL/URLs for the web content that you want to crawl.
         *
         * You should be authorized to crawl the URLs.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-webcrawlerconfiguration.html#cfn-wisdom-knowledgebase-webcrawlerconfiguration-urlconfiguration
         */
        readonly urlConfiguration: cdk.IResolvable | CfnKnowledgeBase.UrlConfigurationProperty;
    }
    /**
     * The configuration of the URL/URLs for the web content that you want to crawl.
     *
     * You should be authorized to crawl the URLs.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-urlconfiguration.html
     */
    interface UrlConfigurationProperty {
        /**
         * List of URLs for crawling.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-urlconfiguration.html#cfn-wisdom-knowledgebase-urlconfiguration-seedurls
         */
        readonly seedUrls?: Array<cdk.IResolvable | CfnKnowledgeBase.SeedUrlProperty> | cdk.IResolvable;
    }
    /**
     * A URL for crawling.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-seedurl.html
     */
    interface SeedUrlProperty {
        /**
         * URL for crawling.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-seedurl.html#cfn-wisdom-knowledgebase-seedurl-url
         */
        readonly url?: string;
    }
    /**
     * The limits of the crawler.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-crawlerlimits.html
     */
    interface CrawlerLimitsProperty {
        /**
         * The limit rate at which the crawler is configured.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-crawlerlimits.html#cfn-wisdom-knowledgebase-crawlerlimits-ratelimit
         */
        readonly rateLimit?: number;
    }
    /**
     * The configuration information for the customer managed key used for encryption.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-serversideencryptionconfiguration.html
     */
    interface ServerSideEncryptionConfigurationProperty {
        /**
         * The customer managed key used for encryption.
         *
         * This customer managed key must have a policy that allows `kms:CreateGrant` and `kms:DescribeKey` permissions to the IAM identity using the key to invoke Wisdom.
         *
         * For more information about setting up a customer managed key for Wisdom, see [Enable Amazon Connect Wisdom for your instance](https://docs.aws.amazon.com/connect/latest/adminguide/enable-wisdom.html) . For information about valid ID values, see [Key identifiers (KeyId)](https://docs.aws.amazon.com/kms/latest/developerguide/concepts.html#key-id) .
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-serversideencryptionconfiguration.html#cfn-wisdom-knowledgebase-serversideencryptionconfiguration-kmskeyid
         */
        readonly kmsKeyId?: string;
    }
    /**
     * Information about how to render the content.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-renderingconfiguration.html
     */
    interface RenderingConfigurationProperty {
        /**
         * A URI template containing exactly one variable in `${variableName}` format.
         *
         * This can only be set for `EXTERNAL` knowledge bases. For Salesforce, ServiceNow, and Zendesk, the variable must be one of the following:
         *
         * - Salesforce: `Id` , `ArticleNumber` , `VersionNumber` , `Title` , `PublishStatus` , or `IsDeleted`
         * - ServiceNow: `number` , `short_description` , `sys_mod_count` , `workflow_state` , or `active`
         * - Zendesk: `id` , `title` , `updated_at` , or `draft`
         *
         * The variable is replaced with the actual value for a piece of content when calling [GetContent](https://docs.aws.amazon.com/amazon-q-connect/latest/APIReference/API_GetContent.html) .
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-renderingconfiguration.html#cfn-wisdom-knowledgebase-renderingconfiguration-templateuri
         */
        readonly templateUri?: string;
    }
    /**
     * Contains details about how to ingest the documents in a data source.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-vectoringestionconfiguration.html
     */
    interface VectorIngestionConfigurationProperty {
        /**
         * Details about how to chunk the documents in the data source.
         *
         * A chunk refers to an excerpt from a data source that is returned when the knowledge base that it belongs to is queried.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-vectoringestionconfiguration.html#cfn-wisdom-knowledgebase-vectoringestionconfiguration-chunkingconfiguration
         */
        readonly chunkingConfiguration?: CfnKnowledgeBase.ChunkingConfigurationProperty | cdk.IResolvable;
        /**
         * A custom parser for data source documents.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-vectoringestionconfiguration.html#cfn-wisdom-knowledgebase-vectoringestionconfiguration-parsingconfiguration
         */
        readonly parsingConfiguration?: cdk.IResolvable | CfnKnowledgeBase.ParsingConfigurationProperty;
    }
    /**
     * Details about how to chunk the documents in the data source.
     *
     * A chunk refers to an excerpt from a data source that is returned when the knowledge base that it belongs to is queried.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-chunkingconfiguration.html
     */
    interface ChunkingConfigurationProperty {
        /**
         * Knowledge base can split your source data into chunks.
         *
         * A chunk refers to an excerpt from a data source that is returned when the knowledge base that it belongs to is queried. You have the following options for chunking your data. If you opt for `NONE` , then you may want to pre-process your files by splitting them up such that each file corresponds to a chunk.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-chunkingconfiguration.html#cfn-wisdom-knowledgebase-chunkingconfiguration-chunkingstrategy
         */
        readonly chunkingStrategy: string;
        /**
         * Configurations for when you choose fixed-size chunking.
         *
         * If you set the `chunkingStrategy` as `NONE` , exclude this field.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-chunkingconfiguration.html#cfn-wisdom-knowledgebase-chunkingconfiguration-fixedsizechunkingconfiguration
         */
        readonly fixedSizeChunkingConfiguration?: CfnKnowledgeBase.FixedSizeChunkingConfigurationProperty | cdk.IResolvable;
        /**
         * Settings for hierarchical document chunking for a data source.
         *
         * Hierarchical chunking splits documents into layers of chunks where the first layer contains large chunks, and the second layer contains smaller chunks derived from the first layer.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-chunkingconfiguration.html#cfn-wisdom-knowledgebase-chunkingconfiguration-hierarchicalchunkingconfiguration
         */
        readonly hierarchicalChunkingConfiguration?: CfnKnowledgeBase.HierarchicalChunkingConfigurationProperty | cdk.IResolvable;
        /**
         * Settings for semantic document chunking for a data source.
         *
         * Semantic chunking splits a document into smaller documents based on groups of similar content derived from the text with natural language processing.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-chunkingconfiguration.html#cfn-wisdom-knowledgebase-chunkingconfiguration-semanticchunkingconfiguration
         */
        readonly semanticChunkingConfiguration?: cdk.IResolvable | CfnKnowledgeBase.SemanticChunkingConfigurationProperty;
    }
    /**
     * Configurations for when you choose fixed-size chunking.
     *
     * If you set the `chunkingStrategy` as `NONE` , exclude this field.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-fixedsizechunkingconfiguration.html
     */
    interface FixedSizeChunkingConfigurationProperty {
        /**
         * The maximum number of tokens to include in a chunk.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-fixedsizechunkingconfiguration.html#cfn-wisdom-knowledgebase-fixedsizechunkingconfiguration-maxtokens
         */
        readonly maxTokens: number;
        /**
         * The percentage of overlap between adjacent chunks of a data source.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-fixedsizechunkingconfiguration.html#cfn-wisdom-knowledgebase-fixedsizechunkingconfiguration-overlappercentage
         */
        readonly overlapPercentage: number;
    }
    /**
     * Settings for hierarchical document chunking for a data source.
     *
     * Hierarchical chunking splits documents into layers of chunks where the first layer contains large chunks, and the second layer contains smaller chunks derived from the first layer.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-hierarchicalchunkingconfiguration.html
     */
    interface HierarchicalChunkingConfigurationProperty {
        /**
         * Token settings for each layer.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-hierarchicalchunkingconfiguration.html#cfn-wisdom-knowledgebase-hierarchicalchunkingconfiguration-levelconfigurations
         */
        readonly levelConfigurations: Array<CfnKnowledgeBase.HierarchicalChunkingLevelConfigurationProperty | cdk.IResolvable> | cdk.IResolvable;
        /**
         * The number of tokens to repeat across chunks in the same layer.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-hierarchicalchunkingconfiguration.html#cfn-wisdom-knowledgebase-hierarchicalchunkingconfiguration-overlaptokens
         */
        readonly overlapTokens: number;
    }
    /**
     * Token settings for each layer.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-hierarchicalchunkinglevelconfiguration.html
     */
    interface HierarchicalChunkingLevelConfigurationProperty {
        /**
         * The maximum number of tokens that a chunk can contain in this layer.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-hierarchicalchunkinglevelconfiguration.html#cfn-wisdom-knowledgebase-hierarchicalchunkinglevelconfiguration-maxtokens
         */
        readonly maxTokens: number;
    }
    /**
     * Settings for semantic document chunking for a data source.
     *
     * Semantic chunking splits a document into smaller documents based on groups of similar content derived from the text with natural language processing.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-semanticchunkingconfiguration.html
     */
    interface SemanticChunkingConfigurationProperty {
        /**
         * The dissimilarity threshold for splitting chunks.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-semanticchunkingconfiguration.html#cfn-wisdom-knowledgebase-semanticchunkingconfiguration-breakpointpercentilethreshold
         */
        readonly breakpointPercentileThreshold: number;
        /**
         * The buffer size.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-semanticchunkingconfiguration.html#cfn-wisdom-knowledgebase-semanticchunkingconfiguration-buffersize
         */
        readonly bufferSize: number;
        /**
         * The maximum number of tokens that a chunk can contain.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-semanticchunkingconfiguration.html#cfn-wisdom-knowledgebase-semanticchunkingconfiguration-maxtokens
         */
        readonly maxTokens: number;
    }
    /**
     * Settings for parsing document contents.
     *
     * By default, the service converts the contents of each document into text before splitting it into chunks. To improve processing of PDF files with tables and images, you can configure the data source to convert the pages of text into images and use a model to describe the contents of each page.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-parsingconfiguration.html
     */
    interface ParsingConfigurationProperty {
        /**
         * Settings for a foundation model used to parse documents for a data source.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-parsingconfiguration.html#cfn-wisdom-knowledgebase-parsingconfiguration-bedrockfoundationmodelconfiguration
         */
        readonly bedrockFoundationModelConfiguration?: CfnKnowledgeBase.BedrockFoundationModelConfigurationProperty | cdk.IResolvable;
        /**
         * The parsing strategy for the data source.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-parsingconfiguration.html#cfn-wisdom-knowledgebase-parsingconfiguration-parsingstrategy
         */
        readonly parsingStrategy: string;
    }
    /**
     * The configuration of the Bedrock foundation model.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-bedrockfoundationmodelconfiguration.html
     */
    interface BedrockFoundationModelConfigurationProperty {
        /**
         * The model ARN of the Bedrock foundation model.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-bedrockfoundationmodelconfiguration.html#cfn-wisdom-knowledgebase-bedrockfoundationmodelconfiguration-modelarn
         */
        readonly modelArn: string;
        /**
         * The parsing prompt of the Bedrock foundation model configuration.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-bedrockfoundationmodelconfiguration.html#cfn-wisdom-knowledgebase-bedrockfoundationmodelconfiguration-parsingprompt
         */
        readonly parsingPrompt?: cdk.IResolvable | CfnKnowledgeBase.ParsingPromptProperty;
    }
    /**
     * Instructions for interpreting the contents of a document.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-parsingprompt.html
     */
    interface ParsingPromptProperty {
        /**
         * Instructions for interpreting the contents of a document.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-knowledgebase-parsingprompt.html#cfn-wisdom-knowledgebase-parsingprompt-parsingprompttext
         */
        readonly parsingPromptText: string;
    }
}
/**
 * Properties for defining a `CfnKnowledgeBase`
 *
 * @struct
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-knowledgebase.html
 */
export interface CfnKnowledgeBaseProps {
    /**
     * The description.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-knowledgebase.html#cfn-wisdom-knowledgebase-description
     */
    readonly description?: string;
    /**
     * The type of knowledge base.
     *
     * Only CUSTOM knowledge bases allow you to upload your own content. EXTERNAL knowledge bases support integrations with third-party systems whose content is synchronized automatically.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-knowledgebase.html#cfn-wisdom-knowledgebase-knowledgebasetype
     */
    readonly knowledgeBaseType: string;
    /**
     * The name of the knowledge base.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-knowledgebase.html#cfn-wisdom-knowledgebase-name
     */
    readonly name: string;
    /**
     * Information about how to render the content.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-knowledgebase.html#cfn-wisdom-knowledgebase-renderingconfiguration
     */
    readonly renderingConfiguration?: cdk.IResolvable | CfnKnowledgeBase.RenderingConfigurationProperty;
    /**
     * This customer managed key must have a policy that allows `kms:CreateGrant` and `kms:DescribeKey` permissions to the IAM identity using the key to invoke Wisdom.
     *
     * For more information about setting up a customer managed key for Wisdom, see [Enable Amazon Connect Wisdom for your instance](https://docs.aws.amazon.com/connect/latest/adminguide/enable-wisdom.html) . For information about valid ID values, see [Key identifiers (KeyId)](https://docs.aws.amazon.com/kms/latest/developerguide/concepts.html#key-id) in the *AWS Key Management Service Developer Guide* .
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-knowledgebase.html#cfn-wisdom-knowledgebase-serversideencryptionconfiguration
     */
    readonly serverSideEncryptionConfiguration?: cdk.IResolvable | CfnKnowledgeBase.ServerSideEncryptionConfigurationProperty;
    /**
     * The source of the knowledge base content.
     *
     * Only set this argument for EXTERNAL or Managed knowledge bases.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-knowledgebase.html#cfn-wisdom-knowledgebase-sourceconfiguration
     */
    readonly sourceConfiguration?: cdk.IResolvable | CfnKnowledgeBase.SourceConfigurationProperty;
    /**
     * The tags used to organize, track, or control access for this resource.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-knowledgebase.html#cfn-wisdom-knowledgebase-tags
     */
    readonly tags?: Array<cdk.CfnTag>;
    /**
     * Contains details about how to ingest the documents in a data source.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-knowledgebase.html#cfn-wisdom-knowledgebase-vectoringestionconfiguration
     */
    readonly vectorIngestionConfiguration?: cdk.IResolvable | CfnKnowledgeBase.VectorIngestionConfigurationProperty;
}
/**
 * Creates an Amazon Q in Connect AI Agent.
 *
 * @cloudformationResource AWS::Wisdom::AIAgent
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiagent.html
 */
export declare class CfnAIAgent extends cdk.CfnResource implements cdk.IInspectable, cdk.ITaggableV2 {
    /**
     * The CloudFormation resource type name for this resource class.
     */
    static readonly CFN_RESOURCE_TYPE_NAME: string;
    /**
     * Build a CfnAIAgent from CloudFormation properties
     *
     * A factory method that creates a new instance of this class from an object
     * containing the CloudFormation properties of this resource.
     * Used in the @aws-cdk/cloudformation-include module.
     *
     * @internal
     */
    static _fromCloudFormation(scope: constructs.Construct, id: string, resourceAttributes: any, options: cfn_parse.FromCloudFormationOptions): CfnAIAgent;
    /**
     * The Amazon Resource Name (ARN) of the AI agent.
     *
     * @cloudformationAttribute AIAgentArn
     */
    readonly attrAiAgentArn: string;
    /**
     * The identifier of the AI Agent.
     *
     * @cloudformationAttribute AIAgentId
     */
    readonly attrAiAgentId: string;
    /**
     * The Amazon Resource Name (ARN) of the Amazon Q in Connect assistant.
     *
     * @cloudformationAttribute AssistantArn
     */
    readonly attrAssistantArn: string;
    /**
     * @cloudformationAttribute ModifiedTimeSeconds
     */
    readonly attrModifiedTimeSeconds: cdk.IResolvable;
    /**
     * The identifier of the Amazon Q in Connect assistant.
     */
    assistantId: string;
    /**
     * Tag Manager which manages the tags for this resource
     */
    readonly cdkTagManager: cdk.TagManager;
    /**
     * Configuration for the AI Agent.
     */
    configuration: CfnAIAgent.AIAgentConfigurationProperty | cdk.IResolvable;
    /**
     * The description of the AI Agent.
     */
    description?: string;
    /**
     * The name of the AI Agent.
     */
    name?: string;
    /**
     * The tags used to organize, track, or control access for this resource.
     */
    tags?: Record<string, string>;
    /**
     * The type of the AI Agent.
     */
    type: string;
    /**
     * @param scope Scope in which this resource is defined
     * @param id Construct identifier for this resource (unique in its scope)
     * @param props Resource properties
     */
    constructor(scope: constructs.Construct, id: string, props: CfnAIAgentProps);
    protected get cfnProperties(): Record<string, any>;
    /**
     * Examines the CloudFormation resource and discloses attributes
     *
     * @param inspector tree inspector to collect and process attributes
     */
    inspect(inspector: cdk.TreeInspector): void;
    protected renderProperties(props: Record<string, any>): Record<string, any>;
}
export declare namespace CfnAIAgent {
    /**
     * A typed union that specifies the configuration based on the type of AI Agent.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-aiagentconfiguration.html
     */
    interface AIAgentConfigurationProperty {
        /**
         * The configuration for AI Agents of type `ANSWER_RECOMMENDATION` .
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-aiagentconfiguration.html#cfn-wisdom-aiagent-aiagentconfiguration-answerrecommendationaiagentconfiguration
         */
        readonly answerRecommendationAiAgentConfiguration?: CfnAIAgent.AnswerRecommendationAIAgentConfigurationProperty | cdk.IResolvable;
        /**
         * The configuration for AI Agents of type `MANUAL_SEARCH` .
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-aiagentconfiguration.html#cfn-wisdom-aiagent-aiagentconfiguration-manualsearchaiagentconfiguration
         */
        readonly manualSearchAiAgentConfiguration?: cdk.IResolvable | CfnAIAgent.ManualSearchAIAgentConfigurationProperty;
        /**
         * The self-service AI agent configuration.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-aiagentconfiguration.html#cfn-wisdom-aiagent-aiagentconfiguration-selfserviceaiagentconfiguration
         */
        readonly selfServiceAiAgentConfiguration?: cdk.IResolvable | CfnAIAgent.SelfServiceAIAgentConfigurationProperty;
    }
    /**
     * The configuration for AI Agents of type `MANUAL_SEARCH` .
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-manualsearchaiagentconfiguration.html
     */
    interface ManualSearchAIAgentConfigurationProperty {
        /**
         * The ID of the answer generation AI guardrail.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-manualsearchaiagentconfiguration.html#cfn-wisdom-aiagent-manualsearchaiagentconfiguration-answergenerationaiguardrailid
         */
        readonly answerGenerationAiGuardrailId?: string;
        /**
         * The AI Prompt identifier for the Answer Generation prompt used by the `ANSWER_RECOMMENDATION` AI Agent.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-manualsearchaiagentconfiguration.html#cfn-wisdom-aiagent-manualsearchaiagentconfiguration-answergenerationaipromptid
         */
        readonly answerGenerationAiPromptId?: string;
        /**
         * The association configurations for overriding behavior on this AI Agent.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-manualsearchaiagentconfiguration.html#cfn-wisdom-aiagent-manualsearchaiagentconfiguration-associationconfigurations
         */
        readonly associationConfigurations?: Array<CfnAIAgent.AssociationConfigurationProperty | cdk.IResolvable> | cdk.IResolvable;
        /**
         * The locale to which specifies the language and region settings that determine the response language for [QueryAssistant](https://docs.aws.amazon.com/connect/latest/APIReference/API_amazon-q-connect_QueryAssistant.html) .
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-manualsearchaiagentconfiguration.html#cfn-wisdom-aiagent-manualsearchaiagentconfiguration-locale
         */
        readonly locale?: string;
    }
    /**
     * The configuration for an Amazon Q in Connect Assistant Association.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-associationconfiguration.html
     */
    interface AssociationConfigurationProperty {
        /**
         * A typed union of the data of the configuration for an Amazon Q in Connect Assistant Association.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-associationconfiguration.html#cfn-wisdom-aiagent-associationconfiguration-associationconfigurationdata
         */
        readonly associationConfigurationData?: CfnAIAgent.AssociationConfigurationDataProperty | cdk.IResolvable;
        /**
         * The identifier of the association for this Association Configuration.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-associationconfiguration.html#cfn-wisdom-aiagent-associationconfiguration-associationid
         */
        readonly associationId?: string;
        /**
         * The type of the association for this Association Configuration.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-associationconfiguration.html#cfn-wisdom-aiagent-associationconfiguration-associationtype
         */
        readonly associationType?: string;
    }
    /**
     * A typed union of the data of the configuration for an Amazon Q in Connect Assistant Association.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-associationconfigurationdata.html
     */
    interface AssociationConfigurationDataProperty {
        /**
         * The data of the configuration for a `KNOWLEDGE_BASE` type Amazon Q in Connect Assistant Association.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-associationconfigurationdata.html#cfn-wisdom-aiagent-associationconfigurationdata-knowledgebaseassociationconfigurationdata
         */
        readonly knowledgeBaseAssociationConfigurationData: cdk.IResolvable | CfnAIAgent.KnowledgeBaseAssociationConfigurationDataProperty;
    }
    /**
     * The data of the configuration for a `KNOWLEDGE_BASE` type Amazon Q in Connect Assistant Association.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-knowledgebaseassociationconfigurationdata.html
     */
    interface KnowledgeBaseAssociationConfigurationDataProperty {
        /**
         * An object that can be used to specify Tag conditions.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-knowledgebaseassociationconfigurationdata.html#cfn-wisdom-aiagent-knowledgebaseassociationconfigurationdata-contenttagfilter
         */
        readonly contentTagFilter?: cdk.IResolvable | CfnAIAgent.TagFilterProperty;
        /**
         * The maximum number of results to return per page.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-knowledgebaseassociationconfigurationdata.html#cfn-wisdom-aiagent-knowledgebaseassociationconfigurationdata-maxresults
         */
        readonly maxResults?: number;
        /**
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-knowledgebaseassociationconfigurationdata.html#cfn-wisdom-aiagent-knowledgebaseassociationconfigurationdata-overrideknowledgebasesearchtype
         */
        readonly overrideKnowledgeBaseSearchType?: string;
    }
    /**
     * An object that can be used to specify tag conditions.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-tagfilter.html
     */
    interface TagFilterProperty {
        /**
         * A list of conditions which would be applied together with an `AND` condition.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-tagfilter.html#cfn-wisdom-aiagent-tagfilter-andconditions
         */
        readonly andConditions?: Array<cdk.IResolvable | CfnAIAgent.TagConditionProperty> | cdk.IResolvable;
        /**
         * A list of conditions which would be applied together with an `OR` condition.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-tagfilter.html#cfn-wisdom-aiagent-tagfilter-orconditions
         */
        readonly orConditions?: Array<cdk.IResolvable | CfnAIAgent.OrConditionProperty> | cdk.IResolvable;
        /**
         * A leaf node condition which can be used to specify a tag condition.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-tagfilter.html#cfn-wisdom-aiagent-tagfilter-tagcondition
         */
        readonly tagCondition?: cdk.IResolvable | CfnAIAgent.TagConditionProperty;
    }
    /**
     * An object that can be used to specify tag conditions.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-tagcondition.html
     */
    interface TagConditionProperty {
        /**
         * The tag key in the tag condition.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-tagcondition.html#cfn-wisdom-aiagent-tagcondition-key
         */
        readonly key: string;
        /**
         * The tag value in the tag condition.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-tagcondition.html#cfn-wisdom-aiagent-tagcondition-value
         */
        readonly value?: string;
    }
    /**
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-orcondition.html
     */
    interface OrConditionProperty {
        /**
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-orcondition.html#cfn-wisdom-aiagent-orcondition-andconditions
         */
        readonly andConditions?: Array<cdk.IResolvable | CfnAIAgent.TagConditionProperty> | cdk.IResolvable;
        /**
         * A leaf node condition which can be used to specify a tag condition.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-orcondition.html#cfn-wisdom-aiagent-orcondition-tagcondition
         */
        readonly tagCondition?: cdk.IResolvable | CfnAIAgent.TagConditionProperty;
    }
    /**
     * The configuration for AI Agents of type `ANSWER_RECOMMENDATION` .
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-answerrecommendationaiagentconfiguration.html
     */
    interface AnswerRecommendationAIAgentConfigurationProperty {
        /**
         * The ID of the answer generation AI guardrail.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-answerrecommendationaiagentconfiguration.html#cfn-wisdom-aiagent-answerrecommendationaiagentconfiguration-answergenerationaiguardrailid
         */
        readonly answerGenerationAiGuardrailId?: string;
        /**
         * The AI Prompt identifier for the Answer Generation prompt used by the `ANSWER_RECOMMENDATION` AI Agent.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-answerrecommendationaiagentconfiguration.html#cfn-wisdom-aiagent-answerrecommendationaiagentconfiguration-answergenerationaipromptid
         */
        readonly answerGenerationAiPromptId?: string;
        /**
         * The association configurations for overriding behavior on this AI Agent.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-answerrecommendationaiagentconfiguration.html#cfn-wisdom-aiagent-answerrecommendationaiagentconfiguration-associationconfigurations
         */
        readonly associationConfigurations?: Array<CfnAIAgent.AssociationConfigurationProperty | cdk.IResolvable> | cdk.IResolvable;
        /**
         * The AI Prompt identifier for the Intent Labeling prompt used by the `ANSWER_RECOMMENDATION` AI Agent.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-answerrecommendationaiagentconfiguration.html#cfn-wisdom-aiagent-answerrecommendationaiagentconfiguration-intentlabelinggenerationaipromptid
         */
        readonly intentLabelingGenerationAiPromptId?: string;
        /**
         * The locale to which specifies the language and region settings that determine the response language for [QueryAssistant](https://docs.aws.amazon.com/connect/latest/APIReference/API_amazon-q-connect_QueryAssistant.html) .
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-answerrecommendationaiagentconfiguration.html#cfn-wisdom-aiagent-answerrecommendationaiagentconfiguration-locale
         */
        readonly locale?: string;
        /**
         * The AI Prompt identifier for the Query Reformulation prompt used by the `ANSWER_RECOMMENDATION` AI Agent.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-answerrecommendationaiagentconfiguration.html#cfn-wisdom-aiagent-answerrecommendationaiagentconfiguration-queryreformulationaipromptid
         */
        readonly queryReformulationAiPromptId?: string;
    }
    /**
     * The configuration of the self-service AI agent.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-selfserviceaiagentconfiguration.html
     */
    interface SelfServiceAIAgentConfigurationProperty {
        /**
         * The association configuration of the self-service AI agent.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-selfserviceaiagentconfiguration.html#cfn-wisdom-aiagent-selfserviceaiagentconfiguration-associationconfigurations
         */
        readonly associationConfigurations?: Array<CfnAIAgent.AssociationConfigurationProperty | cdk.IResolvable> | cdk.IResolvable;
        /**
         * The ID of the self-service AI guardrail.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-selfserviceaiagentconfiguration.html#cfn-wisdom-aiagent-selfserviceaiagentconfiguration-selfserviceaiguardrailid
         */
        readonly selfServiceAiGuardrailId?: string;
        /**
         * The ID of the self-service answer generation AI prompt.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-selfserviceaiagentconfiguration.html#cfn-wisdom-aiagent-selfserviceaiagentconfiguration-selfserviceanswergenerationaipromptid
         */
        readonly selfServiceAnswerGenerationAiPromptId?: string;
        /**
         * The ID of the self-service preprocessing AI prompt.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiagent-selfserviceaiagentconfiguration.html#cfn-wisdom-aiagent-selfserviceaiagentconfiguration-selfservicepreprocessingaipromptid
         */
        readonly selfServicePreProcessingAiPromptId?: string;
    }
}
/**
 * Properties for defining a `CfnAIAgent`
 *
 * @struct
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiagent.html
 */
export interface CfnAIAgentProps {
    /**
     * The identifier of the Amazon Q in Connect assistant.
     *
     * Can be either the ID or the ARN. URLs cannot contain the ARN.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiagent.html#cfn-wisdom-aiagent-assistantid
     */
    readonly assistantId: string;
    /**
     * Configuration for the AI Agent.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiagent.html#cfn-wisdom-aiagent-configuration
     */
    readonly configuration: CfnAIAgent.AIAgentConfigurationProperty | cdk.IResolvable;
    /**
     * The description of the AI Agent.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiagent.html#cfn-wisdom-aiagent-description
     */
    readonly description?: string;
    /**
     * The name of the AI Agent.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiagent.html#cfn-wisdom-aiagent-name
     */
    readonly name?: string;
    /**
     * The tags used to organize, track, or control access for this resource.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiagent.html#cfn-wisdom-aiagent-tags
     */
    readonly tags?: Record<string, string>;
    /**
     * The type of the AI Agent.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiagent.html#cfn-wisdom-aiagent-type
     */
    readonly type: string;
}
/**
 * Creates and Amazon Q in Connect AI Agent version.
 *
 * @cloudformationResource AWS::Wisdom::AIAgentVersion
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiagentversion.html
 */
export declare class CfnAIAgentVersion extends cdk.CfnResource implements cdk.IInspectable {
    /**
     * The CloudFormation resource type name for this resource class.
     */
    static readonly CFN_RESOURCE_TYPE_NAME: string;
    /**
     * Build a CfnAIAgentVersion from CloudFormation properties
     *
     * A factory method that creates a new instance of this class from an object
     * containing the CloudFormation properties of this resource.
     * Used in the @aws-cdk/cloudformation-include module.
     *
     * @internal
     */
    static _fromCloudFormation(scope: constructs.Construct, id: string, resourceAttributes: any, options: cfn_parse.FromCloudFormationOptions): CfnAIAgentVersion;
    /**
     * @cloudformationAttribute AIAgentArn
     */
    readonly attrAiAgentArn: string;
    /**
     * @cloudformationAttribute AIAgentVersionId
     */
    readonly attrAiAgentVersionId: string;
    /**
     * @cloudformationAttribute AssistantArn
     */
    readonly attrAssistantArn: string;
    /**
     * The version number for this AI Agent version.
     *
     * @cloudformationAttribute VersionNumber
     */
    readonly attrVersionNumber: cdk.IResolvable;
    /**
     * The identifier of the AI Agent.
     */
    aiAgentId: string;
    assistantId: string;
    /**
     * The time the AI Agent version was last modified in seconds.
     */
    modifiedTimeSeconds?: number;
    /**
     * @param scope Scope in which this resource is defined
     * @param id Construct identifier for this resource (unique in its scope)
     * @param props Resource properties
     */
    constructor(scope: constructs.Construct, id: string, props: CfnAIAgentVersionProps);
    protected get cfnProperties(): Record<string, any>;
    /**
     * Examines the CloudFormation resource and discloses attributes
     *
     * @param inspector tree inspector to collect and process attributes
     */
    inspect(inspector: cdk.TreeInspector): void;
    protected renderProperties(props: Record<string, any>): Record<string, any>;
}
/**
 * Properties for defining a `CfnAIAgentVersion`
 *
 * @struct
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiagentversion.html
 */
export interface CfnAIAgentVersionProps {
    /**
     * The identifier of the AI Agent.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiagentversion.html#cfn-wisdom-aiagentversion-aiagentid
     */
    readonly aiAgentId: string;
    /**
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiagentversion.html#cfn-wisdom-aiagentversion-assistantid
     */
    readonly assistantId: string;
    /**
     * The time the AI Agent version was last modified in seconds.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiagentversion.html#cfn-wisdom-aiagentversion-modifiedtimeseconds
     */
    readonly modifiedTimeSeconds?: number;
}
/**
 * Creates an Amazon Q in Connect AI Guardrail.
 *
 * @cloudformationResource AWS::Wisdom::AIGuardrail
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiguardrail.html
 */
export declare class CfnAIGuardrail extends cdk.CfnResource implements cdk.IInspectable, cdk.ITaggableV2 {
    /**
     * The CloudFormation resource type name for this resource class.
     */
    static readonly CFN_RESOURCE_TYPE_NAME: string;
    /**
     * Build a CfnAIGuardrail from CloudFormation properties
     *
     * A factory method that creates a new instance of this class from an object
     * containing the CloudFormation properties of this resource.
     * Used in the @aws-cdk/cloudformation-include module.
     *
     * @internal
     */
    static _fromCloudFormation(scope: constructs.Construct, id: string, resourceAttributes: any, options: cfn_parse.FromCloudFormationOptions): CfnAIGuardrail;
    /**
     * The Amazon Resource Name (ARN) of the AI Guardrail.
     *
     * @cloudformationAttribute AIGuardrailArn
     */
    readonly attrAiGuardrailArn: string;
    /**
     * The identifier of the Amazon Q in Connect AI Guardrail.
     *
     * @cloudformationAttribute AIGuardrailId
     */
    readonly attrAiGuardrailId: string;
    /**
     * The Amazon Resource Name (ARN) of the Amazon Q in Connect assistant.
     *
     * @cloudformationAttribute AssistantArn
     */
    readonly attrAssistantArn: string;
    /**
     * The identifier of the Amazon Q in Connect assistant.
     */
    assistantId: string;
    /**
     * The message to return when the AI Guardrail blocks a prompt.
     */
    blockedInputMessaging: string;
    /**
     * The message to return when the AI Guardrail blocks a model response.
     */
    blockedOutputsMessaging: string;
    /**
     * Tag Manager which manages the tags for this resource
     */
    readonly cdkTagManager: cdk.TagManager;
    /**
     * Contains details about how to handle harmful content.
     */
    contentPolicyConfig?: CfnAIGuardrail.AIGuardrailContentPolicyConfigProperty | cdk.IResolvable;
    /**
     * The policy configuration details for the AI Guardrail's contextual grounding policy.
     */
    contextualGroundingPolicyConfig?: CfnAIGuardrail.AIGuardrailContextualGroundingPolicyConfigProperty | cdk.IResolvable;
    /**
     * A description of the AI Guardrail.
     */
    description?: string;
    /**
     * The name of the AI Guardrail.
     */
    name?: string;
    /**
     * Contains details about PII entities and regular expressions to configure for the AI Guardrail.
     */
    sensitiveInformationPolicyConfig?: CfnAIGuardrail.AIGuardrailSensitiveInformationPolicyConfigProperty | cdk.IResolvable;
    /**
     * The tags used to organize, track, or control access for this resource.
     */
    tags?: Record<string, string>;
    /**
     * Contains details about topics that the AI Guardrail should identify and deny.
     */
    topicPolicyConfig?: CfnAIGuardrail.AIGuardrailTopicPolicyConfigProperty | cdk.IResolvable;
    /**
     * Contains details about the word policy to configured for the AI Guardrail.
     */
    wordPolicyConfig?: CfnAIGuardrail.AIGuardrailWordPolicyConfigProperty | cdk.IResolvable;
    /**
     * @param scope Scope in which this resource is defined
     * @param id Construct identifier for this resource (unique in its scope)
     * @param props Resource properties
     */
    constructor(scope: constructs.Construct, id: string, props: CfnAIGuardrailProps);
    protected get cfnProperties(): Record<string, any>;
    /**
     * Examines the CloudFormation resource and discloses attributes
     *
     * @param inspector tree inspector to collect and process attributes
     */
    inspect(inspector: cdk.TreeInspector): void;
    protected renderProperties(props: Record<string, any>): Record<string, any>;
}
export declare namespace CfnAIGuardrail {
    /**
     * Topic policy configuration for a guardrail.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-aiguardrailtopicpolicyconfig.html
     */
    interface AIGuardrailTopicPolicyConfigProperty {
        /**
         * List of topic configs in topic policy.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-aiguardrailtopicpolicyconfig.html#cfn-wisdom-aiguardrail-aiguardrailtopicpolicyconfig-topicsconfig
         */
        readonly topicsConfig: Array<CfnAIGuardrail.GuardrailTopicConfigProperty | cdk.IResolvable> | cdk.IResolvable;
    }
    /**
     * Topic configuration in topic policy.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-guardrailtopicconfig.html
     */
    interface GuardrailTopicConfigProperty {
        /**
         * Definition of topic in topic policy.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-guardrailtopicconfig.html#cfn-wisdom-aiguardrail-guardrailtopicconfig-definition
         */
        readonly definition: string;
        /**
         * Text example in topic policy.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-guardrailtopicconfig.html#cfn-wisdom-aiguardrail-guardrailtopicconfig-examples
         */
        readonly examples?: Array<string>;
        /**
         * Name of topic in topic policy.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-guardrailtopicconfig.html#cfn-wisdom-aiguardrail-guardrailtopicconfig-name
         */
        readonly name: string;
        /**
         * Type of topic in a policy.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-guardrailtopicconfig.html#cfn-wisdom-aiguardrail-guardrailtopicconfig-type
         */
        readonly type: string;
    }
    /**
     * Content policy config for a guardrail.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-aiguardrailcontentpolicyconfig.html
     */
    interface AIGuardrailContentPolicyConfigProperty {
        /**
         * List of content filter configurations in a content policy.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-aiguardrailcontentpolicyconfig.html#cfn-wisdom-aiguardrail-aiguardrailcontentpolicyconfig-filtersconfig
         */
        readonly filtersConfig: Array<CfnAIGuardrail.GuardrailContentFilterConfigProperty | cdk.IResolvable> | cdk.IResolvable;
    }
    /**
     * Content filter configuration in content policy.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-guardrailcontentfilterconfig.html
     */
    interface GuardrailContentFilterConfigProperty {
        /**
         * The strength of the input for the guardrail content filter.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-guardrailcontentfilterconfig.html#cfn-wisdom-aiguardrail-guardrailcontentfilterconfig-inputstrength
         */
        readonly inputStrength: string;
        /**
         * The output strength of the guardrail content filter.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-guardrailcontentfilterconfig.html#cfn-wisdom-aiguardrail-guardrailcontentfilterconfig-outputstrength
         */
        readonly outputStrength: string;
        /**
         * The type of the guardrail content filter.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-guardrailcontentfilterconfig.html#cfn-wisdom-aiguardrail-guardrailcontentfilterconfig-type
         */
        readonly type: string;
    }
    /**
     * Word policy config for a guardrail.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-aiguardrailwordpolicyconfig.html
     */
    interface AIGuardrailWordPolicyConfigProperty {
        /**
         * A config for the list of managed words.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-aiguardrailwordpolicyconfig.html#cfn-wisdom-aiguardrail-aiguardrailwordpolicyconfig-managedwordlistsconfig
         */
        readonly managedWordListsConfig?: Array<CfnAIGuardrail.GuardrailManagedWordsConfigProperty | cdk.IResolvable> | cdk.IResolvable;
        /**
         * List of custom word configurations.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-aiguardrailwordpolicyconfig.html#cfn-wisdom-aiguardrail-aiguardrailwordpolicyconfig-wordsconfig
         */
        readonly wordsConfig?: Array<CfnAIGuardrail.GuardrailWordConfigProperty | cdk.IResolvable> | cdk.IResolvable;
    }
    /**
     * A custom word config.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-guardrailwordconfig.html
     */
    interface GuardrailWordConfigProperty {
        /**
         * The custom word text.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-guardrailwordconfig.html#cfn-wisdom-aiguardrail-guardrailwordconfig-text
         */
        readonly text: string;
    }
    /**
     * A managed words config.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-guardrailmanagedwordsconfig.html
     */
    interface GuardrailManagedWordsConfigProperty {
        /**
         * The type of guardrail managed words.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-guardrailmanagedwordsconfig.html#cfn-wisdom-aiguardrail-guardrailmanagedwordsconfig-type
         */
        readonly type: string;
    }
    /**
     * Sensitive information policy configuration for a guardrail.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-aiguardrailsensitiveinformationpolicyconfig.html
     */
    interface AIGuardrailSensitiveInformationPolicyConfigProperty {
        /**
         * List of entities.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-aiguardrailsensitiveinformationpolicyconfig.html#cfn-wisdom-aiguardrail-aiguardrailsensitiveinformationpolicyconfig-piientitiesconfig
         */
        readonly piiEntitiesConfig?: Array<CfnAIGuardrail.GuardrailPiiEntityConfigProperty | cdk.IResolvable> | cdk.IResolvable;
        /**
         * List of regex.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-aiguardrailsensitiveinformationpolicyconfig.html#cfn-wisdom-aiguardrail-aiguardrailsensitiveinformationpolicyconfig-regexesconfig
         */
        readonly regexesConfig?: Array<CfnAIGuardrail.GuardrailRegexConfigProperty | cdk.IResolvable> | cdk.IResolvable;
    }
    /**
     * PII entity configuration.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-guardrailpiientityconfig.html
     */
    interface GuardrailPiiEntityConfigProperty {
        /**
         * The action of guardrail PII entity configuration.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-guardrailpiientityconfig.html#cfn-wisdom-aiguardrail-guardrailpiientityconfig-action
         */
        readonly action: string;
        /**
         * The currently supported PII entities.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-guardrailpiientityconfig.html#cfn-wisdom-aiguardrail-guardrailpiientityconfig-type
         */
        readonly type: string;
    }
    /**
     * A regex configuration.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-guardrailregexconfig.html
     */
    interface GuardrailRegexConfigProperty {
        /**
         * The action of the guardrail regex configuration.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-guardrailregexconfig.html#cfn-wisdom-aiguardrail-guardrailregexconfig-action
         */
        readonly action: string;
        /**
         * The regex description.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-guardrailregexconfig.html#cfn-wisdom-aiguardrail-guardrailregexconfig-description
         */
        readonly description?: string;
        /**
         * A regex configuration.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-guardrailregexconfig.html#cfn-wisdom-aiguardrail-guardrailregexconfig-name
         */
        readonly name: string;
        /**
         * The regex pattern.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-guardrailregexconfig.html#cfn-wisdom-aiguardrail-guardrailregexconfig-pattern
         */
        readonly pattern: string;
    }
    /**
     * Contextual grounding policy config for a guardrail.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-aiguardrailcontextualgroundingpolicyconfig.html
     */
    interface AIGuardrailContextualGroundingPolicyConfigProperty {
        /**
         * List of contextual grounding filter configs.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-aiguardrailcontextualgroundingpolicyconfig.html#cfn-wisdom-aiguardrail-aiguardrailcontextualgroundingpolicyconfig-filtersconfig
         */
        readonly filtersConfig: Array<CfnAIGuardrail.GuardrailContextualGroundingFilterConfigProperty | cdk.IResolvable> | cdk.IResolvable;
    }
    /**
     * A configuration for grounding filter.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-guardrailcontextualgroundingfilterconfig.html
     */
    interface GuardrailContextualGroundingFilterConfigProperty {
        /**
         * The threshold for this filter.
         *
         * @default - 0
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-guardrailcontextualgroundingfilterconfig.html#cfn-wisdom-aiguardrail-guardrailcontextualgroundingfilterconfig-threshold
         */
        readonly threshold: number;
        /**
         * The type of this filter.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiguardrail-guardrailcontextualgroundingfilterconfig.html#cfn-wisdom-aiguardrail-guardrailcontextualgroundingfilterconfig-type
         */
        readonly type: string;
    }
}
/**
 * Properties for defining a `CfnAIGuardrail`
 *
 * @struct
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiguardrail.html
 */
export interface CfnAIGuardrailProps {
    /**
     * The identifier of the Amazon Q in Connect assistant.
     *
     * Can be either the ID or the ARN. URLs cannot contain the ARN.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiguardrail.html#cfn-wisdom-aiguardrail-assistantid
     */
    readonly assistantId: string;
    /**
     * The message to return when the AI Guardrail blocks a prompt.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiguardrail.html#cfn-wisdom-aiguardrail-blockedinputmessaging
     */
    readonly blockedInputMessaging: string;
    /**
     * The message to return when the AI Guardrail blocks a model response.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiguardrail.html#cfn-wisdom-aiguardrail-blockedoutputsmessaging
     */
    readonly blockedOutputsMessaging: string;
    /**
     * Contains details about how to handle harmful content.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiguardrail.html#cfn-wisdom-aiguardrail-contentpolicyconfig
     */
    readonly contentPolicyConfig?: CfnAIGuardrail.AIGuardrailContentPolicyConfigProperty | cdk.IResolvable;
    /**
     * The policy configuration details for the AI Guardrail's contextual grounding policy.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiguardrail.html#cfn-wisdom-aiguardrail-contextualgroundingpolicyconfig
     */
    readonly contextualGroundingPolicyConfig?: CfnAIGuardrail.AIGuardrailContextualGroundingPolicyConfigProperty | cdk.IResolvable;
    /**
     * A description of the AI Guardrail.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiguardrail.html#cfn-wisdom-aiguardrail-description
     */
    readonly description?: string;
    /**
     * The name of the AI Guardrail.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiguardrail.html#cfn-wisdom-aiguardrail-name
     */
    readonly name?: string;
    /**
     * Contains details about PII entities and regular expressions to configure for the AI Guardrail.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiguardrail.html#cfn-wisdom-aiguardrail-sensitiveinformationpolicyconfig
     */
    readonly sensitiveInformationPolicyConfig?: CfnAIGuardrail.AIGuardrailSensitiveInformationPolicyConfigProperty | cdk.IResolvable;
    /**
     * The tags used to organize, track, or control access for this resource.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiguardrail.html#cfn-wisdom-aiguardrail-tags
     */
    readonly tags?: Record<string, string>;
    /**
     * Contains details about topics that the AI Guardrail should identify and deny.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiguardrail.html#cfn-wisdom-aiguardrail-topicpolicyconfig
     */
    readonly topicPolicyConfig?: CfnAIGuardrail.AIGuardrailTopicPolicyConfigProperty | cdk.IResolvable;
    /**
     * Contains details about the word policy to configured for the AI Guardrail.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiguardrail.html#cfn-wisdom-aiguardrail-wordpolicyconfig
     */
    readonly wordPolicyConfig?: CfnAIGuardrail.AIGuardrailWordPolicyConfigProperty | cdk.IResolvable;
}
/**
 * Creates an Amazon Q in Connect AI Guardrail version.
 *
 * @cloudformationResource AWS::Wisdom::AIGuardrailVersion
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiguardrailversion.html
 */
export declare class CfnAIGuardrailVersion extends cdk.CfnResource implements cdk.IInspectable {
    /**
     * The CloudFormation resource type name for this resource class.
     */
    static readonly CFN_RESOURCE_TYPE_NAME: string;
    /**
     * Build a CfnAIGuardrailVersion from CloudFormation properties
     *
     * A factory method that creates a new instance of this class from an object
     * containing the CloudFormation properties of this resource.
     * Used in the @aws-cdk/cloudformation-include module.
     *
     * @internal
     */
    static _fromCloudFormation(scope: constructs.Construct, id: string, resourceAttributes: any, options: cfn_parse.FromCloudFormationOptions): CfnAIGuardrailVersion;
    /**
     * The ARN of the AI guardrail version.
     *
     * @cloudformationAttribute AIGuardrailArn
     */
    readonly attrAiGuardrailArn: string;
    /**
     * The ID of the AI guardrail version.
     *
     * @cloudformationAttribute AIGuardrailVersionId
     */
    readonly attrAiGuardrailVersionId: string;
    /**
     * The ARN of the AI guardrail version assistant.
     *
     * @cloudformationAttribute AssistantArn
     */
    readonly attrAssistantArn: string;
    /**
     * The version number for this AI Guardrail version.
     *
     * @cloudformationAttribute VersionNumber
     */
    readonly attrVersionNumber: cdk.IResolvable;
    /**
     * The ID of the AI guardrail version.
     */
    aiGuardrailId: string;
    /**
     * The ID of the AI guardrail version assistant.
     */
    assistantId: string;
    /**
     * The modified time of the AI guardrail version in seconds.
     */
    modifiedTimeSeconds?: number;
    /**
     * @param scope Scope in which this resource is defined
     * @param id Construct identifier for this resource (unique in its scope)
     * @param props Resource properties
     */
    constructor(scope: constructs.Construct, id: string, props: CfnAIGuardrailVersionProps);
    protected get cfnProperties(): Record<string, any>;
    /**
     * Examines the CloudFormation resource and discloses attributes
     *
     * @param inspector tree inspector to collect and process attributes
     */
    inspect(inspector: cdk.TreeInspector): void;
    protected renderProperties(props: Record<string, any>): Record<string, any>;
}
/**
 * Properties for defining a `CfnAIGuardrailVersion`
 *
 * @struct
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiguardrailversion.html
 */
export interface CfnAIGuardrailVersionProps {
    /**
     * The ID of the AI guardrail version.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiguardrailversion.html#cfn-wisdom-aiguardrailversion-aiguardrailid
     */
    readonly aiGuardrailId: string;
    /**
     * The ID of the AI guardrail version assistant.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiguardrailversion.html#cfn-wisdom-aiguardrailversion-assistantid
     */
    readonly assistantId: string;
    /**
     * The modified time of the AI guardrail version in seconds.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiguardrailversion.html#cfn-wisdom-aiguardrailversion-modifiedtimeseconds
     */
    readonly modifiedTimeSeconds?: number;
}
/**
 * Creates an Amazon Q in Connect AI Prompt.
 *
 * @cloudformationResource AWS::Wisdom::AIPrompt
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiprompt.html
 */
export declare class CfnAIPrompt extends cdk.CfnResource implements cdk.IInspectable, cdk.ITaggableV2 {
    /**
     * The CloudFormation resource type name for this resource class.
     */
    static readonly CFN_RESOURCE_TYPE_NAME: string;
    /**
     * Build a CfnAIPrompt from CloudFormation properties
     *
     * A factory method that creates a new instance of this class from an object
     * containing the CloudFormation properties of this resource.
     * Used in the @aws-cdk/cloudformation-include module.
     *
     * @internal
     */
    static _fromCloudFormation(scope: constructs.Construct, id: string, resourceAttributes: any, options: cfn_parse.FromCloudFormationOptions): CfnAIPrompt;
    /**
     * The Amazon Resource Name (ARN) of the AI Prompt.
     *
     * @cloudformationAttribute AIPromptArn
     */
    readonly attrAiPromptArn: string;
    /**
     * The identifier of the Amazon Q in Connect AI prompt.
     *
     * @cloudformationAttribute AIPromptId
     */
    readonly attrAiPromptId: string;
    /**
     * The Amazon Resource Name (ARN) of the Amazon Q in Connect assistant.
     *
     * @cloudformationAttribute AssistantArn
     */
    readonly attrAssistantArn: string;
    /**
     * @cloudformationAttribute ModifiedTimeSeconds
     */
    readonly attrModifiedTimeSeconds: cdk.IResolvable;
    /**
     * The API format used for this AI Prompt.
     */
    apiFormat: string;
    /**
     * The identifier of the Amazon Q in Connect assistant.
     */
    assistantId?: string;
    /**
     * Tag Manager which manages the tags for this resource
     */
    readonly cdkTagManager: cdk.TagManager;
    /**
     * The description of the AI Prompt.
     */
    description?: string;
    /**
     * The identifier of the model used for this AI Prompt. The following model Ids are supported:.
     */
    modelId: string;
    /**
     * The name of the AI Prompt.
     */
    name?: string;
    /**
     * The tags used to organize, track, or control access for this resource.
     */
    tags?: Record<string, string>;
    /**
     * The configuration of the prompt template for this AI Prompt.
     */
    templateConfiguration: CfnAIPrompt.AIPromptTemplateConfigurationProperty | cdk.IResolvable;
    /**
     * The type of the prompt template for this AI Prompt.
     */
    templateType: string;
    /**
     * The type of this AI Prompt.
     */
    type: string;
    /**
     * @param scope Scope in which this resource is defined
     * @param id Construct identifier for this resource (unique in its scope)
     * @param props Resource properties
     */
    constructor(scope: constructs.Construct, id: string, props: CfnAIPromptProps);
    protected get cfnProperties(): Record<string, any>;
    /**
     * Examines the CloudFormation resource and discloses attributes
     *
     * @param inspector tree inspector to collect and process attributes
     */
    inspect(inspector: cdk.TreeInspector): void;
    protected renderProperties(props: Record<string, any>): Record<string, any>;
}
export declare namespace CfnAIPrompt {
    /**
     * A typed union that specifies the configuration for a prompt template based on its type.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiprompt-aiprompttemplateconfiguration.html
     */
    interface AIPromptTemplateConfigurationProperty {
        /**
         * The configuration for a prompt template that supports full textual prompt configuration using a YAML prompt.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiprompt-aiprompttemplateconfiguration.html#cfn-wisdom-aiprompt-aiprompttemplateconfiguration-textfullaipromptedittemplateconfiguration
         */
        readonly textFullAiPromptEditTemplateConfiguration: cdk.IResolvable | CfnAIPrompt.TextFullAIPromptEditTemplateConfigurationProperty;
    }
    /**
     * The configuration for a prompt template that supports full textual prompt configuration using a YAML prompt.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiprompt-textfullaipromptedittemplateconfiguration.html
     */
    interface TextFullAIPromptEditTemplateConfigurationProperty {
        /**
         * The YAML text for the AI Prompt template.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-aiprompt-textfullaipromptedittemplateconfiguration.html#cfn-wisdom-aiprompt-textfullaipromptedittemplateconfiguration-text
         */
        readonly text: string;
    }
}
/**
 * Properties for defining a `CfnAIPrompt`
 *
 * @struct
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiprompt.html
 */
export interface CfnAIPromptProps {
    /**
     * The API format used for this AI Prompt.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiprompt.html#cfn-wisdom-aiprompt-apiformat
     */
    readonly apiFormat: string;
    /**
     * The identifier of the Amazon Q in Connect assistant.
     *
     * Can be either the ID or the ARN. URLs cannot contain the ARN.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiprompt.html#cfn-wisdom-aiprompt-assistantid
     */
    readonly assistantId?: string;
    /**
     * The description of the AI Prompt.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiprompt.html#cfn-wisdom-aiprompt-description
     */
    readonly description?: string;
    /**
     * The identifier of the model used for this AI Prompt. The following model Ids are supported:.
     *
     * - `anthropic.claude-3-haiku--v1:0`
     * - `apac.amazon.nova-lite-v1:0`
     * - `apac.amazon.nova-micro-v1:0`
     * - `apac.amazon.nova-pro-v1:0`
     * - `apac.anthropic.claude-3-5-sonnet--v2:0`
     * - `apac.anthropic.claude-3-haiku-20240307-v1:0`
     * - `eu.amazon.nova-lite-v1:0`
     * - `eu.amazon.nova-micro-v1:0`
     * - `eu.amazon.nova-pro-v1:0`
     * - `eu.anthropic.claude-3-7-sonnet-20250219-v1:0`
     * - `eu.anthropic.claude-3-haiku-20240307-v1:0`
     * - `us.amazon.nova-lite-v1:0`
     * - `us.amazon.nova-micro-v1:0`
     * - `us.amazon.nova-pro-v1:0`
     * - `us.anthropic.claude-3-5-haiku-20241022-v1:0`
     * - `us.anthropic.claude-3-7-sonnet-20250219-v1:0`
     * - `us.anthropic.claude-3-haiku-20240307-v1:0`
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiprompt.html#cfn-wisdom-aiprompt-modelid
     */
    readonly modelId: string;
    /**
     * The name of the AI Prompt.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiprompt.html#cfn-wisdom-aiprompt-name
     */
    readonly name?: string;
    /**
     * The tags used to organize, track, or control access for this resource.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiprompt.html#cfn-wisdom-aiprompt-tags
     */
    readonly tags?: Record<string, string>;
    /**
     * The configuration of the prompt template for this AI Prompt.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiprompt.html#cfn-wisdom-aiprompt-templateconfiguration
     */
    readonly templateConfiguration: CfnAIPrompt.AIPromptTemplateConfigurationProperty | cdk.IResolvable;
    /**
     * The type of the prompt template for this AI Prompt.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiprompt.html#cfn-wisdom-aiprompt-templatetype
     */
    readonly templateType: string;
    /**
     * The type of this AI Prompt.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aiprompt.html#cfn-wisdom-aiprompt-type
     */
    readonly type: string;
}
/**
 * Creates an Amazon Q in Connect AI Prompt version.
 *
 * @cloudformationResource AWS::Wisdom::AIPromptVersion
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aipromptversion.html
 */
export declare class CfnAIPromptVersion extends cdk.CfnResource implements cdk.IInspectable {
    /**
     * The CloudFormation resource type name for this resource class.
     */
    static readonly CFN_RESOURCE_TYPE_NAME: string;
    /**
     * Build a CfnAIPromptVersion from CloudFormation properties
     *
     * A factory method that creates a new instance of this class from an object
     * containing the CloudFormation properties of this resource.
     * Used in the @aws-cdk/cloudformation-include module.
     *
     * @internal
     */
    static _fromCloudFormation(scope: constructs.Construct, id: string, resourceAttributes: any, options: cfn_parse.FromCloudFormationOptions): CfnAIPromptVersion;
    /**
     * The ARN of the AI prompt.
     *
     * @cloudformationAttribute AIPromptArn
     */
    readonly attrAiPromptArn: string;
    /**
     * @cloudformationAttribute AIPromptVersionId
     */
    readonly attrAiPromptVersionId: string;
    /**
     * @cloudformationAttribute AssistantArn
     */
    readonly attrAssistantArn: string;
    /**
     * The version number for this AI Prompt version.
     *
     * @cloudformationAttribute VersionNumber
     */
    readonly attrVersionNumber: cdk.IResolvable;
    /**
     * The identifier of the Amazon Q in Connect AI prompt.
     */
    aiPromptId: string;
    /**
     * The identifier of the Amazon Q in Connect assistant.
     */
    assistantId: string;
    /**
     * The time the AI Prompt version was last modified in seconds.
     */
    modifiedTimeSeconds?: number;
    /**
     * @param scope Scope in which this resource is defined
     * @param id Construct identifier for this resource (unique in its scope)
     * @param props Resource properties
     */
    constructor(scope: constructs.Construct, id: string, props: CfnAIPromptVersionProps);
    protected get cfnProperties(): Record<string, any>;
    /**
     * Examines the CloudFormation resource and discloses attributes
     *
     * @param inspector tree inspector to collect and process attributes
     */
    inspect(inspector: cdk.TreeInspector): void;
    protected renderProperties(props: Record<string, any>): Record<string, any>;
}
/**
 * Properties for defining a `CfnAIPromptVersion`
 *
 * @struct
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aipromptversion.html
 */
export interface CfnAIPromptVersionProps {
    /**
     * The identifier of the Amazon Q in Connect AI prompt.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aipromptversion.html#cfn-wisdom-aipromptversion-aipromptid
     */
    readonly aiPromptId: string;
    /**
     * The identifier of the Amazon Q in Connect assistant.
     *
     * Can be either the ID or the ARN. URLs cannot contain the ARN.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aipromptversion.html#cfn-wisdom-aipromptversion-assistantid
     */
    readonly assistantId: string;
    /**
     * The time the AI Prompt version was last modified in seconds.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-aipromptversion.html#cfn-wisdom-aipromptversion-modifiedtimeseconds
     */
    readonly modifiedTimeSeconds?: number;
}
/**
 * Creates an Amazon Q in Connect message template.
 *
 * The name of the message template has to be unique for each knowledge base. The channel subtype of the message template is immutable and cannot be modified after creation. After the message template is created, you can use the `$LATEST` qualifier to reference the created message template.
 *
 * @cloudformationResource AWS::Wisdom::MessageTemplate
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-messagetemplate.html
 */
export declare class CfnMessageTemplate extends cdk.CfnResource implements cdk.IInspectable, cdk.ITaggableV2 {
    /**
     * The CloudFormation resource type name for this resource class.
     */
    static readonly CFN_RESOURCE_TYPE_NAME: string;
    /**
     * Build a CfnMessageTemplate from CloudFormation properties
     *
     * A factory method that creates a new instance of this class from an object
     * containing the CloudFormation properties of this resource.
     * Used in the @aws-cdk/cloudformation-include module.
     *
     * @internal
     */
    static _fromCloudFormation(scope: constructs.Construct, id: string, resourceAttributes: any, options: cfn_parse.FromCloudFormationOptions): CfnMessageTemplate;
    /**
     * The Amazon Resource Name (ARN) of the message template.
     *
     * @cloudformationAttribute MessageTemplateArn
     */
    readonly attrMessageTemplateArn: string;
    /**
     * The checksum value of the message template content that is referenced by the `$LATEST` qualifier. It can be returned in `MessageTemplateData` or `ExtendedMessageTemplateData` . It’s calculated by content, language, `defaultAttributes` and `Attachments` of the message template.
     *
     * @cloudformationAttribute MessageTemplateContentSha256
     */
    readonly attrMessageTemplateContentSha256: string;
    /**
     * The identifier of the message template.
     *
     * @cloudformationAttribute MessageTemplateId
     */
    readonly attrMessageTemplateId: string;
    /**
     * Tag Manager which manages the tags for this resource
     */
    readonly cdkTagManager: cdk.TagManager;
    /**
     * The channel subtype this message template applies to.
     */
    channelSubtype: string;
    /**
     * The content of the message template.
     */
    content: CfnMessageTemplate.ContentProperty | cdk.IResolvable;
    /**
     * An object that specifies the default values to use for variables in the message template.
     */
    defaultAttributes?: cdk.IResolvable | CfnMessageTemplate.MessageTemplateAttributesProperty;
    /**
     * The description of the message template.
     */
    description?: string;
    /**
     * The configuration information of the external data source.
     */
    groupingConfiguration?: CfnMessageTemplate.GroupingConfigurationProperty | cdk.IResolvable;
    /**
     * The Amazon Resource Name (ARN) of the knowledge base.
     */
    knowledgeBaseArn: string;
    /**
     * The language code value for the language in which the quick response is written.
     */
    language?: string;
    /**
     * The name of the message template.
     */
    name: string;
    /**
     * The tags used to organize, track, or control access for this resource.
     */
    tags?: Array<cdk.CfnTag>;
    /**
     * @param scope Scope in which this resource is defined
     * @param id Construct identifier for this resource (unique in its scope)
     * @param props Resource properties
     */
    constructor(scope: constructs.Construct, id: string, props: CfnMessageTemplateProps);
    protected get cfnProperties(): Record<string, any>;
    /**
     * Examines the CloudFormation resource and discloses attributes
     *
     * @param inspector tree inspector to collect and process attributes
     */
    inspect(inspector: cdk.TreeInspector): void;
    protected renderProperties(props: Record<string, any>): Record<string, any>;
}
export declare namespace CfnMessageTemplate {
    /**
     * The content of the message template.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-content.html
     */
    interface ContentProperty {
        /**
         * The content of the message template that applies to the email channel subtype.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-content.html#cfn-wisdom-messagetemplate-content-emailmessagetemplatecontent
         */
        readonly emailMessageTemplateContent?: CfnMessageTemplate.EmailMessageTemplateContentProperty | cdk.IResolvable;
        /**
         * The content of message template that applies to SMS channel subtype.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-content.html#cfn-wisdom-messagetemplate-content-smsmessagetemplatecontent
         */
        readonly smsMessageTemplateContent?: cdk.IResolvable | CfnMessageTemplate.SmsMessageTemplateContentProperty;
    }
    /**
     * The content of the message template that applies to the email channel subtype.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-emailmessagetemplatecontent.html
     */
    interface EmailMessageTemplateContentProperty {
        /**
         * The body to use in email messages.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-emailmessagetemplatecontent.html#cfn-wisdom-messagetemplate-emailmessagetemplatecontent-body
         */
        readonly body: CfnMessageTemplate.EmailMessageTemplateContentBodyProperty | cdk.IResolvable;
        /**
         * The email headers to include in email messages.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-emailmessagetemplatecontent.html#cfn-wisdom-messagetemplate-emailmessagetemplatecontent-headers
         */
        readonly headers: Array<CfnMessageTemplate.EmailMessageTemplateHeaderProperty | cdk.IResolvable> | cdk.IResolvable;
        /**
         * The subject line, or title, to use in email messages.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-emailmessagetemplatecontent.html#cfn-wisdom-messagetemplate-emailmessagetemplatecontent-subject
         */
        readonly subject: string;
    }
    /**
     * The body to use in email messages.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-emailmessagetemplatecontentbody.html
     */
    interface EmailMessageTemplateContentBodyProperty {
        /**
         * The message body, in HTML format, to use in email messages that are based on the message template.
         *
         * We recommend using HTML format for email clients that render HTML content. You can include links, formatted text, and more in an HTML message.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-emailmessagetemplatecontentbody.html#cfn-wisdom-messagetemplate-emailmessagetemplatecontentbody-html
         */
        readonly html?: cdk.IResolvable | CfnMessageTemplate.MessageTemplateBodyContentProviderProperty;
        /**
         * The message body, in plain text format, to use in email messages that are based on the message template.
         *
         * We recommend using plain text format for email clients that don't render HTML content and clients that are connected to high-latency networks, such as mobile devices.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-emailmessagetemplatecontentbody.html#cfn-wisdom-messagetemplate-emailmessagetemplatecontentbody-plaintext
         */
        readonly plainText?: cdk.IResolvable | CfnMessageTemplate.MessageTemplateBodyContentProviderProperty;
    }
    /**
     * The container of the message template body.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-messagetemplatebodycontentprovider.html
     */
    interface MessageTemplateBodyContentProviderProperty {
        /**
         * The content of the message template.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-messagetemplatebodycontentprovider.html#cfn-wisdom-messagetemplate-messagetemplatebodycontentprovider-content
         */
        readonly content?: string;
    }
    /**
     * The email headers to include in email messages.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-emailmessagetemplateheader.html
     */
    interface EmailMessageTemplateHeaderProperty {
        /**
         * The name of the email header.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-emailmessagetemplateheader.html#cfn-wisdom-messagetemplate-emailmessagetemplateheader-name
         */
        readonly name?: string;
        /**
         * The value of the email header.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-emailmessagetemplateheader.html#cfn-wisdom-messagetemplate-emailmessagetemplateheader-value
         */
        readonly value?: string;
    }
    /**
     * The content of the message template that applies to the SMS channel subtype.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-smsmessagetemplatecontent.html
     */
    interface SmsMessageTemplateContentProperty {
        /**
         * The body to use in SMS messages.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-smsmessagetemplatecontent.html#cfn-wisdom-messagetemplate-smsmessagetemplatecontent-body
         */
        readonly body: cdk.IResolvable | CfnMessageTemplate.SmsMessageTemplateContentBodyProperty;
    }
    /**
     * The body to use in SMS messages.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-smsmessagetemplatecontentbody.html
     */
    interface SmsMessageTemplateContentBodyProperty {
        /**
         * The message body to use in SMS messages.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-smsmessagetemplatecontentbody.html#cfn-wisdom-messagetemplate-smsmessagetemplatecontentbody-plaintext
         */
        readonly plainText?: cdk.IResolvable | CfnMessageTemplate.MessageTemplateBodyContentProviderProperty;
    }
    /**
     * The configuration information of the grouping of Amazon Q in Connect users.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-groupingconfiguration.html
     */
    interface GroupingConfigurationProperty {
        /**
         * The criteria used for grouping Amazon Q in Connect users.
         *
         * The following is the list of supported criteria values.
         *
         * - `RoutingProfileArn` : Grouping the users by their [Amazon Connect routing profile ARN](https://docs.aws.amazon.com/connect/latest/APIReference/API_RoutingProfile.html) . User should have [SearchRoutingProfile](https://docs.aws.amazon.com/connect/latest/APIReference/API_SearchRoutingProfiles.html) and [DescribeRoutingProfile](https://docs.aws.amazon.com/connect/latest/APIReference/API_DescribeRoutingProfile.html) permissions when setting criteria to this value.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-groupingconfiguration.html#cfn-wisdom-messagetemplate-groupingconfiguration-criteria
         */
        readonly criteria: string;
        /**
         * The list of values that define different groups of Amazon Q in Connect users.
         *
         * - When setting `criteria` to `RoutingProfileArn` , you need to provide a list of ARNs of [Amazon Connect routing profiles](https://docs.aws.amazon.com/connect/latest/APIReference/API_RoutingProfile.html) as values of this parameter.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-groupingconfiguration.html#cfn-wisdom-messagetemplate-groupingconfiguration-values
         */
        readonly values: Array<string>;
    }
    /**
     * The attributes that are used with the message template.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-messagetemplateattributes.html
     */
    interface MessageTemplateAttributesProperty {
        /**
         * The agent attributes that are used with the message template.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-messagetemplateattributes.html#cfn-wisdom-messagetemplate-messagetemplateattributes-agentattributes
         */
        readonly agentAttributes?: CfnMessageTemplate.AgentAttributesProperty | cdk.IResolvable;
        /**
         * The custom attributes that are used with the message template.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-messagetemplateattributes.html#cfn-wisdom-messagetemplate-messagetemplateattributes-customattributes
         */
        readonly customAttributes?: cdk.IResolvable | Record<string, string>;
        /**
         * The customer profile attributes that are used with the message template.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-messagetemplateattributes.html#cfn-wisdom-messagetemplate-messagetemplateattributes-customerprofileattributes
         */
        readonly customerProfileAttributes?: CfnMessageTemplate.CustomerProfileAttributesProperty | cdk.IResolvable;
        /**
         * The system attributes that are used with the message template.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-messagetemplateattributes.html#cfn-wisdom-messagetemplate-messagetemplateattributes-systemattributes
         */
        readonly systemAttributes?: cdk.IResolvable | CfnMessageTemplate.SystemAttributesProperty;
    }
    /**
     * The system attributes that are used with the message template.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-systemattributes.html
     */
    interface SystemAttributesProperty {
        /**
         * The CustomerEndpoint attribute.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-systemattributes.html#cfn-wisdom-messagetemplate-systemattributes-customerendpoint
         */
        readonly customerEndpoint?: cdk.IResolvable | CfnMessageTemplate.SystemEndpointAttributesProperty;
        /**
         * The name of the task.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-systemattributes.html#cfn-wisdom-messagetemplate-systemattributes-name
         */
        readonly name?: string;
        /**
         * The SystemEndpoint attribute.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-systemattributes.html#cfn-wisdom-messagetemplate-systemattributes-systemendpoint
         */
        readonly systemEndpoint?: cdk.IResolvable | CfnMessageTemplate.SystemEndpointAttributesProperty;
    }
    /**
     * The system endpoint attributes that are used with the message template.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-systemendpointattributes.html
     */
    interface SystemEndpointAttributesProperty {
        /**
         * The customer's phone number if used with `customerEndpoint` , or the number the customer dialed to call your contact center if used with `systemEndpoint` .
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-systemendpointattributes.html#cfn-wisdom-messagetemplate-systemendpointattributes-address
         */
        readonly address?: string;
    }
    /**
     * Information about an agent.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-agentattributes.html
     */
    interface AgentAttributesProperty {
        /**
         * The agent’s first name as entered in their Amazon Connect user account.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-agentattributes.html#cfn-wisdom-messagetemplate-agentattributes-firstname
         */
        readonly firstName?: string;
        /**
         * The agent’s last name as entered in their Amazon Connect user account.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-agentattributes.html#cfn-wisdom-messagetemplate-agentattributes-lastname
         */
        readonly lastName?: string;
    }
    /**
     * The customer profile attributes that are used with the message template.
     *
     * @struct
     * @stability external
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html
     */
    interface CustomerProfileAttributesProperty {
        /**
         * A unique account number that you have given to the customer.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-accountnumber
         */
        readonly accountNumber?: string;
        /**
         * Any additional information relevant to the customer's profile.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-additionalinformation
         */
        readonly additionalInformation?: string;
        /**
         * The first line of a customer address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-address1
         */
        readonly address1?: string;
        /**
         * The second line of a customer address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-address2
         */
        readonly address2?: string;
        /**
         * The third line of a customer address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-address3
         */
        readonly address3?: string;
        /**
         * The fourth line of a customer address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-address4
         */
        readonly address4?: string;
        /**
         * The first line of a customer’s billing address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-billingaddress1
         */
        readonly billingAddress1?: string;
        /**
         * The second line of a customer’s billing address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-billingaddress2
         */
        readonly billingAddress2?: string;
        /**
         * The third line of a customer’s billing address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-billingaddress3
         */
        readonly billingAddress3?: string;
        /**
         * The fourth line of a customer’s billing address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-billingaddress4
         */
        readonly billingAddress4?: string;
        /**
         * The city of a customer’s billing address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-billingcity
         */
        readonly billingCity?: string;
        /**
         * The country of a customer’s billing address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-billingcountry
         */
        readonly billingCountry?: string;
        /**
         * The county of a customer’s billing address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-billingcounty
         */
        readonly billingCounty?: string;
        /**
         * The postal code of a customer’s billing address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-billingpostalcode
         */
        readonly billingPostalCode?: string;
        /**
         * The province of a customer’s billing address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-billingprovince
         */
        readonly billingProvince?: string;
        /**
         * The state of a customer’s billing address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-billingstate
         */
        readonly billingState?: string;
        /**
         * The customer's birth date.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-birthdate
         */
        readonly birthDate?: string;
        /**
         * The customer's business email address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-businessemailaddress
         */
        readonly businessEmailAddress?: string;
        /**
         * The name of the customer's business.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-businessname
         */
        readonly businessName?: string;
        /**
         * The customer's business phone number.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-businessphonenumber
         */
        readonly businessPhoneNumber?: string;
        /**
         * The city in which a customer lives.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-city
         */
        readonly city?: string;
        /**
         * The country in which a customer lives.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-country
         */
        readonly country?: string;
        /**
         * The county in which a customer lives.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-county
         */
        readonly county?: string;
        /**
         * The custom attributes in customer profile attributes.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-custom
         */
        readonly custom?: cdk.IResolvable | Record<string, string>;
        /**
         * The customer's email address, which has not been specified as a personal or business address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-emailaddress
         */
        readonly emailAddress?: string;
        /**
         * The customer's first name.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-firstname
         */
        readonly firstName?: string;
        /**
         * The customer's gender.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-gender
         */
        readonly gender?: string;
        /**
         * The customer's mobile phone number.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-homephonenumber
         */
        readonly homePhoneNumber?: string;
        /**
         * The customer's last name.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-lastname
         */
        readonly lastName?: string;
        /**
         * The first line of a customer’s mailing address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-mailingaddress1
         */
        readonly mailingAddress1?: string;
        /**
         * The second line of a customer’s mailing address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-mailingaddress2
         */
        readonly mailingAddress2?: string;
        /**
         * The third line of a customer’s mailing address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-mailingaddress3
         */
        readonly mailingAddress3?: string;
        /**
         * The fourth line of a customer’s mailing address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-mailingaddress4
         */
        readonly mailingAddress4?: string;
        /**
         * The city of a customer’s mailing address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-mailingcity
         */
        readonly mailingCity?: string;
        /**
         * The country of a customer’s mailing address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-mailingcountry
         */
        readonly mailingCountry?: string;
        /**
         * The county of a customer’s mailing address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-mailingcounty
         */
        readonly mailingCounty?: string;
        /**
         * The postal code of a customer’s mailing address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-mailingpostalcode
         */
        readonly mailingPostalCode?: string;
        /**
         * The province of a customer’s mailing address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-mailingprovince
         */
        readonly mailingProvince?: string;
        /**
         * The state of a customer’s mailing address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-mailingstate
         */
        readonly mailingState?: string;
        /**
         * The customer's middle name.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-middlename
         */
        readonly middleName?: string;
        /**
         * The customer's mobile phone number.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-mobilephonenumber
         */
        readonly mobilePhoneNumber?: string;
        /**
         * The customer's party type.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-partytype
         */
        readonly partyType?: string;
        /**
         * The customer's phone number, which has not been specified as a mobile, home, or business number.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-phonenumber
         */
        readonly phoneNumber?: string;
        /**
         * The postal code of a customer address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-postalcode
         */
        readonly postalCode?: string;
        /**
         * The ARN of a customer profile.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-profilearn
         */
        readonly profileArn?: string;
        /**
         * The unique identifier of a customer profile.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-profileid
         */
        readonly profileId?: string;
        /**
         * The province in which a customer lives.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-province
         */
        readonly province?: string;
        /**
         * The first line of a customer’s shipping address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-shippingaddress1
         */
        readonly shippingAddress1?: string;
        /**
         * The second line of a customer’s shipping address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-shippingaddress2
         */
        readonly shippingAddress2?: string;
        /**
         * The third line of a customer’s shipping address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-shippingaddress3
         */
        readonly shippingAddress3?: string;
        /**
         * The fourth line of a customer’s shipping address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-shippingaddress4
         */
        readonly shippingAddress4?: string;
        /**
         * The city of a customer’s shipping address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-shippingcity
         */
        readonly shippingCity?: string;
        /**
         * The country of a customer’s shipping address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-shippingcountry
         */
        readonly shippingCountry?: string;
        /**
         * The county of a customer’s shipping address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-shippingcounty
         */
        readonly shippingCounty?: string;
        /**
         * The postal code of a customer’s shipping address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-shippingpostalcode
         */
        readonly shippingPostalCode?: string;
        /**
         * The province of a customer’s shipping address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-shippingprovince
         */
        readonly shippingProvince?: string;
        /**
         * The state of a customer’s shipping address.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-shippingstate
         */
        readonly shippingState?: string;
        /**
         * The state in which a customer lives.
         *
         * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wisdom-messagetemplate-customerprofileattributes.html#cfn-wisdom-messagetemplate-customerprofileattributes-state
         */
        readonly state?: string;
    }
}
/**
 * Properties for defining a `CfnMessageTemplate`
 *
 * @struct
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-messagetemplate.html
 */
export interface CfnMessageTemplateProps {
    /**
     * The channel subtype this message template applies to.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-messagetemplate.html#cfn-wisdom-messagetemplate-channelsubtype
     */
    readonly channelSubtype: string;
    /**
     * The content of the message template.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-messagetemplate.html#cfn-wisdom-messagetemplate-content
     */
    readonly content: CfnMessageTemplate.ContentProperty | cdk.IResolvable;
    /**
     * An object that specifies the default values to use for variables in the message template.
     *
     * This object contains different categories of key-value pairs. Each key defines a variable or placeholder in the message template. The corresponding value defines the default value for that variable.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-messagetemplate.html#cfn-wisdom-messagetemplate-defaultattributes
     */
    readonly defaultAttributes?: cdk.IResolvable | CfnMessageTemplate.MessageTemplateAttributesProperty;
    /**
     * The description of the message template.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-messagetemplate.html#cfn-wisdom-messagetemplate-description
     */
    readonly description?: string;
    /**
     * The configuration information of the external data source.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-messagetemplate.html#cfn-wisdom-messagetemplate-groupingconfiguration
     */
    readonly groupingConfiguration?: CfnMessageTemplate.GroupingConfigurationProperty | cdk.IResolvable;
    /**
     * The Amazon Resource Name (ARN) of the knowledge base.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-messagetemplate.html#cfn-wisdom-messagetemplate-knowledgebasearn
     */
    readonly knowledgeBaseArn: string;
    /**
     * The language code value for the language in which the quick response is written.
     *
     * The supported language codes include `de_DE` , `en_US` , `es_ES` , `fr_FR` , `id_ID` , `it_IT` , `ja_JP` , `ko_KR` , `pt_BR` , `zh_CN` , `zh_TW`
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-messagetemplate.html#cfn-wisdom-messagetemplate-language
     */
    readonly language?: string;
    /**
     * The name of the message template.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-messagetemplate.html#cfn-wisdom-messagetemplate-name
     */
    readonly name: string;
    /**
     * The tags used to organize, track, or control access for this resource.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-messagetemplate.html#cfn-wisdom-messagetemplate-tags
     */
    readonly tags?: Array<cdk.CfnTag>;
}
/**
 * Creates a new Amazon Q in Connect message template version from the current content and configuration of a message template.
 *
 * Versions are immutable and monotonically increasing. Once a version is created, you can reference a specific version of the message template by passing in `<messageTemplateArn>:<versionNumber>` as the message template identifier. An error is displayed if the supplied `messageTemplateContentSha256` is different from the `messageTemplateContentSha256` of the message template with `$LATEST` qualifier. If multiple `CreateMessageTemplateVersion` requests are made while the message template remains the same, only the first invocation creates a new version and the succeeding requests will return the same response as the first invocation.
 *
 * @cloudformationResource AWS::Wisdom::MessageTemplateVersion
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-messagetemplateversion.html
 */
export declare class CfnMessageTemplateVersion extends cdk.CfnResource implements cdk.IInspectable {
    /**
     * The CloudFormation resource type name for this resource class.
     */
    static readonly CFN_RESOURCE_TYPE_NAME: string;
    /**
     * Build a CfnMessageTemplateVersion from CloudFormation properties
     *
     * A factory method that creates a new instance of this class from an object
     * containing the CloudFormation properties of this resource.
     * Used in the @aws-cdk/cloudformation-include module.
     *
     * @internal
     */
    static _fromCloudFormation(scope: constructs.Construct, id: string, resourceAttributes: any, options: cfn_parse.FromCloudFormationOptions): CfnMessageTemplateVersion;
    /**
     * The Amazon Resource Name (ARN) of the Message Template Version.
     *
     * @cloudformationAttribute MessageTemplateVersionArn
     */
    readonly attrMessageTemplateVersionArn: string;
    /**
     * The version number for this Message Template Version.
     *
     * @cloudformationAttribute MessageTemplateVersionNumber
     */
    readonly attrMessageTemplateVersionNumber: cdk.IResolvable;
    /**
     * The Amazon Resource Name (ARN) of the message template.
     */
    messageTemplateArn: string;
    /**
     * The content SHA256 of the message template.
     */
    messageTemplateContentSha256?: string;
    /**
     * @param scope Scope in which this resource is defined
     * @param id Construct identifier for this resource (unique in its scope)
     * @param props Resource properties
     */
    constructor(scope: constructs.Construct, id: string, props: CfnMessageTemplateVersionProps);
    protected get cfnProperties(): Record<string, any>;
    /**
     * Examines the CloudFormation resource and discloses attributes
     *
     * @param inspector tree inspector to collect and process attributes
     */
    inspect(inspector: cdk.TreeInspector): void;
    protected renderProperties(props: Record<string, any>): Record<string, any>;
}
/**
 * Properties for defining a `CfnMessageTemplateVersion`
 *
 * @struct
 * @stability external
 * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-messagetemplateversion.html
 */
export interface CfnMessageTemplateVersionProps {
    /**
     * The Amazon Resource Name (ARN) of the message template.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-messagetemplateversion.html#cfn-wisdom-messagetemplateversion-messagetemplatearn
     */
    readonly messageTemplateArn: string;
    /**
     * The content SHA256 of the message template.
     *
     * @see http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wisdom-messagetemplateversion.html#cfn-wisdom-messagetemplateversion-messagetemplatecontentsha256
     */
    readonly messageTemplateContentSha256?: string;
}
