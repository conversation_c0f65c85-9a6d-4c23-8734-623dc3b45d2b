import * as cdk from 'aws-cdk-lib';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import * as elbv2 from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import * as rds from 'aws-cdk-lib/aws-rds';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as cloudfront from 'aws-cdk-lib/aws-cloudfront';
import * as origins from 'aws-cdk-lib/aws-cloudfront-origins';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as ecr from 'aws-cdk-lib/aws-ecr';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Construct } from 'constructs';

export class JerseyColoursStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);

    // VPC
    const vpc = new ec2.Vpc(this, 'JerseyColoursVPC', {
      maxAzs: 2,
      natGateways: 1,
      subnetConfiguration: [
        {
          cidrMask: 24,
          name: 'Public',
          subnetType: ec2.SubnetType.PUBLIC,
        },
        {
          cidrMask: 24,
          name: 'Private',
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
        },
        {
          cidrMask: 28,
          name: 'Database',
          subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
        },
      ],
    });

    // S3 Bucket for pattern files
    const patternBucket = new s3.Bucket(this, 'PatternFilesBucket', {
      bucketName: `jersey-colours-patterns-${this.account}-${this.region}`,
      versioned: true,
      encryption: s3.BucketEncryption.S3_MANAGED,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      cors: [{
        allowedMethods: [s3.HttpMethods.GET, s3.HttpMethods.POST, s3.HttpMethods.PUT],
        allowedOrigins: ['*'],
        allowedHeaders: ['*'],
        maxAge: 3000,
      }],
      lifecycleRules: [{
        id: 'DeleteOldVersions',
        noncurrentVersionExpiration: cdk.Duration.days(30),
      }],
    });

    // RDS PostgreSQL Database
    const dbCredentials = rds.Credentials.fromGeneratedSecret('postgres', {
      secretName: 'jersey-colours-db-credentials',
    });

    const database = new rds.DatabaseInstance(this, 'JerseyColoursDB', {
      engine: rds.DatabaseInstanceEngine.postgres({
        version: rds.PostgresEngineVersion.VER_15,
      }),
      instanceType: ec2.InstanceType.of(ec2.InstanceClass.T3, ec2.InstanceSize.MICRO),
      vpc,
      vpcSubnets: {
        subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
      },
      credentials: dbCredentials,
      multiAz: false,
      allocatedStorage: 20,
      maxAllocatedStorage: 100,
      storageEncrypted: true,
      deletionProtection: false,
      backupRetention: cdk.Duration.days(7),
      deleteAutomatedBackups: true,
      databaseName: 'jersey_colours',
    });

    // ECR Repositories
    const apiRepository = new ecr.Repository(this, 'ApiRepository', {
      repositoryName: 'jersey-colours-api',
      imageScanOnPush: true,
      lifecycleRules: [{
        maxImageCount: 10,
      }],
    });

    const frontendRepository = new ecr.Repository(this, 'FrontendRepository', {
      repositoryName: 'jersey-colours-frontend',
      imageScanOnPush: true,
      lifecycleRules: [{
        maxImageCount: 10,
      }],
    });

    // ECS Cluster
    const cluster = new ecs.Cluster(this, 'JerseyColoursCluster', {
      vpc,
      containerInsightsV2: ecs.ContainerInsights.ENABLED,
    });

    // Task execution role
    const taskExecutionRole = new iam.Role(this, 'TaskExecutionRole', {
      assumedBy: new iam.ServicePrincipal('ecs-tasks.amazonaws.com'),
      managedPolicies: [
        iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AmazonECSTaskExecutionRolePolicy'),
      ],
    });

    // Task role for API
    const apiTaskRole = new iam.Role(this, 'ApiTaskRole', {
      assumedBy: new iam.ServicePrincipal('ecs-tasks.amazonaws.com'),
    });

    // Grant S3 permissions to API task
    patternBucket.grantReadWrite(apiTaskRole);
    database.secret?.grantRead(apiTaskRole);

    // API Load Balancer
    const apiLoadBalancer = new elbv2.ApplicationLoadBalancer(this, 'ApiServiceLB', {
      vpc,
      internetFacing: true,
    });

    const apiTargetGroup = new elbv2.ApplicationTargetGroup(this, 'ApiServiceTargetGroup', {
      vpc,
      port: 80,
      protocol: elbv2.ApplicationProtocol.HTTP,
      targetType: elbv2.TargetType.IP,
    });

    apiLoadBalancer.addListener('ApiServiceListener', {
      port: 80,
      defaultTargetGroups: [apiTargetGroup],
    });

    // API Task Definition
    const apiTaskDefinition = new ecs.FargateTaskDefinition(this, 'ApiServiceTaskDef', {
      memoryLimitMiB: 512,
      cpu: 256,
      executionRole: taskExecutionRole,
      taskRole: apiTaskRole,
    });

    // Explicitly remove any inference accelerators from the task definition
    const apiTaskDefCfn = apiTaskDefinition.node.defaultChild as ecs.CfnTaskDefinition;
    apiTaskDefCfn.addPropertyDeletionOverride('InferenceAccelerators');

    const apiLogGroup = new logs.LogGroup(this, 'ApiServiceLogGroup', {
      retention: logs.RetentionDays.ONE_WEEK,
    });

    apiTaskDefinition.addContainer('web', {
      image: ecs.ContainerImage.fromEcrRepository(apiRepository, 'latest'),
      portMappings: [{ containerPort: 3000 }],
      environment: {
        S3_BUCKET: patternBucket.bucketName,
        AWS_REGION: this.region,
      },
      secrets: {
        DATABASE_URL: ecs.Secret.fromSecretsManager(database.secret!, 'engine'),
      },
      logging: ecs.LogDrivers.awsLogs({
        logGroup: apiLogGroup,
        streamPrefix: 'jersey-colours-api',
      }),
    });

    // API Service
    const apiService = new ecs.FargateService(this, 'ApiService', {
      cluster,
      taskDefinition: apiTaskDefinition,
      desiredCount: 2,
      assignPublicIp: false,
      minHealthyPercent: 50,
      maxHealthyPercent: 200,
    });

    apiService.attachToApplicationTargetGroup(apiTargetGroup);

    // Allow API to connect to database
    database.connections.allowFrom(apiService, ec2.Port.tcp(5432));

    // Frontend Load Balancer
    const frontendLoadBalancer = new elbv2.ApplicationLoadBalancer(this, 'FrontendServiceLB', {
      vpc,
      internetFacing: true,
    });

    const frontendTargetGroup = new elbv2.ApplicationTargetGroup(this, 'FrontendServiceTargetGroup', {
      vpc,
      port: 80,
      protocol: elbv2.ApplicationProtocol.HTTP,
      targetType: elbv2.TargetType.IP,
    });

    frontendLoadBalancer.addListener('FrontendServiceListener', {
      port: 80,
      defaultTargetGroups: [frontendTargetGroup],
    });

    // Frontend Task Definition
    const frontendTaskDefinition = new ecs.FargateTaskDefinition(this, 'FrontendServiceTaskDef', {
      memoryLimitMiB: 256,
      cpu: 256,
      executionRole: taskExecutionRole,
    });

    // Explicitly remove any inference accelerators from the task definition
    const frontendTaskDefCfn = frontendTaskDefinition.node.defaultChild as ecs.CfnTaskDefinition;
    frontendTaskDefCfn.addPropertyDeletionOverride('InferenceAccelerators');

    const frontendLogGroup = new logs.LogGroup(this, 'FrontendServiceLogGroup', {
      retention: logs.RetentionDays.ONE_WEEK,
    });

    frontendTaskDefinition.addContainer('web', {
      image: ecs.ContainerImage.fromEcrRepository(frontendRepository, 'latest'),
      portMappings: [{ containerPort: 80 }],
      environment: {
        API_URL: `http://${apiLoadBalancer.loadBalancerDnsName}`,
      },
      logging: ecs.LogDrivers.awsLogs({
        logGroup: frontendLogGroup,
        streamPrefix: 'jersey-colours-frontend',
      }),
    });

    // Frontend Service
    const frontendService = new ecs.FargateService(this, 'FrontendService', {
      cluster,
      taskDefinition: frontendTaskDefinition,
      desiredCount: 2,
      assignPublicIp: false,
      minHealthyPercent: 50,
      maxHealthyPercent: 200,
    });

    frontendService.attachToApplicationTargetGroup(frontendTargetGroup);

    // Lambda function for SVG generation
    const svgGeneratorFunction = new lambda.Function(this, 'SVGGeneratorFunction', {
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('../svg-generator/dist'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 512,
      environment: {
        NODE_ENV: 'production',
      },
    });

    // API Gateway for SVG generation
    const svgApi = new apigateway.RestApi(this, 'SVGGeneratorAPI', {
      restApiName: 'Jersey Colours SVG Generator',
      description: 'API for generating jersey SVGs',
      defaultCorsPreflightOptions: {
        allowOrigins: apigateway.Cors.ALL_ORIGINS,
        allowMethods: apigateway.Cors.ALL_METHODS,
        allowHeaders: ['Content-Type', 'Authorization'],
      },
    });

    const svgIntegration = new apigateway.LambdaIntegration(svgGeneratorFunction);
    svgApi.root.addResource('generate').addMethod('POST', svgIntegration);

    // CloudFront Distribution
    const distribution = new cloudfront.Distribution(this, 'JerseyColoursDistribution', {
      defaultBehavior: {
        origin: new origins.LoadBalancerV2Origin(frontendLoadBalancer, {
          protocolPolicy: cloudfront.OriginProtocolPolicy.HTTP_ONLY,
        }),
        viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
        cachePolicy: cloudfront.CachePolicy.CACHING_OPTIMIZED,
      },
      additionalBehaviors: {
        '/api/*': {
          origin: new origins.LoadBalancerV2Origin(apiLoadBalancer, {
            protocolPolicy: cloudfront.OriginProtocolPolicy.HTTP_ONLY,
          }),
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
          cachePolicy: cloudfront.CachePolicy.CACHING_DISABLED,
          allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
        },
        '/svg/*': {
          origin: new origins.RestApiOrigin(svgApi),
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
          cachePolicy: cloudfront.CachePolicy.CACHING_OPTIMIZED,
        },
        '/patterns/*': {
          origin: origins.S3BucketOrigin.withOriginAccessIdentity(patternBucket),
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
          cachePolicy: cloudfront.CachePolicy.CACHING_OPTIMIZED,
        },
      },
      priceClass: cloudfront.PriceClass.PRICE_CLASS_100,
    });

    // Outputs
    new cdk.CfnOutput(this, 'DatabaseEndpoint', {
      value: database.instanceEndpoint.hostname,
      description: 'RDS Database Endpoint',
    });

    new cdk.CfnOutput(this, 'ApiLoadBalancerDNS', {
      value: apiLoadBalancer.loadBalancerDnsName,
      description: 'API Load Balancer DNS',
    });

    new cdk.CfnOutput(this, 'FrontendLoadBalancerDNS', {
      value: frontendLoadBalancer.loadBalancerDnsName,
      description: 'Frontend Load Balancer DNS',
    });

    new cdk.CfnOutput(this, 'CloudFrontDomainName', {
      value: distribution.distributionDomainName,
      description: 'CloudFront Distribution Domain Name',
    });

    new cdk.CfnOutput(this, 'S3BucketName', {
      value: patternBucket.bucketName,
      description: 'S3 Bucket for Pattern Files',
    });

    new cdk.CfnOutput(this, 'SVGGeneratorAPIEndpoint', {
      value: svgApi.url,
      description: 'SVG Generator API Endpoint',
    });

    new cdk.CfnOutput(this, 'ApiRepositoryURI', {
      value: apiRepository.repositoryUri,
      description: 'ECR Repository URI for API',
    });

    new cdk.CfnOutput(this, 'FrontendRepositoryURI', {
      value: frontendRepository.repositoryUri,
      description: 'ECR Repository URI for Frontend',
    });
  }
}
