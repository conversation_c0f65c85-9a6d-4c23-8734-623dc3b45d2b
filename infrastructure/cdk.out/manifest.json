{"version": "45.0.0", "artifacts": {"JerseyColoursStack.assets": {"type": "cdk:asset-manifest", "properties": {"file": "JerseyColoursStack.assets.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "JerseyColoursStack": {"type": "aws:cloudformation:stack", "environment": "aws://699965966019/ap-southeast-2", "properties": {"templateFile": "JerseyColoursStack.template.json", "terminationProtection": false, "validateOnSynth": false, "assumeRoleArn": "arn:${AWS::Partition}:iam::699965966019:role/cdk-hnb659fds-deploy-role-699965966019-ap-southeast-2", "cloudFormationExecutionRoleArn": "arn:${AWS::Partition}:iam::699965966019:role/cdk-hnb659fds-cfn-exec-role-699965966019-ap-southeast-2", "stackTemplateAssetObjectUrl": "s3://cdk-hnb659fds-assets-699965966019-ap-southeast-2/dfe004391d9322b61a74c5a84bfbc860e0b73b50d0d9f99486ef0d019a11134e.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version", "additionalDependencies": ["JerseyColoursStack.assets"], "lookupRole": {"arn": "arn:${AWS::Partition}:iam::699965966019:role/cdk-hnb659fds-lookup-role-699965966019-ap-southeast-2", "requiresBootstrapStackVersion": 8, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "dependencies": ["JerseyColoursStack.assets"], "metadata": {"/JerseyColoursStack/JerseyColoursVPC/Resource": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCFE3584A6"}], "/JerseyColoursStack/JerseyColoursVPC/PublicSubnet1/Subnet": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCPublicSubnet1Subnet723163B5"}], "/JerseyColoursStack/JerseyColoursVPC/PublicSubnet1/RouteTable": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCPublicSubnet1RouteTableD9ADFDC6"}], "/JerseyColoursStack/JerseyColoursVPC/PublicSubnet1/RouteTableAssociation": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCPublicSubnet1RouteTableAssociationC3BBF53E"}], "/JerseyColoursStack/JerseyColoursVPC/PublicSubnet1/DefaultRoute": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCPublicSubnet1DefaultRoute5CE7643B"}], "/JerseyColoursStack/JerseyColoursVPC/PublicSubnet1/EIP": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCPublicSubnet1EIPA620A1EE"}], "/JerseyColoursStack/JerseyColoursVPC/PublicSubnet1/NATGateway": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCPublicSubnet1NATGatewayB044EC76"}], "/JerseyColoursStack/JerseyColoursVPC/PublicSubnet2/Subnet": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCPublicSubnet2Subnet6C002866"}], "/JerseyColoursStack/JerseyColoursVPC/PublicSubnet2/RouteTable": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCPublicSubnet2RouteTable3A6F674E"}], "/JerseyColoursStack/JerseyColoursVPC/PublicSubnet2/RouteTableAssociation": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCPublicSubnet2RouteTableAssociation0B5BBA31"}], "/JerseyColoursStack/JerseyColoursVPC/PublicSubnet2/DefaultRoute": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCPublicSubnet2DefaultRoute9DDE8DF4"}], "/JerseyColoursStack/JerseyColoursVPC/PrivateSubnet1/Subnet": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCPrivateSubnet1Subnet0DD79F72"}], "/JerseyColoursStack/JerseyColoursVPC/PrivateSubnet1/RouteTable": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCPrivateSubnet1RouteTableCAD830A6"}], "/JerseyColoursStack/JerseyColoursVPC/PrivateSubnet1/RouteTableAssociation": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCPrivateSubnet1RouteTableAssociationE1A8368C"}], "/JerseyColoursStack/JerseyColoursVPC/PrivateSubnet1/DefaultRoute": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCPrivateSubnet1DefaultRoute60B3F407"}], "/JerseyColoursStack/JerseyColoursVPC/PrivateSubnet2/Subnet": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCPrivateSubnet2Subnet5FC14D8F"}], "/JerseyColoursStack/JerseyColoursVPC/PrivateSubnet2/RouteTable": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCPrivateSubnet2RouteTable340D8A18"}], "/JerseyColoursStack/JerseyColoursVPC/PrivateSubnet2/RouteTableAssociation": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCPrivateSubnet2RouteTableAssociationF7A254FB"}], "/JerseyColoursStack/JerseyColoursVPC/PrivateSubnet2/DefaultRoute": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCPrivateSubnet2DefaultRouteCAEB1F71"}], "/JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet1/Subnet": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCDatabaseSubnet1SubnetF1B293F7"}], "/JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet1/RouteTable": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCDatabaseSubnet1RouteTable2F92102E"}], "/JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet1/RouteTableAssociation": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCDatabaseSubnet1RouteTableAssociationC84E3E12"}], "/JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet2/Subnet": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCDatabaseSubnet2Subnet4AA74667"}], "/JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet2/RouteTable": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCDatabaseSubnet2RouteTable8F0B2487"}], "/JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet2/RouteTableAssociation": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCDatabaseSubnet2RouteTableAssociation2214DB32"}], "/JerseyColoursStack/JerseyColoursVPC/IGW": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCIGW714B0DB7"}], "/JerseyColoursStack/JerseyColoursVPC/VPCGW": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCVPCGW2393A6F9"}], "/JerseyColoursStack/JerseyColoursVPC/RestrictDefaultSecurityGroupCustomResource/Default": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursVPCRestrictDefaultSecurityGroupCustomResourceEAD71727"}], "/JerseyColoursStack/Custom::VpcRestrictDefaultSGCustomResourceProvider": [{"type": "aws:cdk:is-custom-resource-handler-customResourceProvider", "data": true}], "/JerseyColoursStack/Custom::VpcRestrictDefaultSGCustomResourceProvider/Role": [{"type": "aws:cdk:logicalId", "data": "CustomVpcRestrictDefaultSGCustomResourceProviderRole26592FE0"}], "/JerseyColoursStack/Custom::VpcRestrictDefaultSGCustomResourceProvider/Handler": [{"type": "aws:cdk:logicalId", "data": "CustomVpcRestrictDefaultSGCustomResourceProviderHandlerDC833E5E"}], "/JerseyColoursStack/PatternFilesBucket/Resource": [{"type": "aws:cdk:logicalId", "data": "PatternFilesBucket4B88CA60"}], "/JerseyColoursStack/PatternFilesBucket/Policy/Resource": [{"type": "aws:cdk:logicalId", "data": "PatternFilesBucketPolicy18DEF973"}], "/JerseyColoursStack/JerseyColoursDB/SubnetGroup/Default": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursDBSubnetGroupBC34C44B"}], "/JerseyColoursStack/JerseyColoursDB/SecurityGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursDBSecurityGroup08D5F746"}], "/JerseyColoursStack/JerseyColoursDB/SecurityGroup/from JerseyColoursStackApiServiceSecurityGroup5BAD4259:5432": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursDBSecurityGroupfromJerseyColoursStackApiServiceSecurityGroup5BAD425954325C6D348C"}], "/JerseyColoursStack/JerseyColoursDB/Secret/Resource": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursStackJerseyColoursDBSecret34B4DFD33fdaad7efa858a3daf9490cf0a702aeb"}], "/JerseyColoursStack/JerseyColoursDB/Secret/Attachment/Resource": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursDBSecretAttachmentD17E6A2D"}], "/JerseyColoursStack/JerseyColoursDB/Resource": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursDB5E7999F9"}], "/JerseyColoursStack/ApiRepository/Resource": [{"type": "aws:cdk:logicalId", "data": "ApiRepositoryB8378B43"}], "/JerseyColoursStack/FrontendRepository/Resource": [{"type": "aws:cdk:logicalId", "data": "FrontendRepository7D714FA2"}], "/JerseyColoursStack/JerseyColoursCluster/Resource": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursCluster9715902D"}], "/JerseyColoursStack/TaskExecutionRole/Resource": [{"type": "aws:cdk:logicalId", "data": "TaskExecutionRole250D2532"}], "/JerseyColoursStack/TaskExecutionRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "TaskExecutionRoleDefaultPolicyA84DD1B0"}], "/JerseyColoursStack/ApiTaskRole/Resource": [{"type": "aws:cdk:logicalId", "data": "ApiTaskRole12FAD4A7"}], "/JerseyColoursStack/ApiTaskRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "ApiTaskRoleDefaultPolicyBEF5D530"}], "/JerseyColoursStack/ApiServiceLB/Resource": [{"type": "aws:cdk:logicalId", "data": "ApiServiceLBA5369AC5"}], "/JerseyColoursStack/ApiServiceLB/SecurityGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "ApiServiceLBSecurityGroup0ACC3F19"}], "/JerseyColoursStack/ApiServiceLB/SecurityGroup/to JerseyColoursStackApiServiceSecurityGroup5BAD4259:3000": [{"type": "aws:cdk:logicalId", "data": "ApiServiceLBSecurityGrouptoJerseyColoursStackApiServiceSecurityGroup5BAD42593000449BF842"}], "/JerseyColoursStack/ApiServiceLB/ApiServiceListener/Resource": [{"type": "aws:cdk:logicalId", "data": "ApiServiceLBApiServiceListener39E84C21"}], "/JerseyColoursStack/ApiServiceTargetGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "ApiServiceTargetGroupF5D87875"}], "/JerseyColoursStack/ApiServiceTaskDef/Resource": [{"type": "aws:cdk:logicalId", "data": "ApiServiceTaskDef5552F312"}], "/JerseyColoursStack/ApiServiceLogGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "ApiServiceLogGroup42225A1C"}], "/JerseyColoursStack/ApiService/Service": [{"type": "aws:cdk:logicalId", "data": "ApiServiceC9037CF0"}], "/JerseyColoursStack/ApiService/SecurityGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "ApiServiceSecurityGroupA2426F91"}], "/JerseyColoursStack/ApiService/SecurityGroup/from JerseyColoursStackApiServiceLBSecurityGroup25818FD9:3000": [{"type": "aws:cdk:logicalId", "data": "ApiServiceSecurityGroupfromJerseyColoursStackApiServiceLBSecurityGroup25818FD930007D094E77"}], "/JerseyColoursStack/FrontendServiceLB/Resource": [{"type": "aws:cdk:logicalId", "data": "FrontendServiceLBB5764C7D"}], "/JerseyColoursStack/FrontendServiceLB/SecurityGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "FrontendServiceLBSecurityGroup18025BC2"}], "/JerseyColoursStack/FrontendServiceLB/SecurityGroup/to JerseyColoursStackFrontendServiceSecurityGroupD365E684:80": [{"type": "aws:cdk:logicalId", "data": "FrontendServiceLBSecurityGrouptoJerseyColoursStackFrontendServiceSecurityGroupD365E684802A340844"}], "/JerseyColoursStack/FrontendServiceLB/FrontendServiceListener/Resource": [{"type": "aws:cdk:logicalId", "data": "FrontendServiceLBFrontendServiceListenerFD9F0591"}], "/JerseyColoursStack/FrontendServiceTargetGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "FrontendServiceTargetGroup51F20C12"}], "/JerseyColoursStack/FrontendServiceTaskDef/TaskRole/Resource": [{"type": "aws:cdk:logicalId", "data": "FrontendServiceTaskDefTaskRoleBB6B2323"}], "/JerseyColoursStack/FrontendServiceTaskDef/Resource": [{"type": "aws:cdk:logicalId", "data": "FrontendServiceTaskDef06639368"}], "/JerseyColoursStack/FrontendServiceLogGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "FrontendServiceLogGroup99775B35"}], "/JerseyColoursStack/FrontendService/Service": [{"type": "aws:cdk:logicalId", "data": "FrontendServiceBC94BA93"}], "/JerseyColoursStack/FrontendService/SecurityGroup/Resource": [{"type": "aws:cdk:logicalId", "data": "FrontendServiceSecurityGroup85470DEC"}], "/JerseyColoursStack/FrontendService/SecurityGroup/from JerseyColoursStackFrontendServiceLBSecurityGroupE8599451:80": [{"type": "aws:cdk:logicalId", "data": "FrontendServiceSecurityGroupfromJerseyColoursStackFrontendServiceLBSecurityGroupE8599451804751B38B"}], "/JerseyColoursStack/SVGGeneratorFunction/ServiceRole/Resource": [{"type": "aws:cdk:logicalId", "data": "SVGGeneratorFunctionServiceRoleF2DA643E"}], "/JerseyColoursStack/SVGGeneratorFunction/Resource": [{"type": "aws:cdk:logicalId", "data": "SVGGeneratorFunction1FB593CD"}], "/JerseyColoursStack/SVGGeneratorAPI/Resource": [{"type": "aws:cdk:logicalId", "data": "SVGGeneratorAPIF54FD110"}], "/JerseyColoursStack/SVGGeneratorAPI/Deployment/Resource": [{"type": "aws:cdk:logicalId", "data": "SVGGeneratorAPIDeployment8D93C6A655a93079e01ac4e5b139c19b275d33bf"}], "/JerseyColoursStack/SVGGeneratorAPI/DeploymentStage.prod/Resource": [{"type": "aws:cdk:logicalId", "data": "SVGGeneratorAPIDeploymentStageprod32256403"}], "/JerseyColoursStack/SVGGeneratorAPI/Endpoint": [{"type": "aws:cdk:logicalId", "data": "SVGGeneratorAPIEndpoint0D61CFEC"}], "/JerseyColoursStack/SVGGeneratorAPI/Default/OPTIONS/Resource": [{"type": "aws:cdk:logicalId", "data": "SVGGeneratorAPIOPTIONS7A0985AC"}], "/JerseyColoursStack/SVGGeneratorAPI/Default/generate/Resource": [{"type": "aws:cdk:logicalId", "data": "SVGGeneratorAPIgenerateF65226DF"}], "/JerseyColoursStack/SVGGeneratorAPI/Default/generate/OPTIONS/Resource": [{"type": "aws:cdk:logicalId", "data": "SVGGeneratorAPIgenerateOPTIONS3B980618"}], "/JerseyColoursStack/SVGGeneratorAPI/Default/generate/POST/ApiPermission.JerseyColoursStackSVGGeneratorAPIA9C7E49B.POST..generate": [{"type": "aws:cdk:logicalId", "data": "SVGGeneratorAPIgeneratePOSTApiPermissionJerseyColoursStackSVGGeneratorAPIA9C7E49BPOSTgenerate62E49712"}], "/JerseyColoursStack/SVGGeneratorAPI/Default/generate/POST/ApiPermission.Test.JerseyColoursStackSVGGeneratorAPIA9C7E49B.POST..generate": [{"type": "aws:cdk:logicalId", "data": "SVGGeneratorAPIgeneratePOSTApiPermissionTestJerseyColoursStackSVGGeneratorAPIA9C7E49BPOSTgenerate3B3A9B10"}], "/JerseyColoursStack/SVGGeneratorAPI/Default/generate/POST/Resource": [{"type": "aws:cdk:logicalId", "data": "SVGGeneratorAPIgeneratePOSTDC9F0BBB"}], "/JerseyColoursStack/JerseyColoursDistribution/Origin4/S3Origin/Resource": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursDistributionOrigin4S3Origin35EAB48A"}], "/JerseyColoursStack/JerseyColoursDistribution/Resource": [{"type": "aws:cdk:logicalId", "data": "JerseyColoursDistributionCB46AE9E"}], "/JerseyColoursStack/DatabaseEndpoint": [{"type": "aws:cdk:logicalId", "data": "DatabaseEndpoint"}], "/JerseyColoursStack/ApiLoadBalancerDNS": [{"type": "aws:cdk:logicalId", "data": "ApiLoadBalancerDNS"}], "/JerseyColoursStack/FrontendLoadBalancerDNS": [{"type": "aws:cdk:logicalId", "data": "FrontendLoadBalancerDNS"}], "/JerseyColoursStack/CloudFrontDomainName": [{"type": "aws:cdk:logicalId", "data": "CloudFrontDomainName"}], "/JerseyColoursStack/S3BucketName": [{"type": "aws:cdk:logicalId", "data": "S3BucketName"}], "/JerseyColoursStack/SVGGeneratorAPIEndpoint": [{"type": "aws:cdk:logicalId", "data": "SVGGeneratorAPIEndpoint"}], "/JerseyColoursStack/ApiRepositoryURI": [{"type": "aws:cdk:logicalId", "data": "ApiRepositoryURI"}], "/JerseyColoursStack/FrontendRepositoryURI": [{"type": "aws:cdk:logicalId", "data": "FrontendRepositoryURI"}], "/JerseyColoursStack/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/JerseyColoursStack/BootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "BootstrapVersion"}], "/JerseyColoursStack/CheckBootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "CheckBootstrapVersion"}]}, "displayName": "JerseyColoursStack"}, "Tree": {"type": "cdk:tree", "properties": {"file": "tree.json"}}}, "minimumCliVersion": "2.1020.2"}