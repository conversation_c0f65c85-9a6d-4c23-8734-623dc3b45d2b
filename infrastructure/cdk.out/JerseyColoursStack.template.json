{"Description": "Jersey Colours - Racing Jersey Designer Application", "Resources": {"JerseyColoursVPCFE3584A6": {"Type": "AWS::EC2::VPC", "Properties": {"CidrBlock": "10.0.0.0/16", "EnableDnsHostnames": true, "EnableDnsSupport": true, "InstanceTenancy": "default", "Tags": [{"Key": "Name", "Value": "JerseyColoursStack/JerseyColoursVPC"}]}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/Resource"}}, "JerseyColoursVPCPublicSubnet1Subnet723163B5": {"Type": "AWS::EC2::Subnet", "Properties": {"AvailabilityZone": "ap-southeast-2a", "CidrBlock": "10.0.0.0/24", "MapPublicIpOnLaunch": true, "Tags": [{"Key": "aws-cdk:subnet-name", "Value": "Public"}, {"Key": "aws-cdk:subnet-type", "Value": "Public"}, {"Key": "Name", "Value": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet1"}], "VpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet1/Subnet"}}, "JerseyColoursVPCPublicSubnet1RouteTableD9ADFDC6": {"Type": "AWS::EC2::RouteTable", "Properties": {"Tags": [{"Key": "Name", "Value": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet1"}], "VpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet1/RouteTable"}}, "JerseyColoursVPCPublicSubnet1RouteTableAssociationC3BBF53E": {"Type": "AWS::EC2::SubnetRouteTableAssociation", "Properties": {"RouteTableId": {"Ref": "JerseyColoursVPCPublicSubnet1RouteTableD9ADFDC6"}, "SubnetId": {"Ref": "JerseyColoursVPCPublicSubnet1Subnet723163B5"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet1/RouteTableAssociation"}}, "JerseyColoursVPCPublicSubnet1DefaultRoute5CE7643B": {"Type": "AWS::EC2::Route", "Properties": {"DestinationCidrBlock": "0.0.0.0/0", "GatewayId": {"Ref": "JerseyColoursVPCIGW714B0DB7"}, "RouteTableId": {"Ref": "JerseyColoursVPCPublicSubnet1RouteTableD9ADFDC6"}}, "DependsOn": ["JerseyColoursVPCVPCGW2393A6F9"], "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet1/DefaultRoute"}}, "JerseyColoursVPCPublicSubnet1EIPA620A1EE": {"Type": "AWS::EC2::EIP", "Properties": {"Domain": "vpc", "Tags": [{"Key": "Name", "Value": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet1"}]}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet1/EIP"}}, "JerseyColoursVPCPublicSubnet1NATGatewayB044EC76": {"Type": "AWS::EC2::NatGateway", "Properties": {"AllocationId": {"Fn::GetAtt": ["JerseyColoursVPCPublicSubnet1EIPA620A1EE", "AllocationId"]}, "SubnetId": {"Ref": "JerseyColoursVPCPublicSubnet1Subnet723163B5"}, "Tags": [{"Key": "Name", "Value": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet1"}]}, "DependsOn": ["JerseyColoursVPCPublicSubnet1DefaultRoute5CE7643B", "JerseyColoursVPCPublicSubnet1RouteTableAssociationC3BBF53E"], "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet1/NATGateway"}}, "JerseyColoursVPCPublicSubnet2Subnet6C002866": {"Type": "AWS::EC2::Subnet", "Properties": {"AvailabilityZone": "ap-southeast-2b", "CidrBlock": "********/24", "MapPublicIpOnLaunch": true, "Tags": [{"Key": "aws-cdk:subnet-name", "Value": "Public"}, {"Key": "aws-cdk:subnet-type", "Value": "Public"}, {"Key": "Name", "Value": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet2"}], "VpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet2/Subnet"}}, "JerseyColoursVPCPublicSubnet2RouteTable3A6F674E": {"Type": "AWS::EC2::RouteTable", "Properties": {"Tags": [{"Key": "Name", "Value": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet2"}], "VpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet2/RouteTable"}}, "JerseyColoursVPCPublicSubnet2RouteTableAssociation0B5BBA31": {"Type": "AWS::EC2::SubnetRouteTableAssociation", "Properties": {"RouteTableId": {"Ref": "JerseyColoursVPCPublicSubnet2RouteTable3A6F674E"}, "SubnetId": {"Ref": "JerseyColoursVPCPublicSubnet2Subnet6C002866"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet2/RouteTableAssociation"}}, "JerseyColoursVPCPublicSubnet2DefaultRoute9DDE8DF4": {"Type": "AWS::EC2::Route", "Properties": {"DestinationCidrBlock": "0.0.0.0/0", "GatewayId": {"Ref": "JerseyColoursVPCIGW714B0DB7"}, "RouteTableId": {"Ref": "JerseyColoursVPCPublicSubnet2RouteTable3A6F674E"}}, "DependsOn": ["JerseyColoursVPCVPCGW2393A6F9"], "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet2/DefaultRoute"}}, "JerseyColoursVPCPrivateSubnet1Subnet0DD79F72": {"Type": "AWS::EC2::Subnet", "Properties": {"AvailabilityZone": "ap-southeast-2a", "CidrBlock": "********/24", "MapPublicIpOnLaunch": false, "Tags": [{"Key": "aws-cdk:subnet-name", "Value": "Private"}, {"Key": "aws-cdk:subnet-type", "Value": "Private"}, {"Key": "Name", "Value": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet1"}], "VpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet1/Subnet"}}, "JerseyColoursVPCPrivateSubnet1RouteTableCAD830A6": {"Type": "AWS::EC2::RouteTable", "Properties": {"Tags": [{"Key": "Name", "Value": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet1"}], "VpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet1/RouteTable"}}, "JerseyColoursVPCPrivateSubnet1RouteTableAssociationE1A8368C": {"Type": "AWS::EC2::SubnetRouteTableAssociation", "Properties": {"RouteTableId": {"Ref": "JerseyColoursVPCPrivateSubnet1RouteTableCAD830A6"}, "SubnetId": {"Ref": "JerseyColoursVPCPrivateSubnet1Subnet0DD79F72"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet1/RouteTableAssociation"}}, "JerseyColoursVPCPrivateSubnet1DefaultRoute60B3F407": {"Type": "AWS::EC2::Route", "Properties": {"DestinationCidrBlock": "0.0.0.0/0", "NatGatewayId": {"Ref": "JerseyColoursVPCPublicSubnet1NATGatewayB044EC76"}, "RouteTableId": {"Ref": "JerseyColoursVPCPrivateSubnet1RouteTableCAD830A6"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet1/DefaultRoute"}}, "JerseyColoursVPCPrivateSubnet2Subnet5FC14D8F": {"Type": "AWS::EC2::Subnet", "Properties": {"AvailabilityZone": "ap-southeast-2b", "CidrBlock": "********/24", "MapPublicIpOnLaunch": false, "Tags": [{"Key": "aws-cdk:subnet-name", "Value": "Private"}, {"Key": "aws-cdk:subnet-type", "Value": "Private"}, {"Key": "Name", "Value": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet2"}], "VpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet2/Subnet"}}, "JerseyColoursVPCPrivateSubnet2RouteTable340D8A18": {"Type": "AWS::EC2::RouteTable", "Properties": {"Tags": [{"Key": "Name", "Value": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet2"}], "VpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet2/RouteTable"}}, "JerseyColoursVPCPrivateSubnet2RouteTableAssociationF7A254FB": {"Type": "AWS::EC2::SubnetRouteTableAssociation", "Properties": {"RouteTableId": {"Ref": "JerseyColoursVPCPrivateSubnet2RouteTable340D8A18"}, "SubnetId": {"Ref": "JerseyColoursVPCPrivateSubnet2Subnet5FC14D8F"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet2/RouteTableAssociation"}}, "JerseyColoursVPCPrivateSubnet2DefaultRouteCAEB1F71": {"Type": "AWS::EC2::Route", "Properties": {"DestinationCidrBlock": "0.0.0.0/0", "NatGatewayId": {"Ref": "JerseyColoursVPCPublicSubnet1NATGatewayB044EC76"}, "RouteTableId": {"Ref": "JerseyColoursVPCPrivateSubnet2RouteTable340D8A18"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet2/DefaultRoute"}}, "JerseyColoursVPCDatabaseSubnet1SubnetF1B293F7": {"Type": "AWS::EC2::Subnet", "Properties": {"AvailabilityZone": "ap-southeast-2a", "CidrBlock": "********/28", "MapPublicIpOnLaunch": false, "Tags": [{"Key": "aws-cdk:subnet-name", "Value": "Database"}, {"Key": "aws-cdk:subnet-type", "Value": "Isolated"}, {"Key": "Name", "Value": "JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet1"}], "VpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet1/Subnet"}}, "JerseyColoursVPCDatabaseSubnet1RouteTable2F92102E": {"Type": "AWS::EC2::RouteTable", "Properties": {"Tags": [{"Key": "Name", "Value": "JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet1"}], "VpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet1/RouteTable"}}, "JerseyColoursVPCDatabaseSubnet1RouteTableAssociationC84E3E12": {"Type": "AWS::EC2::SubnetRouteTableAssociation", "Properties": {"RouteTableId": {"Ref": "JerseyColoursVPCDatabaseSubnet1RouteTable2F92102E"}, "SubnetId": {"Ref": "JerseyColoursVPCDatabaseSubnet1SubnetF1B293F7"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet1/RouteTableAssociation"}}, "JerseyColoursVPCDatabaseSubnet2Subnet4AA74667": {"Type": "AWS::EC2::Subnet", "Properties": {"AvailabilityZone": "ap-southeast-2b", "CidrBlock": "*********/28", "MapPublicIpOnLaunch": false, "Tags": [{"Key": "aws-cdk:subnet-name", "Value": "Database"}, {"Key": "aws-cdk:subnet-type", "Value": "Isolated"}, {"Key": "Name", "Value": "JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet2"}], "VpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet2/Subnet"}}, "JerseyColoursVPCDatabaseSubnet2RouteTable8F0B2487": {"Type": "AWS::EC2::RouteTable", "Properties": {"Tags": [{"Key": "Name", "Value": "JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet2"}], "VpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet2/RouteTable"}}, "JerseyColoursVPCDatabaseSubnet2RouteTableAssociation2214DB32": {"Type": "AWS::EC2::SubnetRouteTableAssociation", "Properties": {"RouteTableId": {"Ref": "JerseyColoursVPCDatabaseSubnet2RouteTable8F0B2487"}, "SubnetId": {"Ref": "JerseyColoursVPCDatabaseSubnet2Subnet4AA74667"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet2/RouteTableAssociation"}}, "JerseyColoursVPCIGW714B0DB7": {"Type": "AWS::EC2::InternetGateway", "Properties": {"Tags": [{"Key": "Name", "Value": "JerseyColoursStack/JerseyColoursVPC"}]}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/IGW"}}, "JerseyColoursVPCVPCGW2393A6F9": {"Type": "AWS::EC2::VPCGatewayAttachment", "Properties": {"InternetGatewayId": {"Ref": "JerseyColoursVPCIGW714B0DB7"}, "VpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/VPCGW"}}, "JerseyColoursVPCRestrictDefaultSecurityGroupCustomResourceEAD71727": {"Type": "Custom::VpcRestrictDefaultSG", "Properties": {"ServiceToken": {"Fn::GetAtt": ["CustomVpcRestrictDefaultSGCustomResourceProviderHandlerDC833E5E", "<PERSON><PERSON>"]}, "DefaultSecurityGroupId": {"Fn::GetAtt": ["JerseyColoursVPCFE3584A6", "DefaultSecurityGroup"]}, "Account": "************"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursVPC/RestrictDefaultSecurityGroupCustomResource/Default"}}, "CustomVpcRestrictDefaultSGCustomResourceProviderRole26592FE0": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}]}, "ManagedPolicyArns": [{"Fn::Sub": "arn:${AWS::Partition}:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"}], "Policies": [{"PolicyName": "Inline", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["ec2:AuthorizeSecurityGroupIngress", "ec2:AuthorizeSecurityGroupEgress", "ec2:RevokeSecurityGroupIngress", "ec2:RevokeSecurityGroupEgress"], "Resource": [{"Fn::Join": ["", ["arn:aws:ec2:ap-southeast-2:************:security-group/", {"Fn::GetAtt": ["JerseyColoursVPCFE3584A6", "DefaultSecurityGroup"]}]]}]}]}}]}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/Custom::VpcRestrictDefaultSGCustomResourceProvider/Role"}}, "CustomVpcRestrictDefaultSGCustomResourceProviderHandlerDC833E5E": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"S3Bucket": "cdk-hnb659fds-assets-************-ap-southeast-2", "S3Key": "7fa1e366ee8a9ded01fc355f704cff92bfd179574e6f9cfee800a3541df1b200.zip"}, "Timeout": 900, "MemorySize": 128, "Handler": "__entrypoint__.handler", "Role": {"Fn::GetAtt": ["CustomVpcRestrictDefaultSGCustomResourceProviderRole26592FE0", "<PERSON><PERSON>"]}, "Runtime": "nodejs22.x", "Description": "Lambda function for removing all inbound/outbound rules from the VPC default security group"}, "DependsOn": ["CustomVpcRestrictDefaultSGCustomResourceProviderRole26592FE0"], "Metadata": {"aws:cdk:path": "JerseyColoursStack/Custom::VpcRestrictDefaultSGCustomResourceProvider/Handler", "aws:asset:path": "asset.7fa1e366ee8a9ded01fc355f704cff92bfd179574e6f9cfee800a3541df1b200", "aws:asset:property": "Code"}}, "PatternFilesBucket4B88CA60": {"Type": "AWS::S3::<PERSON><PERSON>", "Properties": {"BucketEncryption": {"ServerSideEncryptionConfiguration": [{"ServerSideEncryptionByDefault": {"SSEAlgorithm": "AES256"}}]}, "BucketName": "jersey-colours-patterns-************-ap-southeast-2", "CorsConfiguration": {"CorsRules": [{"AllowedHeaders": ["*"], "AllowedMethods": ["GET", "POST", "PUT"], "AllowedOrigins": ["*"], "MaxAge": 3000}]}, "LifecycleConfiguration": {"Rules": [{"Id": "DeleteOldVersions", "NoncurrentVersionExpiration": {"NoncurrentDays": 30}, "Status": "Enabled"}]}, "PublicAccessBlockConfiguration": {"BlockPublicAcls": true, "BlockPublicPolicy": true, "IgnorePublicAcls": true, "RestrictPublicBuckets": true}, "VersioningConfiguration": {"Status": "Enabled"}}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "JerseyColoursStack/PatternFilesBucket/Resource"}}, "PatternFilesBucketPolicy18DEF973": {"Type": "AWS::S3::BucketPolicy", "Properties": {"Bucket": {"Ref": "PatternFilesBucket4B88CA60"}, "PolicyDocument": {"Statement": [{"Action": "s3:GetObject", "Effect": "Allow", "Principal": {"CanonicalUser": {"Fn::GetAtt": ["JerseyColoursDistributionOrigin4S3Origin35EAB48A", "S3CanonicalUserId"]}}, "Resource": {"Fn::Join": ["", [{"Fn::GetAtt": ["PatternFilesBucket4B88CA60", "<PERSON><PERSON>"]}, "/*"]]}}], "Version": "2012-10-17"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/PatternFilesBucket/Policy/Resource"}}, "JerseyColoursDBSubnetGroupBC34C44B": {"Type": "AWS::RDS::DBSubnetGroup", "Properties": {"DBSubnetGroupDescription": "Subnet group for JerseyColoursDB database", "SubnetIds": [{"Ref": "JerseyColoursVPCDatabaseSubnet1SubnetF1B293F7"}, {"Ref": "JerseyColoursVPCDatabaseSubnet2Subnet4AA74667"}]}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursDB/SubnetGroup/Default"}}, "JerseyColoursDBSecurityGroup08D5F746": {"Type": "AWS::EC2::SecurityGroup", "Properties": {"GroupDescription": "Security group for JerseyColoursDB database", "SecurityGroupEgress": [{"CidrIp": "0.0.0.0/0", "Description": "Allow all outbound traffic by default", "IpProtocol": "-1"}], "VpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursDB/SecurityGroup/Resource"}}, "JerseyColoursDBSecurityGroupfromJerseyColoursStackApiServiceSecurityGroup5BAD425954325C6D348C": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "from JerseyColoursStackApiServiceSecurityGroup5BAD4259:5432", "FromPort": 5432, "GroupId": {"Fn::GetAtt": ["JerseyColoursDBSecurityGroup08D5F746", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["ApiServiceSecurityGroupA2426F91", "GroupId"]}, "ToPort": 5432}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursDB/SecurityGroup/from JerseyColoursStackApiServiceSecurityGroup5BAD4259:5432"}}, "JerseyColoursStackJerseyColoursDBSecret34B4DFD33fdaad7efa858a3daf9490cf0a702aeb": {"Type": "AWS::<PERSON>Manager::Secret", "Properties": {"Description": {"Fn::Join": ["", ["Generated by the CDK for stack: ", {"Ref": "AWS::StackName"}]]}, "GenerateSecretString": {"ExcludeCharacters": " %+~`#$&*()|[]{}:;<>?!'/@\"\\", "GenerateStringKey": "password", "PasswordLength": 30, "SecretStringTemplate": "{\"username\":\"postgres\"}"}, "Name": "jersey-colours-db-credentials"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursDB/Secret/Resource"}}, "JerseyColoursDBSecretAttachmentD17E6A2D": {"Type": "AWS::SecretsManager::SecretTargetAttachment", "Properties": {"SecretId": {"Ref": "JerseyColoursStackJerseyColoursDBSecret34B4DFD33fdaad7efa858a3daf9490cf0a702aeb"}, "TargetId": {"Ref": "JerseyColoursDB5E7999F9"}, "TargetType": "AWS::RDS::DBInstance"}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursDB/Secret/Attachment/Resource"}}, "JerseyColoursDB5E7999F9": {"Type": "AWS::RDS::DBInstance", "Properties": {"AllocatedStorage": "20", "BackupRetentionPeriod": 7, "CopyTagsToSnapshot": true, "DBInstanceClass": "db.t3.micro", "DBName": "jersey_colours", "DBSubnetGroupName": {"Ref": "JerseyColoursDBSubnetGroupBC34C44B"}, "DeleteAutomatedBackups": true, "DeletionProtection": false, "Engine": "postgres", "EngineVersion": "15", "MasterUserPassword": {"Fn::Join": ["", ["{{resolve:secretsmanager:", {"Ref": "JerseyColoursStackJerseyColoursDBSecret34B4DFD33fdaad7efa858a3daf9490cf0a702aeb"}, ":SecretString:password::}}"]]}, "MasterUsername": "postgres", "MaxAllocatedStorage": 100, "MultiAZ": false, "PubliclyAccessible": false, "StorageEncrypted": true, "StorageType": "gp2", "VPCSecurityGroups": [{"Fn::GetAtt": ["JerseyColoursDBSecurityGroup08D5F746", "GroupId"]}]}, "UpdateReplacePolicy": "Snapshot", "DeletionPolicy": "Snapshot", "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursDB/Resource"}}, "ApiRepositoryB8378B43": {"Type": "AWS::ECR::Repository", "Properties": {"ImageScanningConfiguration": {"ScanOnPush": true}, "LifecyclePolicy": {"LifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"selection\":{\"tagStatus\":\"any\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}}]}"}, "RepositoryName": "jersey-colours-api"}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "JerseyColoursStack/ApiRepository/Resource"}}, "FrontendRepository7D714FA2": {"Type": "AWS::ECR::Repository", "Properties": {"ImageScanningConfiguration": {"ScanOnPush": true}, "LifecyclePolicy": {"LifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"selection\":{\"tagStatus\":\"any\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}}]}"}, "RepositoryName": "jersey-colours-frontend"}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "JerseyColoursStack/FrontendRepository/Resource"}}, "JerseyColoursCluster9715902D": {"Type": "AWS::ECS::Cluster", "Properties": {"ClusterSettings": [{"Name": "containerInsights", "Value": "enabled"}]}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursCluster/Resource"}}, "TaskExecutionRole250D2532": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "ecs-tasks.amazonaws.com"}}], "Version": "2012-10-17"}, "ManagedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"]]}]}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/TaskExecutionRole/Resource"}}, "TaskExecutionRoleDefaultPolicyA84DD1B0": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": ["ecr:BatchCheckLayerAvailability", "ecr:BatchGetImage", "ecr:GetDownloadUrlForLayer"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["ApiRepositoryB8378B43", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["FrontendRepository7D714FA2", "<PERSON><PERSON>"]}]}, {"Action": "ecr:GetAuthorizationToken", "Effect": "Allow", "Resource": "*"}, {"Action": ["logs:CreateLogStream", "logs:PutLogEvents"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["ApiServiceLogGroup42225A1C", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["FrontendServiceLogGroup99775B35", "<PERSON><PERSON>"]}]}, {"Action": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "Effect": "Allow", "Resource": {"Ref": "JerseyColoursDBSecretAttachmentD17E6A2D"}}], "Version": "2012-10-17"}, "PolicyName": "TaskExecutionRoleDefaultPolicyA84DD1B0", "Roles": [{"Ref": "TaskExecutionRole250D2532"}]}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/TaskExecutionRole/DefaultPolicy/Resource"}}, "ApiTaskRole12FAD4A7": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "ecs-tasks.amazonaws.com"}}], "Version": "2012-10-17"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/ApiTaskRole/Resource"}}, "ApiTaskRoleDefaultPolicyBEF5D530": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": ["s3:Abort*", "s3:DeleteObject*", "s3:GetBucket*", "s3:GetObject*", "s3:List*", "s3:PutObject", "s3:PutObjectLegalHold", "s3:PutObjectRetention", "s3:PutObjectTagging", "s3:PutObjectVersionTagging"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["PatternFilesBucket4B88CA60", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["PatternFilesBucket4B88CA60", "<PERSON><PERSON>"]}, "/*"]]}]}, {"Action": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "Effect": "Allow", "Resource": {"Ref": "JerseyColoursDBSecretAttachmentD17E6A2D"}}], "Version": "2012-10-17"}, "PolicyName": "ApiTaskRoleDefaultPolicyBEF5D530", "Roles": [{"Ref": "ApiTaskRole12FAD4A7"}]}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/ApiTaskRole/DefaultPolicy/Resource"}}, "ApiServiceLBA5369AC5": {"Type": "AWS::ElasticLoadBalancingV2::LoadBalancer", "Properties": {"LoadBalancerAttributes": [{"Key": "deletion_protection.enabled", "Value": "false"}], "Scheme": "internet-facing", "SecurityGroups": [{"Fn::GetAtt": ["ApiServiceLBSecurityGroup0ACC3F19", "GroupId"]}], "Subnets": [{"Ref": "JerseyColoursVPCPublicSubnet1Subnet723163B5"}, {"Ref": "JerseyColoursVPCPublicSubnet2Subnet6C002866"}], "Type": "application"}, "DependsOn": ["JerseyColoursVPCPublicSubnet1DefaultRoute5CE7643B", "JerseyColoursVPCPublicSubnet1RouteTableAssociationC3BBF53E", "JerseyColoursVPCPublicSubnet2DefaultRoute9DDE8DF4", "JerseyColoursVPCPublicSubnet2RouteTableAssociation0B5BBA31"], "Metadata": {"aws:cdk:path": "JerseyColoursStack/ApiServiceLB/Resource"}}, "ApiServiceLBSecurityGroup0ACC3F19": {"Type": "AWS::EC2::SecurityGroup", "Properties": {"GroupDescription": "Automatically created Security Group for ELB JerseyColoursStackApiServiceLBD76B9D50", "SecurityGroupIngress": [{"CidrIp": "0.0.0.0/0", "Description": "Allow from anyone on port 80", "FromPort": 80, "IpProtocol": "tcp", "ToPort": 80}], "VpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/ApiServiceLB/SecurityGroup/Resource"}}, "ApiServiceLBSecurityGrouptoJerseyColoursStackApiServiceSecurityGroup5BAD42593000449BF842": {"Type": "AWS::EC2::SecurityGroupEgress", "Properties": {"Description": "Load balancer to target", "DestinationSecurityGroupId": {"Fn::GetAtt": ["ApiServiceSecurityGroupA2426F91", "GroupId"]}, "FromPort": 3000, "GroupId": {"Fn::GetAtt": ["ApiServiceLBSecurityGroup0ACC3F19", "GroupId"]}, "IpProtocol": "tcp", "ToPort": 3000}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/ApiServiceLB/SecurityGroup/to JerseyColoursStackApiServiceSecurityGroup5BAD4259:3000"}}, "ApiServiceLBApiServiceListener39E84C21": {"Type": "AWS::ElasticLoadBalancingV2::Listener", "Properties": {"DefaultActions": [{"TargetGroupArn": {"Ref": "ApiServiceTargetGroupF5D87875"}, "Type": "forward"}], "LoadBalancerArn": {"Ref": "ApiServiceLBA5369AC5"}, "Port": 80, "Protocol": "HTTP"}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/ApiServiceLB/ApiServiceListener/Resource"}}, "ApiServiceTargetGroupF5D87875": {"Type": "AWS::ElasticLoadBalancingV2::TargetGroup", "Properties": {"Port": 80, "Protocol": "HTTP", "TargetGroupAttributes": [{"Key": "stickiness.enabled", "Value": "false"}], "TargetType": "ip", "VpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/ApiServiceTargetGroup/Resource"}}, "ApiServiceTaskDef5552F312": {"Type": "AWS::ECS::TaskDefinition", "Properties": {"ContainerDefinitions": [{"Environment": [{"Name": "S3_BUCKET", "Value": {"Ref": "PatternFilesBucket4B88CA60"}}, {"Name": "AWS_REGION", "Value": "ap-southeast-2"}], "Essential": true, "Image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["ApiRepositoryB8378B43", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["ApiRepositoryB8378B43", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "ApiRepositoryB8378B43"}, ":latest"]]}, "LogConfiguration": {"LogDriver": "awslogs", "Options": {"awslogs-group": {"Ref": "ApiServiceLogGroup42225A1C"}, "awslogs-stream-prefix": "jersey-colours-api", "awslogs-region": "ap-southeast-2"}}, "Name": "web", "PortMappings": [{"ContainerPort": 3000, "Protocol": "tcp"}], "Secrets": [{"Name": "DATABASE_URL", "ValueFrom": {"Fn::Join": ["", [{"Ref": "JerseyColoursDBSecretAttachmentD17E6A2D"}, ":engine::"]]}}]}], "Cpu": "256", "ExecutionRoleArn": {"Fn::GetAtt": ["TaskExecutionRole250D2532", "<PERSON><PERSON>"]}, "Family": "JerseyColoursStackApiServiceTaskDef20A1B9CB", "Memory": "512", "NetworkMode": "awsvpc", "RequiresCompatibilities": ["FARGATE"], "TaskRoleArn": {"Fn::GetAtt": ["ApiTaskRole12FAD4A7", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/ApiServiceTaskDef/Resource"}}, "ApiServiceLogGroup42225A1C": {"Type": "AWS::Logs::LogGroup", "Properties": {"RetentionInDays": 7}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "JerseyColoursStack/ApiServiceLogGroup/Resource"}}, "ApiServiceC9037CF0": {"Type": "AWS::ECS::Service", "Properties": {"Cluster": {"Ref": "JerseyColoursCluster9715902D"}, "DeploymentConfiguration": {"Alarms": {"AlarmNames": [], "Enable": false, "Rollback": false}, "MaximumPercent": 200, "MinimumHealthyPercent": 50}, "DesiredCount": 2, "EnableECSManagedTags": false, "HealthCheckGracePeriodSeconds": 60, "LaunchType": "FARGATE", "LoadBalancers": [{"ContainerName": "web", "ContainerPort": 3000, "TargetGroupArn": {"Ref": "ApiServiceTargetGroupF5D87875"}}], "NetworkConfiguration": {"AwsvpcConfiguration": {"AssignPublicIp": "DISABLED", "SecurityGroups": [{"Fn::GetAtt": ["ApiServiceSecurityGroupA2426F91", "GroupId"]}], "Subnets": [{"Ref": "JerseyColoursVPCPrivateSubnet1Subnet0DD79F72"}, {"Ref": "JerseyColoursVPCPrivateSubnet2Subnet5FC14D8F"}]}}, "TaskDefinition": {"Ref": "ApiServiceTaskDef5552F312"}}, "DependsOn": ["ApiServiceLBApiServiceListener39E84C21", "ApiTaskRoleDefaultPolicyBEF5D530", "ApiTaskRole12FAD4A7"], "Metadata": {"aws:cdk:path": "JerseyColoursStack/ApiService/Service"}}, "ApiServiceSecurityGroupA2426F91": {"Type": "AWS::EC2::SecurityGroup", "Properties": {"GroupDescription": "JerseyColoursStack/ApiService/SecurityGroup", "SecurityGroupEgress": [{"CidrIp": "0.0.0.0/0", "Description": "Allow all outbound traffic by default", "IpProtocol": "-1"}], "VpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}, "DependsOn": ["ApiTaskRoleDefaultPolicyBEF5D530", "ApiTaskRole12FAD4A7"], "Metadata": {"aws:cdk:path": "JerseyColoursStack/ApiService/SecurityGroup/Resource"}}, "ApiServiceSecurityGroupfromJerseyColoursStackApiServiceLBSecurityGroup25818FD930007D094E77": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Load balancer to target", "FromPort": 3000, "GroupId": {"Fn::GetAtt": ["ApiServiceSecurityGroupA2426F91", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["ApiServiceLBSecurityGroup0ACC3F19", "GroupId"]}, "ToPort": 3000}, "DependsOn": ["ApiTaskRoleDefaultPolicyBEF5D530", "ApiTaskRole12FAD4A7"], "Metadata": {"aws:cdk:path": "JerseyColoursStack/ApiService/SecurityGroup/from JerseyColoursStackApiServiceLBSecurityGroup25818FD9:3000"}}, "FrontendServiceLBB5764C7D": {"Type": "AWS::ElasticLoadBalancingV2::LoadBalancer", "Properties": {"LoadBalancerAttributes": [{"Key": "deletion_protection.enabled", "Value": "false"}], "Scheme": "internet-facing", "SecurityGroups": [{"Fn::GetAtt": ["FrontendServiceLBSecurityGroup18025BC2", "GroupId"]}], "Subnets": [{"Ref": "JerseyColoursVPCPublicSubnet1Subnet723163B5"}, {"Ref": "JerseyColoursVPCPublicSubnet2Subnet6C002866"}], "Type": "application"}, "DependsOn": ["JerseyColoursVPCPublicSubnet1DefaultRoute5CE7643B", "JerseyColoursVPCPublicSubnet1RouteTableAssociationC3BBF53E", "JerseyColoursVPCPublicSubnet2DefaultRoute9DDE8DF4", "JerseyColoursVPCPublicSubnet2RouteTableAssociation0B5BBA31"], "Metadata": {"aws:cdk:path": "JerseyColoursStack/FrontendServiceLB/Resource"}}, "FrontendServiceLBSecurityGroup18025BC2": {"Type": "AWS::EC2::SecurityGroup", "Properties": {"GroupDescription": "Automatically created Security Group for ELB JerseyColoursStackFrontendServiceLB4698FFEB", "SecurityGroupIngress": [{"CidrIp": "0.0.0.0/0", "Description": "Allow from anyone on port 80", "FromPort": 80, "IpProtocol": "tcp", "ToPort": 80}], "VpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/FrontendServiceLB/SecurityGroup/Resource"}}, "FrontendServiceLBSecurityGrouptoJerseyColoursStackFrontendServiceSecurityGroupD365E684802A340844": {"Type": "AWS::EC2::SecurityGroupEgress", "Properties": {"Description": "Load balancer to target", "DestinationSecurityGroupId": {"Fn::GetAtt": ["FrontendServiceSecurityGroup85470DEC", "GroupId"]}, "FromPort": 80, "GroupId": {"Fn::GetAtt": ["FrontendServiceLBSecurityGroup18025BC2", "GroupId"]}, "IpProtocol": "tcp", "ToPort": 80}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/FrontendServiceLB/SecurityGroup/to JerseyColoursStackFrontendServiceSecurityGroupD365E684:80"}}, "FrontendServiceLBFrontendServiceListenerFD9F0591": {"Type": "AWS::ElasticLoadBalancingV2::Listener", "Properties": {"DefaultActions": [{"TargetGroupArn": {"Ref": "FrontendServiceTargetGroup51F20C12"}, "Type": "forward"}], "LoadBalancerArn": {"Ref": "FrontendServiceLBB5764C7D"}, "Port": 80, "Protocol": "HTTP"}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/FrontendServiceLB/FrontendServiceListener/Resource"}}, "FrontendServiceTargetGroup51F20C12": {"Type": "AWS::ElasticLoadBalancingV2::TargetGroup", "Properties": {"Port": 80, "Protocol": "HTTP", "TargetGroupAttributes": [{"Key": "stickiness.enabled", "Value": "false"}], "TargetType": "ip", "VpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/FrontendServiceTargetGroup/Resource"}}, "FrontendServiceTaskDefTaskRoleBB6B2323": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "ecs-tasks.amazonaws.com"}}], "Version": "2012-10-17"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/FrontendServiceTaskDef/TaskRole/Resource"}}, "FrontendServiceTaskDef06639368": {"Type": "AWS::ECS::TaskDefinition", "Properties": {"ContainerDefinitions": [{"Environment": [{"Name": "API_URL", "Value": {"Fn::Join": ["", ["http://", {"Fn::GetAtt": ["ApiServiceLBA5369AC5", "DNSName"]}]]}}], "Essential": true, "Image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["FrontendRepository7D714FA2", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["FrontendRepository7D714FA2", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "FrontendRepository7D714FA2"}, ":latest"]]}, "LogConfiguration": {"LogDriver": "awslogs", "Options": {"awslogs-group": {"Ref": "FrontendServiceLogGroup99775B35"}, "awslogs-stream-prefix": "jersey-colours-frontend", "awslogs-region": "ap-southeast-2"}}, "Name": "web", "PortMappings": [{"ContainerPort": 80, "Protocol": "tcp"}]}], "Cpu": "256", "ExecutionRoleArn": {"Fn::GetAtt": ["TaskExecutionRole250D2532", "<PERSON><PERSON>"]}, "Family": "JerseyColoursStackFrontendServiceTaskDefF87F9B84", "Memory": "256", "NetworkMode": "awsvpc", "RequiresCompatibilities": ["FARGATE"], "TaskRoleArn": {"Fn::GetAtt": ["FrontendServiceTaskDefTaskRoleBB6B2323", "<PERSON><PERSON>"]}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/FrontendServiceTaskDef/Resource"}}, "FrontendServiceLogGroup99775B35": {"Type": "AWS::Logs::LogGroup", "Properties": {"RetentionInDays": 7}, "UpdateReplacePolicy": "<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Metadata": {"aws:cdk:path": "JerseyColoursStack/FrontendServiceLogGroup/Resource"}}, "FrontendServiceBC94BA93": {"Type": "AWS::ECS::Service", "Properties": {"Cluster": {"Ref": "JerseyColoursCluster9715902D"}, "DeploymentConfiguration": {"Alarms": {"AlarmNames": [], "Enable": false, "Rollback": false}, "MaximumPercent": 200, "MinimumHealthyPercent": 50}, "DesiredCount": 2, "EnableECSManagedTags": false, "HealthCheckGracePeriodSeconds": 60, "LaunchType": "FARGATE", "LoadBalancers": [{"ContainerName": "web", "ContainerPort": 80, "TargetGroupArn": {"Ref": "FrontendServiceTargetGroup51F20C12"}}], "NetworkConfiguration": {"AwsvpcConfiguration": {"AssignPublicIp": "DISABLED", "SecurityGroups": [{"Fn::GetAtt": ["FrontendServiceSecurityGroup85470DEC", "GroupId"]}], "Subnets": [{"Ref": "JerseyColoursVPCPrivateSubnet1Subnet0DD79F72"}, {"Ref": "JerseyColoursVPCPrivateSubnet2Subnet5FC14D8F"}]}}, "TaskDefinition": {"Ref": "FrontendServiceTaskDef06639368"}}, "DependsOn": ["FrontendServiceLBFrontendServiceListenerFD9F0591", "FrontendServiceTaskDefTaskRoleBB6B2323"], "Metadata": {"aws:cdk:path": "JerseyColoursStack/FrontendService/Service"}}, "FrontendServiceSecurityGroup85470DEC": {"Type": "AWS::EC2::SecurityGroup", "Properties": {"GroupDescription": "JerseyColoursStack/FrontendService/SecurityGroup", "SecurityGroupEgress": [{"CidrIp": "0.0.0.0/0", "Description": "Allow all outbound traffic by default", "IpProtocol": "-1"}], "VpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}, "DependsOn": ["FrontendServiceTaskDefTaskRoleBB6B2323"], "Metadata": {"aws:cdk:path": "JerseyColoursStack/FrontendService/SecurityGroup/Resource"}}, "FrontendServiceSecurityGroupfromJerseyColoursStackFrontendServiceLBSecurityGroupE8599451804751B38B": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"Description": "Load balancer to target", "FromPort": 80, "GroupId": {"Fn::GetAtt": ["FrontendServiceSecurityGroup85470DEC", "GroupId"]}, "IpProtocol": "tcp", "SourceSecurityGroupId": {"Fn::GetAtt": ["FrontendServiceLBSecurityGroup18025BC2", "GroupId"]}, "ToPort": 80}, "DependsOn": ["FrontendServiceTaskDefTaskRoleBB6B2323"], "Metadata": {"aws:cdk:path": "JerseyColoursStack/FrontendService/SecurityGroup/from JerseyColoursStackFrontendServiceLBSecurityGroupE8599451:80"}}, "SVGGeneratorFunctionServiceRoleF2DA643E": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "ManagedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}]}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/SVGGeneratorFunction/ServiceRole/Resource"}}, "SVGGeneratorFunction1FB593CD": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"S3Bucket": "cdk-hnb659fds-assets-************-ap-southeast-2", "S3Key": "2da74cf8648f42eef5a108fc71819bd6093a4f75aa67e03c1b813d2c20db3fba.zip"}, "Environment": {"Variables": {"NODE_ENV": "production"}}, "Handler": "index.handler", "MemorySize": 512, "Role": {"Fn::GetAtt": ["SVGGeneratorFunctionServiceRoleF2DA643E", "<PERSON><PERSON>"]}, "Runtime": "nodejs20.x", "Timeout": 30}, "DependsOn": ["SVGGeneratorFunctionServiceRoleF2DA643E"], "Metadata": {"aws:cdk:path": "JerseyColoursStack/SVGGeneratorFunction/Resource", "aws:asset:path": "asset.2da74cf8648f42eef5a108fc71819bd6093a4f75aa67e03c1b813d2c20db3fba", "aws:asset:is-bundled": false, "aws:asset:property": "Code"}}, "SVGGeneratorAPIF54FD110": {"Type": "AWS::ApiGateway::RestApi", "Properties": {"Description": "API for generating jersey SVGs", "Name": "Jersey Colours SVG Generator"}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/SVGGeneratorAPI/Resource"}}, "SVGGeneratorAPIDeployment8D93C6A655a93079e01ac4e5b139c19b275d33bf": {"Type": "AWS::ApiGateway::Deployment", "Properties": {"Description": "API for generating jersey SVGs", "RestApiId": {"Ref": "SVGGeneratorAPIF54FD110"}}, "DependsOn": ["SVGGeneratorAPIgenerateOPTIONS3B980618", "SVGGeneratorAPIgeneratePOSTDC9F0BBB", "SVGGeneratorAPIgenerateF65226DF", "SVGGeneratorAPIOPTIONS7A0985AC"], "Metadata": {"aws:cdk:path": "JerseyColoursStack/SVGGeneratorAPI/Deployment/Resource", "aws:cdk:do-not-refactor": true}}, "SVGGeneratorAPIDeploymentStageprod32256403": {"Type": "AWS::ApiGateway::Stage", "Properties": {"DeploymentId": {"Ref": "SVGGeneratorAPIDeployment8D93C6A655a93079e01ac4e5b139c19b275d33bf"}, "RestApiId": {"Ref": "SVGGeneratorAPIF54FD110"}, "StageName": "prod"}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/SVGGeneratorAPI/DeploymentStage.prod/Resource"}}, "SVGGeneratorAPIOPTIONS7A0985AC": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": false, "AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,Authorization'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}, "StatusCode": "204"}], "RequestTemplates": {"application/json": "{ statusCode: 200 }"}, "Type": "MOCK"}, "MethodResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}, "StatusCode": "204"}], "ResourceId": {"Fn::GetAtt": ["SVGGeneratorAPIF54FD110", "RootResourceId"]}, "RestApiId": {"Ref": "SVGGeneratorAPIF54FD110"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/SVGGeneratorAPI/Default/OPTIONS/Resource"}}, "SVGGeneratorAPIgenerateF65226DF": {"Type": "AWS::ApiGateway::Resource", "Properties": {"ParentId": {"Fn::GetAtt": ["SVGGeneratorAPIF54FD110", "RootResourceId"]}, "PathPart": "generate", "RestApiId": {"Ref": "SVGGeneratorAPIF54FD110"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/SVGGeneratorAPI/Default/generate/Resource"}}, "SVGGeneratorAPIgenerateOPTIONS3B980618": {"Type": "AWS::ApiGateway::Method", "Properties": {"ApiKeyRequired": false, "AuthorizationType": "NONE", "HttpMethod": "OPTIONS", "Integration": {"IntegrationResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,Authorization'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}, "StatusCode": "204"}], "RequestTemplates": {"application/json": "{ statusCode: 200 }"}, "Type": "MOCK"}, "MethodResponses": [{"ResponseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}, "StatusCode": "204"}], "ResourceId": {"Ref": "SVGGeneratorAPIgenerateF65226DF"}, "RestApiId": {"Ref": "SVGGeneratorAPIF54FD110"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/SVGGeneratorAPI/Default/generate/OPTIONS/Resource"}}, "SVGGeneratorAPIgeneratePOSTApiPermissionJerseyColoursStackSVGGeneratorAPIA9C7E49BPOSTgenerate62E49712": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["SVGGeneratorFunction1FB593CD", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:ap-southeast-2:************:", {"Ref": "SVGGeneratorAPIF54FD110"}, "/", {"Ref": "SVGGeneratorAPIDeploymentStageprod32256403"}, "/POST/generate"]]}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/SVGGeneratorAPI/Default/generate/POST/ApiPermission.JerseyColoursStackSVGGeneratorAPIA9C7E49B.POST..generate"}}, "SVGGeneratorAPIgeneratePOSTApiPermissionTestJerseyColoursStackSVGGeneratorAPIA9C7E49BPOSTgenerate3B3A9B10": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["SVGGeneratorFunction1FB593CD", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:ap-southeast-2:************:", {"Ref": "SVGGeneratorAPIF54FD110"}, "/test-invoke-stage/POST/generate"]]}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/SVGGeneratorAPI/Default/generate/POST/ApiPermission.Test.JerseyColoursStackSVGGeneratorAPIA9C7E49B.POST..generate"}}, "SVGGeneratorAPIgeneratePOSTDC9F0BBB": {"Type": "AWS::ApiGateway::Method", "Properties": {"AuthorizationType": "NONE", "HttpMethod": "POST", "Integration": {"IntegrationHttpMethod": "POST", "Type": "AWS_PROXY", "Uri": {"Fn::Join": ["", ["arn:aws:apigateway:ap-southeast-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["SVGGeneratorFunction1FB593CD", "<PERSON><PERSON>"]}, "/invocations"]]}}, "ResourceId": {"Ref": "SVGGeneratorAPIgenerateF65226DF"}, "RestApiId": {"Ref": "SVGGeneratorAPIF54FD110"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/SVGGeneratorAPI/Default/generate/POST/Resource"}}, "JerseyColoursDistributionOrigin4S3Origin35EAB48A": {"Type": "AWS::CloudFront::CloudFrontOriginAccessIdentity", "Properties": {"CloudFrontOriginAccessIdentityConfig": {"Comment": "Identity for JerseyColoursStackJerseyColoursDistributionOrigin45280CFB2"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursDistribution/Origin4/S3Origin/Resource"}}, "JerseyColoursDistributionCB46AE9E": {"Type": "AWS::CloudFront::Distribution", "Properties": {"DistributionConfig": {"CacheBehaviors": [{"AllowedMethods": ["GET", "HEAD", "OPTIONS", "PUT", "PATCH", "POST", "DELETE"], "CachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad", "Compress": true, "PathPattern": "/api/*", "TargetOriginId": "JerseyColoursStackJerseyColoursDistributionOrigin2FFE74F6B", "ViewerProtocolPolicy": "redirect-to-https"}, {"CachePolicyId": "658327ea-f89d-4fab-a63d-7e88639e58f6", "Compress": true, "PathPattern": "/svg/*", "TargetOriginId": "JerseyColoursStackJerseyColoursDistributionOrigin34078A2E8", "ViewerProtocolPolicy": "redirect-to-https"}, {"CachePolicyId": "658327ea-f89d-4fab-a63d-7e88639e58f6", "Compress": true, "PathPattern": "/patterns/*", "TargetOriginId": "JerseyColoursStackJerseyColoursDistributionOrigin45280CFB2", "ViewerProtocolPolicy": "redirect-to-https"}], "DefaultCacheBehavior": {"CachePolicyId": "658327ea-f89d-4fab-a63d-7e88639e58f6", "Compress": true, "TargetOriginId": "JerseyColoursStackJerseyColoursDistributionOrigin1C9CF29C7", "ViewerProtocolPolicy": "redirect-to-https"}, "Enabled": true, "HttpVersion": "http2", "IPV6Enabled": true, "Origins": [{"CustomOriginConfig": {"OriginProtocolPolicy": "http-only", "OriginSSLProtocols": ["TLSv1.2"]}, "DomainName": {"Fn::GetAtt": ["FrontendServiceLBB5764C7D", "DNSName"]}, "Id": "JerseyColoursStackJerseyColoursDistributionOrigin1C9CF29C7"}, {"CustomOriginConfig": {"OriginProtocolPolicy": "http-only", "OriginSSLProtocols": ["TLSv1.2"]}, "DomainName": {"Fn::GetAtt": ["ApiServiceLBA5369AC5", "DNSName"]}, "Id": "JerseyColoursStackJerseyColoursDistributionOrigin2FFE74F6B"}, {"CustomOriginConfig": {"OriginProtocolPolicy": "https-only", "OriginSSLProtocols": ["TLSv1.2"]}, "DomainName": {"Fn::Select": [2, {"Fn::Split": ["/", {"Fn::Join": ["", ["https://", {"Ref": "SVGGeneratorAPIF54FD110"}, ".execute-api.ap-southeast-2.", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "SVGGeneratorAPIDeploymentStageprod32256403"}, "/"]]}]}]}, "Id": "JerseyColoursStackJerseyColoursDistributionOrigin34078A2E8", "OriginPath": {"Fn::Join": ["", ["/", {"Fn::Select": [3, {"Fn::Split": ["/", {"Fn::Join": ["", ["https://", {"Ref": "SVGGeneratorAPIF54FD110"}, ".execute-api.ap-southeast-2.", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "SVGGeneratorAPIDeploymentStageprod32256403"}, "/"]]}]}]}]]}}, {"DomainName": {"Fn::GetAtt": ["PatternFilesBucket4B88CA60", "RegionalDomainName"]}, "Id": "JerseyColoursStackJerseyColoursDistributionOrigin45280CFB2", "S3OriginConfig": {"OriginAccessIdentity": {"Fn::Join": ["", ["origin-access-identity/cloudfront/", {"Ref": "JerseyColoursDistributionOrigin4S3Origin35EAB48A"}]]}}}], "PriceClass": "PriceClass_100"}}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/JerseyColoursDistribution/Resource"}}, "CDKMetadata": {"Type": "AWS::CDK::Metadata", "Properties": {"Analytics": "v2:deflate64:H4sIAAAAAAAA/31S227bMAz9lr6rWpYN2HPStEWAbjWSoq8FLbMuG1syRCpFYPjfB1l27GaXJx6eQ0q8LfVy8V0vruCDr01xuK4o1+1ewBwUfPBLi2ap2+fGqJtX+5zdqCzkFZl9yC1K5Ca0c0HwCfIKJ37iVszOEAg5ew6O4HabRfML5B4EP+CkMk9HEJwe3lpBb/EckCoZvJUImLcarag9muBJTvfehaav4b/E1pYemf/gb3u6U/xNt+tgDqmIASWTuYrMaaIHPzlrYOyUL1i3GxDIgXFrWcAaVKmpcz2b9ZwYo/dofPp0sx4zO8U9yzVYKNHrNpUdA5N5Al+izMZxDrhUOoXG63aHjWMS5/tGJi/KrNubKrCgj9oI78CXEPfJhw2+kqVxm5eMswJk0c+4IXeP/kgmXUiCnSKodbtz6W56O803oU5hBSxkKgdFDhVYQ7Y8LnW7apqKTH9WDw6Kda+lqj/58zhiQTvEjHimp3GddzRzO1W5knX74MqzPOJOVVDnBej2LlgzDmaOM/Q1MZOz8bZegBmF9SoaBQ2V6Z7jXlhWDaWlJLjBpnKncaszby9QpmH2YIfsgjcYT1D9RHlzRRQHNKrDyz3ulKlcKF69s6LbDbF4ykNf8qOnkuzKGGTeFmiF5JSuwYXiLib8K2L+TNfN/g0srv5bGRdS5t2RCvR9H/2EYn9ky5jzGKQJ0inrCtTv/OW4XOivP/Ti6p2Jrn2wQjXqXbK/AWLkA5TfBAAA"}, "Metadata": {"aws:cdk:path": "JerseyColoursStack/CDKMetadata/Default"}}}, "Outputs": {"SVGGeneratorAPIEndpoint0D61CFEC": {"Value": {"Fn::Join": ["", ["https://", {"Ref": "SVGGeneratorAPIF54FD110"}, ".execute-api.ap-southeast-2.", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "SVGGeneratorAPIDeploymentStageprod32256403"}, "/"]]}}, "DatabaseEndpoint": {"Description": "RDS Database Endpoint", "Value": {"Fn::GetAtt": ["JerseyColoursDB5E7999F9", "Endpoint.Address"]}}, "ApiLoadBalancerDNS": {"Description": "API Load Balancer DNS", "Value": {"Fn::GetAtt": ["ApiServiceLBA5369AC5", "DNSName"]}}, "FrontendLoadBalancerDNS": {"Description": "Frontend Load Balancer DNS", "Value": {"Fn::GetAtt": ["FrontendServiceLBB5764C7D", "DNSName"]}}, "CloudFrontDomainName": {"Description": "CloudFront Distribution Domain Name", "Value": {"Fn::GetAtt": ["JerseyColoursDistributionCB46AE9E", "DomainName"]}}, "S3BucketName": {"Description": "S3 Bucket for Pattern Files", "Value": {"Ref": "PatternFilesBucket4B88CA60"}}, "SVGGeneratorAPIEndpoint": {"Description": "SVG Generator API Endpoint", "Value": {"Fn::Join": ["", ["https://", {"Ref": "SVGGeneratorAPIF54FD110"}, ".execute-api.ap-southeast-2.", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "SVGGeneratorAPIDeploymentStageprod32256403"}, "/"]]}}, "ApiRepositoryURI": {"Description": "ECR Repository URI for API", "Value": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["ApiRepositoryB8378B43", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["ApiRepositoryB8378B43", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "ApiRepositoryB8378B43"}]]}}, "FrontendRepositoryURI": {"Description": "ECR Repository URI for Frontend", "Value": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["FrontendRepository7D714FA2", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["FrontendRepository7D714FA2", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "FrontendRepository7D714FA2"}]]}}}, "Parameters": {"BootstrapVersion": {"Type": "AWS::SSM::Parameter::Value<String>", "Default": "/cdk-bootstrap/hnb659fds/version", "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]"}}, "Rules": {"CheckBootstrapVersion": {"Assertions": [{"Assert": {"Fn::Not": [{"Fn::Contains": [["1", "2", "3", "4", "5"], {"Ref": "BootstrapVersion"}]}]}, "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI."}]}}}