{"version": "tree-0.1", "tree": {"id": "App", "path": "", "constructInfo": {"fqn": "aws-cdk-lib.App", "version": "2.204.0"}, "children": {"JerseyColoursStack": {"id": "JerseyColoursStack", "path": "JerseyColoursStack", "constructInfo": {"fqn": "aws-cdk-lib.<PERSON><PERSON>", "version": "2.204.0"}, "children": {"JerseyColoursVPC": {"id": "JerseyColoursVPC", "path": "JerseyColoursStack/JerseyColoursVPC", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.Vpc", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/JerseyColoursVPC/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnVPC", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::VPC", "aws:cdk:cloudformation:props": {"cidrBlock": "10.0.0.0/16", "enableDnsHostnames": true, "enableDnsSupport": true, "instanceTenancy": "default", "tags": [{"key": "Name", "value": "JerseyColoursStack/JerseyColoursVPC"}]}}}, "PublicSubnet1": {"id": "PublicSubnet1", "path": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet1", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.PublicSubnet", "version": "2.204.0", "metadata": []}, "children": {"Subnet": {"id": "Subnet", "path": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet1/Subnet", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnet", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::Subnet", "aws:cdk:cloudformation:props": {"availabilityZone": "ap-southeast-2a", "cidrBlock": "10.0.0.0/24", "mapPublicIpOnLaunch": true, "tags": [{"key": "aws-cdk:subnet-name", "value": "Public"}, {"key": "aws-cdk:subnet-type", "value": "Public"}, {"key": "Name", "value": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet1"}], "vpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}}}, "Acl": {"id": "Acl", "path": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet1/Acl", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.204.0", "metadata": []}}, "RouteTable": {"id": "RouteTable", "path": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet1/RouteTable", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnRouteTable", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::RouteTable", "aws:cdk:cloudformation:props": {"tags": [{"key": "Name", "value": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet1"}], "vpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}}}, "RouteTableAssociation": {"id": "RouteTableAssociation", "path": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet1/RouteTableAssociation", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnetRouteTableAssociation", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SubnetRouteTableAssociation", "aws:cdk:cloudformation:props": {"routeTableId": {"Ref": "JerseyColoursVPCPublicSubnet1RouteTableD9ADFDC6"}, "subnetId": {"Ref": "JerseyColoursVPCPublicSubnet1Subnet723163B5"}}}}, "DefaultRoute": {"id": "DefaultRoute", "path": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet1/DefaultRoute", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnRoute", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::Route", "aws:cdk:cloudformation:props": {"destinationCidrBlock": "0.0.0.0/0", "gatewayId": {"Ref": "JerseyColoursVPCIGW714B0DB7"}, "routeTableId": {"Ref": "JerseyColoursVPCPublicSubnet1RouteTableD9ADFDC6"}}}}, "EIP": {"id": "EIP", "path": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet1/EIP", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnEIP", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::EIP", "aws:cdk:cloudformation:props": {"domain": "vpc", "tags": [{"key": "Name", "value": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet1"}]}}}, "NATGateway": {"id": "NATGateway", "path": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet1/NATGateway", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnNatGateway", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::NatGateway", "aws:cdk:cloudformation:props": {"allocationId": {"Fn::GetAtt": ["JerseyColoursVPCPublicSubnet1EIPA620A1EE", "AllocationId"]}, "subnetId": {"Ref": "JerseyColoursVPCPublicSubnet1Subnet723163B5"}, "tags": [{"key": "Name", "value": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet1"}]}}}}}, "PublicSubnet2": {"id": "PublicSubnet2", "path": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet2", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.PublicSubnet", "version": "2.204.0", "metadata": []}, "children": {"Subnet": {"id": "Subnet", "path": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet2/Subnet", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnet", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::Subnet", "aws:cdk:cloudformation:props": {"availabilityZone": "ap-southeast-2b", "cidrBlock": "********/24", "mapPublicIpOnLaunch": true, "tags": [{"key": "aws-cdk:subnet-name", "value": "Public"}, {"key": "aws-cdk:subnet-type", "value": "Public"}, {"key": "Name", "value": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet2"}], "vpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}}}, "Acl": {"id": "Acl", "path": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet2/Acl", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.204.0", "metadata": []}}, "RouteTable": {"id": "RouteTable", "path": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet2/RouteTable", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnRouteTable", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::RouteTable", "aws:cdk:cloudformation:props": {"tags": [{"key": "Name", "value": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet2"}], "vpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}}}, "RouteTableAssociation": {"id": "RouteTableAssociation", "path": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet2/RouteTableAssociation", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnetRouteTableAssociation", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SubnetRouteTableAssociation", "aws:cdk:cloudformation:props": {"routeTableId": {"Ref": "JerseyColoursVPCPublicSubnet2RouteTable3A6F674E"}, "subnetId": {"Ref": "JerseyColoursVPCPublicSubnet2Subnet6C002866"}}}}, "DefaultRoute": {"id": "DefaultRoute", "path": "JerseyColoursStack/JerseyColoursVPC/PublicSubnet2/DefaultRoute", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnRoute", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::Route", "aws:cdk:cloudformation:props": {"destinationCidrBlock": "0.0.0.0/0", "gatewayId": {"Ref": "JerseyColoursVPCIGW714B0DB7"}, "routeTableId": {"Ref": "JerseyColoursVPCPublicSubnet2RouteTable3A6F674E"}}}}}}, "PrivateSubnet1": {"id": "PrivateSubnet1", "path": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet1", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.PrivateSubnet", "version": "2.204.0", "metadata": []}, "children": {"Subnet": {"id": "Subnet", "path": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet1/Subnet", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnet", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::Subnet", "aws:cdk:cloudformation:props": {"availabilityZone": "ap-southeast-2a", "cidrBlock": "********/24", "mapPublicIpOnLaunch": false, "tags": [{"key": "aws-cdk:subnet-name", "value": "Private"}, {"key": "aws-cdk:subnet-type", "value": "Private"}, {"key": "Name", "value": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet1"}], "vpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}}}, "Acl": {"id": "Acl", "path": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet1/Acl", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.204.0", "metadata": []}}, "RouteTable": {"id": "RouteTable", "path": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet1/RouteTable", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnRouteTable", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::RouteTable", "aws:cdk:cloudformation:props": {"tags": [{"key": "Name", "value": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet1"}], "vpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}}}, "RouteTableAssociation": {"id": "RouteTableAssociation", "path": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet1/RouteTableAssociation", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnetRouteTableAssociation", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SubnetRouteTableAssociation", "aws:cdk:cloudformation:props": {"routeTableId": {"Ref": "JerseyColoursVPCPrivateSubnet1RouteTableCAD830A6"}, "subnetId": {"Ref": "JerseyColoursVPCPrivateSubnet1Subnet0DD79F72"}}}}, "DefaultRoute": {"id": "DefaultRoute", "path": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet1/DefaultRoute", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnRoute", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::Route", "aws:cdk:cloudformation:props": {"destinationCidrBlock": "0.0.0.0/0", "natGatewayId": {"Ref": "JerseyColoursVPCPublicSubnet1NATGatewayB044EC76"}, "routeTableId": {"Ref": "JerseyColoursVPCPrivateSubnet1RouteTableCAD830A6"}}}}}}, "PrivateSubnet2": {"id": "PrivateSubnet2", "path": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet2", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.PrivateSubnet", "version": "2.204.0", "metadata": []}, "children": {"Subnet": {"id": "Subnet", "path": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet2/Subnet", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnet", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::Subnet", "aws:cdk:cloudformation:props": {"availabilityZone": "ap-southeast-2b", "cidrBlock": "********/24", "mapPublicIpOnLaunch": false, "tags": [{"key": "aws-cdk:subnet-name", "value": "Private"}, {"key": "aws-cdk:subnet-type", "value": "Private"}, {"key": "Name", "value": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet2"}], "vpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}}}, "Acl": {"id": "Acl", "path": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet2/Acl", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.204.0", "metadata": []}}, "RouteTable": {"id": "RouteTable", "path": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet2/RouteTable", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnRouteTable", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::RouteTable", "aws:cdk:cloudformation:props": {"tags": [{"key": "Name", "value": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet2"}], "vpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}}}, "RouteTableAssociation": {"id": "RouteTableAssociation", "path": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet2/RouteTableAssociation", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnetRouteTableAssociation", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SubnetRouteTableAssociation", "aws:cdk:cloudformation:props": {"routeTableId": {"Ref": "JerseyColoursVPCPrivateSubnet2RouteTable340D8A18"}, "subnetId": {"Ref": "JerseyColoursVPCPrivateSubnet2Subnet5FC14D8F"}}}}, "DefaultRoute": {"id": "DefaultRoute", "path": "JerseyColoursStack/JerseyColoursVPC/PrivateSubnet2/DefaultRoute", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnRoute", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::Route", "aws:cdk:cloudformation:props": {"destinationCidrBlock": "0.0.0.0/0", "natGatewayId": {"Ref": "JerseyColoursVPCPublicSubnet1NATGatewayB044EC76"}, "routeTableId": {"Ref": "JerseyColoursVPCPrivateSubnet2RouteTable340D8A18"}}}}}}, "DatabaseSubnet1": {"id": "DatabaseSubnet1", "path": "JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet1", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.PrivateSubnet", "version": "2.204.0", "metadata": []}, "children": {"Subnet": {"id": "Subnet", "path": "JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet1/Subnet", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnet", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::Subnet", "aws:cdk:cloudformation:props": {"availabilityZone": "ap-southeast-2a", "cidrBlock": "********/28", "mapPublicIpOnLaunch": false, "tags": [{"key": "aws-cdk:subnet-name", "value": "Database"}, {"key": "aws-cdk:subnet-type", "value": "Isolated"}, {"key": "Name", "value": "JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet1"}], "vpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}}}, "Acl": {"id": "Acl", "path": "JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet1/Acl", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.204.0", "metadata": []}}, "RouteTable": {"id": "RouteTable", "path": "JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet1/RouteTable", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnRouteTable", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::RouteTable", "aws:cdk:cloudformation:props": {"tags": [{"key": "Name", "value": "JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet1"}], "vpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}}}, "RouteTableAssociation": {"id": "RouteTableAssociation", "path": "JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet1/RouteTableAssociation", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnetRouteTableAssociation", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SubnetRouteTableAssociation", "aws:cdk:cloudformation:props": {"routeTableId": {"Ref": "JerseyColoursVPCDatabaseSubnet1RouteTable2F92102E"}, "subnetId": {"Ref": "JerseyColoursVPCDatabaseSubnet1SubnetF1B293F7"}}}}}}, "DatabaseSubnet2": {"id": "DatabaseSubnet2", "path": "JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet2", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.PrivateSubnet", "version": "2.204.0", "metadata": []}, "children": {"Subnet": {"id": "Subnet", "path": "JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet2/Subnet", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnet", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::Subnet", "aws:cdk:cloudformation:props": {"availabilityZone": "ap-southeast-2b", "cidrBlock": "*********/28", "mapPublicIpOnLaunch": false, "tags": [{"key": "aws-cdk:subnet-name", "value": "Database"}, {"key": "aws-cdk:subnet-type", "value": "Isolated"}, {"key": "Name", "value": "JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet2"}], "vpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}}}, "Acl": {"id": "Acl", "path": "JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet2/Acl", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.204.0", "metadata": []}}, "RouteTable": {"id": "RouteTable", "path": "JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet2/RouteTable", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnRouteTable", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::RouteTable", "aws:cdk:cloudformation:props": {"tags": [{"key": "Name", "value": "JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet2"}], "vpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}}}, "RouteTableAssociation": {"id": "RouteTableAssociation", "path": "JerseyColoursStack/JerseyColoursVPC/DatabaseSubnet2/RouteTableAssociation", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSubnetRouteTableAssociation", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SubnetRouteTableAssociation", "aws:cdk:cloudformation:props": {"routeTableId": {"Ref": "JerseyColoursVPCDatabaseSubnet2RouteTable8F0B2487"}, "subnetId": {"Ref": "JerseyColoursVPCDatabaseSubnet2Subnet4AA74667"}}}}}}, "IGW": {"id": "IGW", "path": "JerseyColoursStack/JerseyColoursVPC/IGW", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnInternetGateway", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::InternetGateway", "aws:cdk:cloudformation:props": {"tags": [{"key": "Name", "value": "JerseyColoursStack/JerseyColoursVPC"}]}}}, "VPCGW": {"id": "VPCGW", "path": "JerseyColoursStack/JerseyColoursVPC/VPCGW", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnVPCGatewayAttachment", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::VPCGatewayAttachment", "aws:cdk:cloudformation:props": {"internetGatewayId": {"Ref": "JerseyColoursVPCIGW714B0DB7"}, "vpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}}}, "RestrictDefaultSecurityGroupCustomResource": {"id": "RestrictDefaultSecurityGroupCustomResource", "path": "JerseyColoursStack/JerseyColoursVPC/RestrictDefaultSecurityGroupCustomResource", "constructInfo": {"fqn": "aws-cdk-lib.CustomResource", "version": "2.204.0", "metadata": []}, "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "JerseyColoursStack/JerseyColoursVPC/RestrictDefaultSecurityGroupCustomResource/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.204.0"}}}}}}, "Custom::VpcRestrictDefaultSGCustomResourceProvider": {"id": "Custom::VpcRestrictDefaultSGCustomResourceProvider", "path": "JerseyColoursStack/Custom::VpcRestrictDefaultSGCustomResourceProvider", "constructInfo": {"fqn": "aws-cdk-lib.CustomResourceProviderBase", "version": "2.204.0"}, "children": {"Staging": {"id": "Staging", "path": "JerseyColoursStack/Custom::VpcRestrictDefaultSGCustomResourceProvider/Staging", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.204.0"}}, "Role": {"id": "Role", "path": "JerseyColoursStack/Custom::VpcRestrictDefaultSGCustomResourceProvider/Role", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.204.0"}}, "Handler": {"id": "Handler", "path": "JerseyColoursStack/Custom::VpcRestrictDefaultSGCustomResourceProvider/Handler", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.204.0"}}}}, "PatternFilesBucket": {"id": "PatternFilesBucket", "path": "JerseyColoursStack/PatternFilesBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.Bucket", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/PatternFilesBucket/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.CfnBucket", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::S3::<PERSON><PERSON>", "aws:cdk:cloudformation:props": {"bucketEncryption": {"serverSideEncryptionConfiguration": [{"serverSideEncryptionByDefault": {"sseAlgorithm": "AES256"}}]}, "bucketName": "jersey-colours-patterns-699965966019-ap-southeast-2", "corsConfiguration": {"corsRules": [{"maxAge": 3000, "allowedHeaders": ["*"], "allowedMethods": ["GET", "POST", "PUT"], "allowedOrigins": ["*"]}]}, "lifecycleConfiguration": {"rules": [{"id": "DeleteOldVersions", "noncurrentVersionExpiration": {"noncurrentDays": 30}, "status": "Enabled"}]}, "publicAccessBlockConfiguration": {"blockPublicAcls": true, "blockPublicPolicy": true, "ignorePublicAcls": true, "restrictPublicBuckets": true}, "versioningConfiguration": {"status": "Enabled"}}}}, "Policy": {"id": "Policy", "path": "JerseyColoursStack/PatternFilesBucket/Policy", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketPolicy", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/PatternFilesBucket/Policy/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.CfnBucketPolicy", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::S3::BucketPolicy", "aws:cdk:cloudformation:props": {"bucket": {"Ref": "PatternFilesBucket4B88CA60"}, "policyDocument": {"Statement": [{"Action": "s3:GetObject", "Effect": "Allow", "Principal": {"CanonicalUser": {"Fn::GetAtt": ["JerseyColoursDistributionOrigin4S3Origin35EAB48A", "S3CanonicalUserId"]}}, "Resource": {"Fn::Join": ["", [{"Fn::GetAtt": ["PatternFilesBucket4B88CA60", "<PERSON><PERSON>"]}, "/*"]]}}], "Version": "2012-10-17"}}}}}}}}, "JerseyColoursDB": {"id": "JerseyColoursDB", "path": "JerseyColoursStack/JerseyColoursDB", "constructInfo": {"fqn": "aws-cdk-lib.aws_rds.DatabaseInstance", "version": "2.204.0", "metadata": []}, "children": {"SubnetGroup": {"id": "SubnetGroup", "path": "JerseyColoursStack/JerseyColoursDB/SubnetGroup", "constructInfo": {"fqn": "aws-cdk-lib.aws_rds.SubnetGroup", "version": "2.204.0", "metadata": []}, "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "JerseyColoursStack/JerseyColoursDB/SubnetGroup/Default", "constructInfo": {"fqn": "aws-cdk-lib.aws_rds.CfnDBSubnetGroup", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::RDS::DBSubnetGroup", "aws:cdk:cloudformation:props": {"dbSubnetGroupDescription": "Subnet group for JerseyColoursDB database", "subnetIds": [{"Ref": "JerseyColoursVPCDatabaseSubnet1SubnetF1B293F7"}, {"Ref": "JerseyColoursVPCDatabaseSubnet2Subnet4AA74667"}]}}}}}, "SecurityGroup": {"id": "SecurityGroup", "path": "JerseyColoursStack/JerseyColoursDB/SecurityGroup", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.SecurityGroup", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/JerseyColoursDB/SecurityGroup/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSecurityGroup", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SecurityGroup", "aws:cdk:cloudformation:props": {"groupDescription": "Security group for JerseyColoursDB database", "securityGroupEgress": [{"cidrIp": "0.0.0.0/0", "description": "Allow all outbound traffic by default", "ipProtocol": "-1"}], "vpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}}}, "from JerseyColoursStackApiServiceSecurityGroup5BAD4259:5432": {"id": "from JerseyColoursStackApiServiceSecurityGroup5BAD4259:5432", "path": "JerseyColoursStack/JerseyColoursDB/SecurityGroup/from JerseyColoursStackApiServiceSecurityGroup5BAD4259:5432", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSecurityGroupIngress", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SecurityGroupIngress", "aws:cdk:cloudformation:props": {"description": "from JerseyColoursStackApiServiceSecurityGroup5BAD4259:5432", "fromPort": 5432, "groupId": {"Fn::GetAtt": ["JerseyColoursDBSecurityGroup08D5F746", "GroupId"]}, "ipProtocol": "tcp", "sourceSecurityGroupId": {"Fn::GetAtt": ["ApiServiceSecurityGroupA2426F91", "GroupId"]}, "toPort": 5432}}}}}, "Secret": {"id": "Secret", "path": "JerseyColoursStack/JerseyColoursDB/Secret", "constructInfo": {"fqn": "aws-cdk-lib.aws_rds.DatabaseSecret", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/JerseyColoursDB/Secret/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_secretsmanager.CfnSecret", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::<PERSON>Manager::Secret", "aws:cdk:cloudformation:props": {"description": {"Fn::Join": ["", ["Generated by the CDK for stack: ", {"Ref": "AWS::StackName"}]]}, "generateSecretString": {"passwordLength": 30, "secretStringTemplate": "{\"username\":\"postgres\"}", "generateStringKey": "password", "excludeCharacters": " %+~`#$&*()|[]{}:;<>?!'/@\"\\"}, "name": "jersey-colours-db-credentials"}}}, "Attachment": {"id": "Attachment", "path": "JerseyColoursStack/JerseyColoursDB/Secret/Attachment", "constructInfo": {"fqn": "aws-cdk-lib.aws_secretsmanager.SecretTargetAttachment", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/JerseyColoursDB/Secret/Attachment/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_secretsmanager.CfnSecretTargetAttachment", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::SecretsManager::SecretTargetAttachment", "aws:cdk:cloudformation:props": {"secretId": {"Ref": "JerseyColoursStackJerseyColoursDBSecret34B4DFD33fdaad7efa858a3daf9490cf0a702aeb"}, "targetId": {"Ref": "JerseyColoursDB5E7999F9"}, "targetType": "AWS::RDS::DBInstance"}}}}}}}, "Resource": {"id": "Resource", "path": "JerseyColoursStack/JerseyColoursDB/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_rds.CfnDBInstance", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::RDS::DBInstance", "aws:cdk:cloudformation:props": {"allocatedStorage": "20", "backupRetentionPeriod": 7, "copyTagsToSnapshot": true, "dbInstanceClass": "db.t3.micro", "dbName": "jersey_colours", "dbSubnetGroupName": {"Ref": "JerseyColoursDBSubnetGroupBC34C44B"}, "deleteAutomatedBackups": true, "deletionProtection": false, "engine": "postgres", "engineVersion": "15", "masterUsername": "postgres", "masterUserPassword": {"Fn::Join": ["", ["{{resolve:secretsmanager:", {"Ref": "JerseyColoursStackJerseyColoursDBSecret34B4DFD33fdaad7efa858a3daf9490cf0a702aeb"}, ":SecretString:password::}}"]]}, "maxAllocatedStorage": 100, "multiAz": false, "publiclyAccessible": false, "storageEncrypted": true, "storageType": "gp2", "vpcSecurityGroups": [{"Fn::GetAtt": ["JerseyColoursDBSecurityGroup08D5F746", "GroupId"]}]}}}}}, "ApiRepository": {"id": "ApiRepository", "path": "JerseyColoursStack/ApiRepository", "constructInfo": {"fqn": "aws-cdk-lib.aws_ecr.Repository", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/ApiRepository/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_ecr.CfnRepository", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ECR::Repository", "aws:cdk:cloudformation:props": {"imageScanningConfiguration": {"scanOnPush": true}, "lifecyclePolicy": {"lifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"selection\":{\"tagStatus\":\"any\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}}]}"}, "repositoryName": "jersey-colours-api"}}}}}, "FrontendRepository": {"id": "FrontendRepository", "path": "JerseyColoursStack/FrontendRepository", "constructInfo": {"fqn": "aws-cdk-lib.aws_ecr.Repository", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/FrontendRepository/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_ecr.CfnRepository", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ECR::Repository", "aws:cdk:cloudformation:props": {"imageScanningConfiguration": {"scanOnPush": true}, "lifecyclePolicy": {"lifecyclePolicyText": "{\"rules\":[{\"rulePriority\":1,\"selection\":{\"tagStatus\":\"any\",\"countType\":\"imageCountMoreThan\",\"countNumber\":10},\"action\":{\"type\":\"expire\"}}]}"}, "repositoryName": "jersey-colours-frontend"}}}}}, "JerseyColoursCluster": {"id": "JerseyColoursCluster", "path": "JerseyColoursStack/JerseyColoursCluster", "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.Cluster", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/JerseyColoursCluster/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.CfnCluster", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ECS::Cluster", "aws:cdk:cloudformation:props": {"clusterSettings": [{"name": "containerInsights", "value": "enabled"}]}}}}}, "TaskExecutionRole": {"id": "TaskExecutionRole", "path": "JerseyColoursStack/TaskExecutionRole", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.204.0", "metadata": []}, "children": {"ImportTaskExecutionRole": {"id": "ImportTaskExecutionRole", "path": "JerseyColoursStack/TaskExecutionRole/ImportTaskExecutionRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.204.0", "metadata": []}}, "Resource": {"id": "Resource", "path": "JerseyColoursStack/TaskExecutionRole/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "ecs-tasks.amazonaws.com"}}], "Version": "2012-10-17"}, "managedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"]]}]}}}, "DefaultPolicy": {"id": "DefaultPolicy", "path": "JerseyColoursStack/TaskExecutionRole/DefaultPolicy", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Policy", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/TaskExecutionRole/DefaultPolicy/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnPolicy", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Policy", "aws:cdk:cloudformation:props": {"policyDocument": {"Statement": [{"Action": ["ecr:BatchCheckLayerAvailability", "ecr:BatchGetImage", "ecr:GetDownloadUrlForLayer"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["ApiRepositoryB8378B43", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["FrontendRepository7D714FA2", "<PERSON><PERSON>"]}]}, {"Action": "ecr:GetAuthorizationToken", "Effect": "Allow", "Resource": "*"}, {"Action": ["logs:CreateLogStream", "logs:PutLogEvents"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["ApiServiceLogGroup42225A1C", "<PERSON><PERSON>"]}, {"Fn::GetAtt": ["FrontendServiceLogGroup99775B35", "<PERSON><PERSON>"]}]}, {"Action": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "Effect": "Allow", "Resource": {"Ref": "JerseyColoursDBSecretAttachmentD17E6A2D"}}], "Version": "2012-10-17"}, "policyName": "TaskExecutionRoleDefaultPolicyA84DD1B0", "roles": [{"Ref": "TaskExecutionRole250D2532"}]}}}}}}}, "ApiTaskRole": {"id": "ApiTaskRole", "path": "JerseyColoursStack/ApiTaskRole", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.204.0", "metadata": []}, "children": {"ImportApiTaskRole": {"id": "ImportApiTaskRole", "path": "JerseyColoursStack/ApiTaskRole/ImportApiTaskRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.204.0", "metadata": []}}, "Resource": {"id": "Resource", "path": "JerseyColoursStack/ApiTaskRole/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "ecs-tasks.amazonaws.com"}}], "Version": "2012-10-17"}}}}, "DefaultPolicy": {"id": "DefaultPolicy", "path": "JerseyColoursStack/ApiTaskRole/DefaultPolicy", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Policy", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/ApiTaskRole/DefaultPolicy/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnPolicy", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Policy", "aws:cdk:cloudformation:props": {"policyDocument": {"Statement": [{"Action": ["s3:Abort*", "s3:DeleteObject*", "s3:GetBucket*", "s3:GetObject*", "s3:List*", "s3:PutObject", "s3:PutObjectLegalHold", "s3:PutObjectRetention", "s3:PutObjectTagging", "s3:PutObjectVersionTagging"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["PatternFilesBucket4B88CA60", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["PatternFilesBucket4B88CA60", "<PERSON><PERSON>"]}, "/*"]]}]}, {"Action": ["secretsmanager:Describe<PERSON><PERSON><PERSON>", "secretsmanager:GetSecretValue"], "Effect": "Allow", "Resource": {"Ref": "JerseyColoursDBSecretAttachmentD17E6A2D"}}], "Version": "2012-10-17"}, "policyName": "ApiTaskRoleDefaultPolicyBEF5D530", "roles": [{"Ref": "ApiTaskRole12FAD4A7"}]}}}}}}}, "ApiServiceLB": {"id": "ApiServiceLB", "path": "JerseyColoursStack/ApiServiceLB", "constructInfo": {"fqn": "aws-cdk-lib.aws_elasticloadbalancingv2.ApplicationLoadBalancer", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/ApiServiceLB/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_elasticloadbalancingv2.CfnLoadBalancer", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ElasticLoadBalancingV2::LoadBalancer", "aws:cdk:cloudformation:props": {"loadBalancerAttributes": [{"key": "deletion_protection.enabled", "value": "false"}], "scheme": "internet-facing", "securityGroups": [{"Fn::GetAtt": ["ApiServiceLBSecurityGroup0ACC3F19", "GroupId"]}], "subnets": [{"Ref": "JerseyColoursVPCPublicSubnet1Subnet723163B5"}, {"Ref": "JerseyColoursVPCPublicSubnet2Subnet6C002866"}], "type": "application"}}}, "SecurityGroup": {"id": "SecurityGroup", "path": "JerseyColoursStack/ApiServiceLB/SecurityGroup", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.SecurityGroup", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/ApiServiceLB/SecurityGroup/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSecurityGroup", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SecurityGroup", "aws:cdk:cloudformation:props": {"groupDescription": "Automatically created Security Group for ELB JerseyColoursStackApiServiceLBD76B9D50", "securityGroupIngress": [{"cidrIp": "0.0.0.0/0", "ipProtocol": "tcp", "fromPort": 80, "toPort": 80, "description": "Allow from anyone on port 80"}], "vpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}}}, "to JerseyColoursStackApiServiceSecurityGroup5BAD4259:3000": {"id": "to JerseyColoursStackApiServiceSecurityGroup5BAD4259:3000", "path": "JerseyColoursStack/ApiServiceLB/SecurityGroup/to JerseyColoursStackApiServiceSecurityGroup5BAD4259:3000", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSecurityGroupEgress", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SecurityGroupEgress", "aws:cdk:cloudformation:props": {"description": "Load balancer to target", "destinationSecurityGroupId": {"Fn::GetAtt": ["ApiServiceSecurityGroupA2426F91", "GroupId"]}, "fromPort": 3000, "groupId": {"Fn::GetAtt": ["ApiServiceLBSecurityGroup0ACC3F19", "GroupId"]}, "ipProtocol": "tcp", "toPort": 3000}}}}}, "ApiServiceListener": {"id": "ApiServiceListener", "path": "JerseyColoursStack/ApiServiceLB/ApiServiceListener", "constructInfo": {"fqn": "aws-cdk-lib.aws_elasticloadbalancingv2.ApplicationListener", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/ApiServiceLB/ApiServiceListener/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_elasticloadbalancingv2.CfnListener", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ElasticLoadBalancingV2::Listener", "aws:cdk:cloudformation:props": {"defaultActions": [{"type": "forward", "targetGroupArn": {"Ref": "ApiServiceTargetGroupF5D87875"}}], "loadBalancerArn": {"Ref": "ApiServiceLBA5369AC5"}, "port": 80, "protocol": "HTTP"}}}}}}}, "ApiServiceTargetGroup": {"id": "ApiServiceTargetGroup", "path": "JerseyColoursStack/ApiServiceTargetGroup", "constructInfo": {"fqn": "aws-cdk-lib.aws_elasticloadbalancingv2.ApplicationTargetGroup", "version": "2.204.0"}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/ApiServiceTargetGroup/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_elasticloadbalancingv2.CfnTargetGroup", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ElasticLoadBalancingV2::TargetGroup", "aws:cdk:cloudformation:props": {"port": 80, "protocol": "HTTP", "targetGroupAttributes": [{"key": "stickiness.enabled", "value": "false"}], "targetType": "ip", "vpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}}}}}, "ApiServiceTaskDef": {"id": "ApiServiceTaskDef", "path": "JerseyColoursStack/ApiServiceTaskDef", "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.FargateTaskDefinition", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/ApiServiceTaskDef/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.CfnTaskDefinition", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ECS::TaskDefinition", "aws:cdk:cloudformation:props": {"containerDefinitions": [{"essential": true, "image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["ApiRepositoryB8378B43", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["ApiRepositoryB8378B43", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "ApiRepositoryB8378B43"}, ":latest"]]}, "name": "web", "portMappings": [{"containerPort": 3000, "protocol": "tcp"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": {"Ref": "ApiServiceLogGroup42225A1C"}, "awslogs-stream-prefix": "jersey-colours-api", "awslogs-region": "ap-southeast-2"}}, "environment": [{"name": "S3_BUCKET", "value": {"Ref": "PatternFilesBucket4B88CA60"}}, {"name": "AWS_REGION", "value": "ap-southeast-2"}], "secrets": [{"name": "DATABASE_URL", "valueFrom": {"Fn::Join": ["", [{"Ref": "JerseyColoursDBSecretAttachmentD17E6A2D"}, ":engine::"]]}}]}], "cpu": "256", "executionRoleArn": {"Fn::GetAtt": ["TaskExecutionRole250D2532", "<PERSON><PERSON>"]}, "family": "JerseyColoursStackApiServiceTaskDef20A1B9CB", "memory": "512", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "taskRoleArn": {"Fn::GetAtt": ["ApiTaskRole12FAD4A7", "<PERSON><PERSON>"]}}}}, "web": {"id": "web", "path": "JerseyColoursStack/ApiServiceTaskDef/web", "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.ContainerDefinition", "version": "2.204.0"}}}}, "ApiServiceLogGroup": {"id": "ApiServiceLogGroup", "path": "JerseyColoursStack/ApiServiceLogGroup", "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.LogGroup", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/ApiServiceLogGroup/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.CfnLogGroup", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Logs::LogGroup", "aws:cdk:cloudformation:props": {"retentionInDays": 7}}}}}, "ApiService": {"id": "ApiService", "path": "JerseyColoursStack/ApiService", "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.FargateService", "version": "2.204.0", "metadata": []}, "children": {"Service": {"id": "Service", "path": "JerseyColoursStack/ApiService/Service", "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.CfnService", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ECS::Service", "aws:cdk:cloudformation:props": {"cluster": {"Ref": "JerseyColoursCluster9715902D"}, "deploymentConfiguration": {"maximumPercent": 200, "minimumHealthyPercent": 50, "alarms": {"alarmNames": [], "enable": false, "rollback": false}}, "desiredCount": 2, "enableEcsManagedTags": false, "healthCheckGracePeriodSeconds": 60, "launchType": "FARGATE", "loadBalancers": [{"targetGroupArn": {"Ref": "ApiServiceTargetGroupF5D87875"}, "containerName": "web", "containerPort": 3000}], "networkConfiguration": {"awsvpcConfiguration": {"assignPublicIp": "DISABLED", "subnets": [{"Ref": "JerseyColoursVPCPrivateSubnet1Subnet0DD79F72"}, {"Ref": "JerseyColoursVPCPrivateSubnet2Subnet5FC14D8F"}], "securityGroups": [{"Fn::GetAtt": ["ApiServiceSecurityGroupA2426F91", "GroupId"]}]}}, "taskDefinition": {"Ref": "ApiServiceTaskDef5552F312"}}}}, "SecurityGroup": {"id": "SecurityGroup", "path": "JerseyColoursStack/ApiService/SecurityGroup", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.SecurityGroup", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/ApiService/SecurityGroup/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSecurityGroup", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SecurityGroup", "aws:cdk:cloudformation:props": {"groupDescription": "JerseyColoursStack/ApiService/SecurityGroup", "securityGroupEgress": [{"cidrIp": "0.0.0.0/0", "description": "Allow all outbound traffic by default", "ipProtocol": "-1"}], "vpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}}}, "from JerseyColoursStackApiServiceLBSecurityGroup25818FD9:3000": {"id": "from JerseyColoursStackApiServiceLBSecurityGroup25818FD9:3000", "path": "JerseyColoursStack/ApiService/SecurityGroup/from JerseyColoursStackApiServiceLBSecurityGroup25818FD9:3000", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSecurityGroupIngress", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SecurityGroupIngress", "aws:cdk:cloudformation:props": {"description": "Load balancer to target", "fromPort": 3000, "groupId": {"Fn::GetAtt": ["ApiServiceSecurityGroupA2426F91", "GroupId"]}, "ipProtocol": "tcp", "sourceSecurityGroupId": {"Fn::GetAtt": ["ApiServiceLBSecurityGroup0ACC3F19", "GroupId"]}, "toPort": 3000}}}}}}}, "FrontendServiceLB": {"id": "FrontendServiceLB", "path": "JerseyColoursStack/FrontendServiceLB", "constructInfo": {"fqn": "aws-cdk-lib.aws_elasticloadbalancingv2.ApplicationLoadBalancer", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/FrontendServiceLB/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_elasticloadbalancingv2.CfnLoadBalancer", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ElasticLoadBalancingV2::LoadBalancer", "aws:cdk:cloudformation:props": {"loadBalancerAttributes": [{"key": "deletion_protection.enabled", "value": "false"}], "scheme": "internet-facing", "securityGroups": [{"Fn::GetAtt": ["FrontendServiceLBSecurityGroup18025BC2", "GroupId"]}], "subnets": [{"Ref": "JerseyColoursVPCPublicSubnet1Subnet723163B5"}, {"Ref": "JerseyColoursVPCPublicSubnet2Subnet6C002866"}], "type": "application"}}}, "SecurityGroup": {"id": "SecurityGroup", "path": "JerseyColoursStack/FrontendServiceLB/SecurityGroup", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.SecurityGroup", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/FrontendServiceLB/SecurityGroup/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSecurityGroup", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SecurityGroup", "aws:cdk:cloudformation:props": {"groupDescription": "Automatically created Security Group for ELB JerseyColoursStackFrontendServiceLB4698FFEB", "securityGroupIngress": [{"cidrIp": "0.0.0.0/0", "ipProtocol": "tcp", "fromPort": 80, "toPort": 80, "description": "Allow from anyone on port 80"}], "vpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}}}, "to JerseyColoursStackFrontendServiceSecurityGroupD365E684:80": {"id": "to JerseyColoursStackFrontendServiceSecurityGroupD365E684:80", "path": "JerseyColoursStack/FrontendServiceLB/SecurityGroup/to JerseyColoursStackFrontendServiceSecurityGroupD365E684:80", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSecurityGroupEgress", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SecurityGroupEgress", "aws:cdk:cloudformation:props": {"description": "Load balancer to target", "destinationSecurityGroupId": {"Fn::GetAtt": ["FrontendServiceSecurityGroup85470DEC", "GroupId"]}, "fromPort": 80, "groupId": {"Fn::GetAtt": ["FrontendServiceLBSecurityGroup18025BC2", "GroupId"]}, "ipProtocol": "tcp", "toPort": 80}}}}}, "FrontendServiceListener": {"id": "FrontendServiceListener", "path": "JerseyColoursStack/FrontendServiceLB/FrontendServiceListener", "constructInfo": {"fqn": "aws-cdk-lib.aws_elasticloadbalancingv2.ApplicationListener", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/FrontendServiceLB/FrontendServiceListener/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_elasticloadbalancingv2.CfnListener", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ElasticLoadBalancingV2::Listener", "aws:cdk:cloudformation:props": {"defaultActions": [{"type": "forward", "targetGroupArn": {"Ref": "FrontendServiceTargetGroup51F20C12"}}], "loadBalancerArn": {"Ref": "FrontendServiceLBB5764C7D"}, "port": 80, "protocol": "HTTP"}}}}}}}, "FrontendServiceTargetGroup": {"id": "FrontendServiceTargetGroup", "path": "JerseyColoursStack/FrontendServiceTargetGroup", "constructInfo": {"fqn": "aws-cdk-lib.aws_elasticloadbalancingv2.ApplicationTargetGroup", "version": "2.204.0"}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/FrontendServiceTargetGroup/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_elasticloadbalancingv2.CfnTargetGroup", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ElasticLoadBalancingV2::TargetGroup", "aws:cdk:cloudformation:props": {"port": 80, "protocol": "HTTP", "targetGroupAttributes": [{"key": "stickiness.enabled", "value": "false"}], "targetType": "ip", "vpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}}}}}, "FrontendServiceTaskDef": {"id": "FrontendServiceTaskDef", "path": "JerseyColoursStack/FrontendServiceTaskDef", "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.FargateTaskDefinition", "version": "2.204.0", "metadata": []}, "children": {"TaskRole": {"id": "TaskRole", "path": "JerseyColoursStack/FrontendServiceTaskDef/TaskRole", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.204.0", "metadata": []}, "children": {"ImportTaskRole": {"id": "ImportTaskRole", "path": "JerseyColoursStack/FrontendServiceTaskDef/TaskRole/ImportTaskRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.204.0", "metadata": []}}, "Resource": {"id": "Resource", "path": "JerseyColoursStack/FrontendServiceTaskDef/TaskRole/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "ecs-tasks.amazonaws.com"}}], "Version": "2012-10-17"}}}}}}, "Resource": {"id": "Resource", "path": "JerseyColoursStack/FrontendServiceTaskDef/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.CfnTaskDefinition", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ECS::TaskDefinition", "aws:cdk:cloudformation:props": {"containerDefinitions": [{"essential": true, "image": {"Fn::Join": ["", [{"Fn::Select": [4, {"Fn::Split": [":", {"Fn::GetAtt": ["FrontendRepository7D714FA2", "<PERSON><PERSON>"]}]}]}, ".dkr.ecr.", {"Fn::Select": [3, {"Fn::Split": [":", {"Fn::GetAtt": ["FrontendRepository7D714FA2", "<PERSON><PERSON>"]}]}]}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "FrontendRepository7D714FA2"}, ":latest"]]}, "name": "web", "portMappings": [{"containerPort": 80, "protocol": "tcp"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": {"Ref": "FrontendServiceLogGroup99775B35"}, "awslogs-stream-prefix": "jersey-colours-frontend", "awslogs-region": "ap-southeast-2"}}, "environment": [{"name": "API_URL", "value": {"Fn::Join": ["", ["http://", {"Fn::GetAtt": ["ApiServiceLBA5369AC5", "DNSName"]}]]}}]}], "cpu": "256", "executionRoleArn": {"Fn::GetAtt": ["TaskExecutionRole250D2532", "<PERSON><PERSON>"]}, "family": "JerseyColoursStackFrontendServiceTaskDefF87F9B84", "memory": "256", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "taskRoleArn": {"Fn::GetAtt": ["FrontendServiceTaskDefTaskRoleBB6B2323", "<PERSON><PERSON>"]}}}}, "web": {"id": "web", "path": "JerseyColoursStack/FrontendServiceTaskDef/web", "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.ContainerDefinition", "version": "2.204.0"}}}}, "FrontendServiceLogGroup": {"id": "FrontendServiceLogGroup", "path": "JerseyColoursStack/FrontendServiceLogGroup", "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.LogGroup", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/FrontendServiceLogGroup/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_logs.CfnLogGroup", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Logs::LogGroup", "aws:cdk:cloudformation:props": {"retentionInDays": 7}}}}}, "FrontendService": {"id": "FrontendService", "path": "JerseyColoursStack/FrontendService", "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.FargateService", "version": "2.204.0", "metadata": []}, "children": {"Service": {"id": "Service", "path": "JerseyColoursStack/FrontendService/Service", "constructInfo": {"fqn": "aws-cdk-lib.aws_ecs.CfnService", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ECS::Service", "aws:cdk:cloudformation:props": {"cluster": {"Ref": "JerseyColoursCluster9715902D"}, "deploymentConfiguration": {"maximumPercent": 200, "minimumHealthyPercent": 50, "alarms": {"alarmNames": [], "enable": false, "rollback": false}}, "desiredCount": 2, "enableEcsManagedTags": false, "healthCheckGracePeriodSeconds": 60, "launchType": "FARGATE", "loadBalancers": [{"targetGroupArn": {"Ref": "FrontendServiceTargetGroup51F20C12"}, "containerName": "web", "containerPort": 80}], "networkConfiguration": {"awsvpcConfiguration": {"assignPublicIp": "DISABLED", "subnets": [{"Ref": "JerseyColoursVPCPrivateSubnet1Subnet0DD79F72"}, {"Ref": "JerseyColoursVPCPrivateSubnet2Subnet5FC14D8F"}], "securityGroups": [{"Fn::GetAtt": ["FrontendServiceSecurityGroup85470DEC", "GroupId"]}]}}, "taskDefinition": {"Ref": "FrontendServiceTaskDef06639368"}}}}, "SecurityGroup": {"id": "SecurityGroup", "path": "JerseyColoursStack/FrontendService/SecurityGroup", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.SecurityGroup", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/FrontendService/SecurityGroup/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSecurityGroup", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SecurityGroup", "aws:cdk:cloudformation:props": {"groupDescription": "JerseyColoursStack/FrontendService/SecurityGroup", "securityGroupEgress": [{"cidrIp": "0.0.0.0/0", "description": "Allow all outbound traffic by default", "ipProtocol": "-1"}], "vpcId": {"Ref": "JerseyColoursVPCFE3584A6"}}}}, "from JerseyColoursStackFrontendServiceLBSecurityGroupE8599451:80": {"id": "from JerseyColoursStackFrontendServiceLBSecurityGroupE8599451:80", "path": "JerseyColoursStack/FrontendService/SecurityGroup/from JerseyColoursStackFrontendServiceLBSecurityGroupE8599451:80", "constructInfo": {"fqn": "aws-cdk-lib.aws_ec2.CfnSecurityGroupIngress", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::EC2::SecurityGroupIngress", "aws:cdk:cloudformation:props": {"description": "Load balancer to target", "fromPort": 80, "groupId": {"Fn::GetAtt": ["FrontendServiceSecurityGroup85470DEC", "GroupId"]}, "ipProtocol": "tcp", "sourceSecurityGroupId": {"Fn::GetAtt": ["FrontendServiceLBSecurityGroup18025BC2", "GroupId"]}, "toPort": 80}}}}}}}, "SVGGeneratorFunction": {"id": "SVGGeneratorFunction", "path": "JerseyColoursStack/SVGGeneratorFunction", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.Function", "version": "2.204.0", "metadata": []}, "children": {"ServiceRole": {"id": "ServiceRole", "path": "JerseyColoursStack/SVGGeneratorFunction/ServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.204.0", "metadata": []}, "children": {"ImportServiceRole": {"id": "ImportServiceRole", "path": "JerseyColoursStack/SVGGeneratorFunction/ServiceRole/ImportServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.204.0", "metadata": []}}, "Resource": {"id": "Resource", "path": "JerseyColoursStack/SVGGeneratorFunction/ServiceRole/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "managedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}]}}}}}, "Code": {"id": "Code", "path": "JerseyColoursStack/SVGGeneratorFunction/Code", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.204.0"}, "children": {"Stage": {"id": "Stage", "path": "JerseyColoursStack/SVGGeneratorFunction/Code/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.204.0"}}, "AssetBucket": {"id": "AssetBucket", "path": "JerseyColoursStack/SVGGeneratorFunction/Code/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.204.0", "metadata": []}}}}, "Resource": {"id": "Resource", "path": "JerseyColoursStack/SVGGeneratorFunction/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnFunction", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Function", "aws:cdk:cloudformation:props": {"code": {"s3Bucket": "cdk-hnb659fds-assets-699965966019-ap-southeast-2", "s3Key": "2da74cf8648f42eef5a108fc71819bd6093a4f75aa67e03c1b813d2c20db3fba.zip"}, "environment": {"variables": {"NODE_ENV": "production"}}, "handler": "index.handler", "memorySize": 512, "role": {"Fn::GetAtt": ["SVGGeneratorFunctionServiceRoleF2DA643E", "<PERSON><PERSON>"]}, "runtime": "nodejs20.x", "timeout": 30}}}}}, "SVGGeneratorAPI": {"id": "SVGGeneratorAPI", "path": "JerseyColoursStack/SVGGeneratorAPI", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.RestApi", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/SVGGeneratorAPI/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnRestApi", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::RestApi", "aws:cdk:cloudformation:props": {"description": "API for generating jersey SVGs", "name": "Jersey Colours SVG Generator"}}}, "Deployment": {"id": "Deployment", "path": "JerseyColoursStack/SVGGeneratorAPI/Deployment", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Deployment", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/SVGGeneratorAPI/Deployment/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnDeployment", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Deployment", "aws:cdk:cloudformation:props": {"description": "API for generating jersey SVGs", "restApiId": {"Ref": "SVGGeneratorAPIF54FD110"}}}}}}, "DeploymentStage.prod": {"id": "DeploymentStage.prod", "path": "JerseyColoursStack/SVGGeneratorAPI/DeploymentStage.prod", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Stage", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/SVGGeneratorAPI/DeploymentStage.prod/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnStage", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Stage", "aws:cdk:cloudformation:props": {"deploymentId": {"Ref": "SVGGeneratorAPIDeployment8D93C6A655a93079e01ac4e5b139c19b275d33bf"}, "restApiId": {"Ref": "SVGGeneratorAPIF54FD110"}, "stageName": "prod"}}}}}, "Endpoint": {"id": "Endpoint", "path": "JerseyColoursStack/SVGGeneratorAPI/Endpoint", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.204.0"}}, "Default": {"id": "<PERSON><PERSON><PERSON>", "path": "JerseyColoursStack/SVGGeneratorAPI/Default", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.ResourceBase", "version": "2.204.0", "metadata": []}, "children": {"OPTIONS": {"id": "OPTIONS", "path": "JerseyColoursStack/SVGGeneratorAPI/Default/OPTIONS", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/SVGGeneratorAPI/Default/OPTIONS/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": false, "authorizationType": "NONE", "httpMethod": "OPTIONS", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{ statusCode: 200 }"}, "integrationResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,Authorization'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}}]}, "methodResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}}], "resourceId": {"Fn::GetAtt": ["SVGGeneratorAPIF54FD110", "RootResourceId"]}, "restApiId": {"Ref": "SVGGeneratorAPIF54FD110"}}}}}}, "generate": {"id": "generate", "path": "JerseyColoursStack/SVGGeneratorAPI/Default/generate", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Resource", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/SVGGeneratorAPI/Default/generate/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnResource", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Resource", "aws:cdk:cloudformation:props": {"parentId": {"Fn::GetAtt": ["SVGGeneratorAPIF54FD110", "RootResourceId"]}, "pathPart": "generate", "restApiId": {"Ref": "SVGGeneratorAPIF54FD110"}}}}, "OPTIONS": {"id": "OPTIONS", "path": "JerseyColoursStack/SVGGeneratorAPI/Default/generate/OPTIONS", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/SVGGeneratorAPI/Default/generate/OPTIONS/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"apiKeyRequired": false, "authorizationType": "NONE", "httpMethod": "OPTIONS", "integration": {"type": "MOCK", "requestTemplates": {"application/json": "{ statusCode: 200 }"}, "integrationResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": "'Content-Type,Authorization'", "method.response.header.Access-Control-Allow-Origin": "'*'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET,PUT,POST,DELETE,PATCH,HEAD'"}}]}, "methodResponses": [{"statusCode": "204", "responseParameters": {"method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Allow-Methods": true}}], "resourceId": {"Ref": "SVGGeneratorAPIgenerateF65226DF"}, "restApiId": {"Ref": "SVGGeneratorAPIF54FD110"}}}}}}, "POST": {"id": "POST", "path": "JerseyColoursStack/SVGGeneratorAPI/Default/generate/POST", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.Method", "version": "2.204.0", "metadata": []}, "children": {"ApiPermission.JerseyColoursStackSVGGeneratorAPIA9C7E49B.POST..generate": {"id": "ApiPermission.JerseyColoursStackSVGGeneratorAPIA9C7E49B.POST..generate", "path": "JerseyColoursStack/SVGGeneratorAPI/Default/generate/POST/ApiPermission.JerseyColoursStackSVGGeneratorAPIA9C7E49B.POST..generate", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["SVGGeneratorFunction1FB593CD", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:ap-southeast-2:699965966019:", {"Ref": "SVGGeneratorAPIF54FD110"}, "/", {"Ref": "SVGGeneratorAPIDeploymentStageprod32256403"}, "/POST/generate"]]}}}}, "ApiPermission.Test.JerseyColoursStackSVGGeneratorAPIA9C7E49B.POST..generate": {"id": "ApiPermission.Test.JerseyColoursStackSVGGeneratorAPIA9C7E49B.POST..generate", "path": "JerseyColoursStack/SVGGeneratorAPI/Default/generate/POST/ApiPermission.Test.JerseyColoursStackSVGGeneratorAPIA9C7E49B.POST..generate", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnPermission", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Permission", "aws:cdk:cloudformation:props": {"action": "lambda:InvokeFunction", "functionName": {"Fn::GetAtt": ["SVGGeneratorFunction1FB593CD", "<PERSON><PERSON>"]}, "principal": "apigateway.amazonaws.com", "sourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:ap-southeast-2:699965966019:", {"Ref": "SVGGeneratorAPIF54FD110"}, "/test-invoke-stage/POST/generate"]]}}}}, "Resource": {"id": "Resource", "path": "JerseyColoursStack/SVGGeneratorAPI/Default/generate/POST/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_apigateway.CfnMethod", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::ApiGateway::Method", "aws:cdk:cloudformation:props": {"authorizationType": "NONE", "httpMethod": "POST", "integration": {"type": "AWS_PROXY", "uri": {"Fn::Join": ["", ["arn:aws:apigateway:ap-southeast-2:lambda:path/2015-03-31/functions/", {"Fn::GetAtt": ["SVGGeneratorFunction1FB593CD", "<PERSON><PERSON>"]}, "/invocations"]]}, "integrationHttpMethod": "POST"}, "resourceId": {"Ref": "SVGGeneratorAPIgenerateF65226DF"}, "restApiId": {"Ref": "SVGGeneratorAPIF54FD110"}}}}}}}}}}}}, "JerseyColoursDistribution": {"id": "JerseyColoursDistribution", "path": "JerseyColoursStack/JerseyColoursDistribution", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.Distribution", "version": "2.204.0", "metadata": []}, "children": {"Origin1": {"id": "Origin1", "path": "JerseyColoursStack/JerseyColoursDistribution/Origin1", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "Origin2": {"id": "Origin2", "path": "JerseyColoursStack/JerseyColoursDistribution/Origin2", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "Origin3": {"id": "Origin3", "path": "JerseyColoursStack/JerseyColoursDistribution/Origin3", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "Origin4": {"id": "Origin4", "path": "JerseyColoursStack/JerseyColoursDistribution/Origin4", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}, "children": {"S3Origin": {"id": "S3Origin", "path": "JerseyColoursStack/JerseyColoursDistribution/Origin4/S3Origin", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.OriginAccessIdentity", "version": "2.204.0", "metadata": []}, "children": {"Resource": {"id": "Resource", "path": "JerseyColoursStack/JerseyColoursDistribution/Origin4/S3Origin/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CfnCloudFrontOriginAccessIdentity", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFront::CloudFrontOriginAccessIdentity", "aws:cdk:cloudformation:props": {"cloudFrontOriginAccessIdentityConfig": {"comment": "Identity for JerseyColoursStackJerseyColoursDistributionOrigin45280CFB2"}}}}}}}}, "Resource": {"id": "Resource", "path": "JerseyColoursStack/JerseyColoursDistribution/Resource", "constructInfo": {"fqn": "aws-cdk-lib.aws_cloudfront.CfnDistribution", "version": "2.204.0"}, "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFront::Distribution", "aws:cdk:cloudformation:props": {"distributionConfig": {"enabled": true, "origins": [{"domainName": {"Fn::GetAtt": ["FrontendServiceLBB5764C7D", "DNSName"]}, "id": "JerseyColoursStackJerseyColoursDistributionOrigin1C9CF29C7", "customOriginConfig": {"originSslProtocols": ["TLSv1.2"], "originProtocolPolicy": "http-only"}}, {"domainName": {"Fn::GetAtt": ["ApiServiceLBA5369AC5", "DNSName"]}, "id": "JerseyColoursStackJerseyColoursDistributionOrigin2FFE74F6B", "customOriginConfig": {"originSslProtocols": ["TLSv1.2"], "originProtocolPolicy": "http-only"}}, {"domainName": {"Fn::Select": [2, {"Fn::Split": ["/", {"Fn::Join": ["", ["https://", {"Ref": "SVGGeneratorAPIF54FD110"}, ".execute-api.ap-southeast-2.", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "SVGGeneratorAPIDeploymentStageprod32256403"}, "/"]]}]}]}, "id": "JerseyColoursStackJerseyColoursDistributionOrigin34078A2E8", "originPath": {"Fn::Join": ["", ["/", {"Fn::Select": [3, {"Fn::Split": ["/", {"Fn::Join": ["", ["https://", {"Ref": "SVGGeneratorAPIF54FD110"}, ".execute-api.ap-southeast-2.", {"Ref": "AWS::URLSuffix"}, "/", {"Ref": "SVGGeneratorAPIDeploymentStageprod32256403"}, "/"]]}]}]}]]}, "customOriginConfig": {"originSslProtocols": ["TLSv1.2"], "originProtocolPolicy": "https-only"}}, {"domainName": {"Fn::GetAtt": ["PatternFilesBucket4B88CA60", "RegionalDomainName"]}, "id": "JerseyColoursStackJerseyColoursDistributionOrigin45280CFB2", "s3OriginConfig": {"originAccessIdentity": {"Fn::Join": ["", ["origin-access-identity/cloudfront/", {"Ref": "JerseyColoursDistributionOrigin4S3Origin35EAB48A"}]]}}}], "defaultCacheBehavior": {"pathPattern": "*", "targetOriginId": "JerseyColoursStackJerseyColoursDistributionOrigin1C9CF29C7", "cachePolicyId": "658327ea-f89d-4fab-a63d-7e88639e58f6", "compress": true, "viewerProtocolPolicy": "redirect-to-https"}, "cacheBehaviors": [{"pathPattern": "/api/*", "targetOriginId": "JerseyColoursStackJerseyColoursDistributionOrigin2FFE74F6B", "allowedMethods": ["GET", "HEAD", "OPTIONS", "PUT", "PATCH", "POST", "DELETE"], "cachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad", "compress": true, "viewerProtocolPolicy": "redirect-to-https"}, {"pathPattern": "/svg/*", "targetOriginId": "JerseyColoursStackJerseyColoursDistributionOrigin34078A2E8", "cachePolicyId": "658327ea-f89d-4fab-a63d-7e88639e58f6", "compress": true, "viewerProtocolPolicy": "redirect-to-https"}, {"pathPattern": "/patterns/*", "targetOriginId": "JerseyColoursStackJerseyColoursDistributionOrigin45280CFB2", "cachePolicyId": "658327ea-f89d-4fab-a63d-7e88639e58f6", "compress": true, "viewerProtocolPolicy": "redirect-to-https"}], "httpVersion": "http2", "ipv6Enabled": true, "priceClass": "PriceClass_100"}}}}}}, "DatabaseEndpoint": {"id": "DatabaseEndpoint", "path": "JerseyColoursStack/DatabaseEndpoint", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.204.0"}}, "ApiLoadBalancerDNS": {"id": "ApiLoadBalancerDNS", "path": "JerseyColoursStack/ApiLoadBalancerDNS", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.204.0"}}, "FrontendLoadBalancerDNS": {"id": "FrontendLoadBalancerDNS", "path": "JerseyColoursStack/FrontendLoadBalancerDNS", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.204.0"}}, "CloudFrontDomainName": {"id": "CloudFrontDomainName", "path": "JerseyColoursStack/CloudFrontDomainName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.204.0"}}, "S3BucketName": {"id": "S3BucketName", "path": "JerseyColoursStack/S3BucketName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.204.0"}}, "SVGGeneratorAPIEndpoint": {"id": "SVGGeneratorAPIEndpoint", "path": "JerseyColoursStack/SVGGeneratorAPIEndpoint", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.204.0"}}, "ApiRepositoryURI": {"id": "ApiRepositoryURI", "path": "JerseyColoursStack/ApiRepositoryURI", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.204.0"}}, "FrontendRepositoryURI": {"id": "FrontendRepositoryURI", "path": "JerseyColoursStack/FrontendRepositoryURI", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.204.0"}}, "CDKMetadata": {"id": "CDKMetadata", "path": "JerseyColoursStack/CDKMetadata", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}, "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "JerseyColoursStack/CDKMetadata/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.204.0"}}}}, "BootstrapVersion": {"id": "BootstrapVersion", "path": "JerseyColoursStack/BootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.204.0"}}, "CheckBootstrapVersion": {"id": "CheckBootstrapVersion", "path": "JerseyColoursStack/CheckBootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnRule", "version": "2.204.0"}}}}, "Tree": {"id": "Tree", "path": "Tree", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}}}}